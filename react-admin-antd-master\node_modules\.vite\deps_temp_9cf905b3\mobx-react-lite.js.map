{"version": 3, "sources": ["../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/utils/assertEnvironment.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/utils/reactBatchedUpdates.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/utils/observerBatching.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/utils/utils.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/useObserver.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/utils/printDebugValue.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/staticRendering.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/utils/UniversalFinalizationRegistry.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/utils/observerFinalizationRegistry.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/observer.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/ObserverComponent.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/useLocalObservable.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/useLocalStore.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/useAsObservableSource.ts", "../../.pnpm/mobx-react-lite@4.1.0_mobx@_023e66bd92f15312098b7b5fc44919a9/node_modules/mobx-react-lite/src/index.ts"], "sourcesContent": ["import { makeObservable } from \"mobx\"\nimport { useState } from \"react\"\n\nif (!useState) {\n    throw new Error(\"mobx-react-lite requires React with Hooks support\")\n}\nif (!makeObservable) {\n    throw new Error(\"mobx-react-lite@3 requires mobx at least version 6 to be available\")\n}\n", "export { unstable_batchedUpdates } from \"react-dom\"\n", "import { configure } from \"mobx\"\n\nexport function defaultNoopBatch(callback: () => void) {\n    callback()\n}\n\nexport function observerBatching(reactionScheduler: any) {\n    if (!reactionScheduler) {\n        reactionScheduler = defaultNoopBatch\n        if (\"production\" !== process.env.NODE_ENV) {\n            console.warn(\n                \"[MobX] Failed to get unstable_batched updates from react-dom / react-native\"\n            )\n        }\n    }\n    configure({ reactionScheduler })\n}\n\nexport const isObserverBatched = () => {\n    if (\"production\" !== process.env.NODE_ENV) {\n        console.warn(\"[MobX] Deprecated\")\n    }\n\n    return true\n}\n", "const deprecatedMessages: string[] = []\n\nexport function useDeprecated(msg: string) {\n    if (!deprecatedMessages.includes(msg)) {\n        deprecatedMessages.push(msg)\n        console.warn(msg)\n    }\n}\n", "import { Reaction } from \"mobx\"\nimport React from \"react\"\nimport { printDebugValue } from \"./utils/printDebugValue\"\nimport { isUsingStaticRendering } from \"./staticRendering\"\nimport { observerFinalizationRegistry } from \"./utils/observerFinalizationRegistry\"\nimport { useSyncExternalStore } from \"use-sync-external-store/shim\"\n\n// Do not store `admRef` (even as part of a closure!) on this object,\n// otherwise it will prevent GC and therefore reaction disposal via FinalizationRegistry.\ntype ObserverAdministration = {\n    reaction: Reaction | null // also serves as disposed flag\n    onStoreChange: Function | null // also serves as mounted flag\n    // stateVersion that 'ticks' for every time the reaction fires\n    // tearing is still present,\n    // because there is no cross component synchronization,\n    // but we can use `useSyncExternalStore` API.\n    // TODO: optimize to use number?\n    stateVersion: any\n    name: string\n    // These don't depend on state/props, therefore we can keep them here instead of `useCallback`\n    subscribe: Parameters<typeof React.useSyncExternalStore>[0]\n    getSnapshot: Parameters<typeof React.useSyncExternalStore>[1]\n}\n\nfunction createReaction(adm: ObserverAdministration) {\n    adm.reaction = new Reaction(`observer${adm.name}`, () => {\n        adm.stateVersion = Symbol()\n        // onStoreChange won't be available until the component \"mounts\".\n        // If state changes in between initial render and mount,\n        // `useSyncExternalStore` should handle that by checking the state version and issuing update.\n        adm.onStoreChange?.()\n    })\n}\n\nexport function useObserver<T>(render: () => T, baseComponentName: string = \"observed\"): T {\n    if (isUsingStaticRendering()) {\n        return render()\n    }\n\n    const admRef = React.useRef<ObserverAdministration | null>(null)\n\n    if (!admRef.current) {\n        // First render\n        const adm: ObserverAdministration = {\n            reaction: null,\n            onStoreChange: null,\n            stateVersion: Symbol(),\n            name: baseComponentName,\n            subscribe(onStoreChange: () => void) {\n                // Do NOT access admRef here!\n                observerFinalizationRegistry.unregister(adm)\n                adm.onStoreChange = onStoreChange\n                if (!adm.reaction) {\n                    // We've lost our reaction and therefore all subscriptions, occurs when:\n                    // 1. Timer based finalization registry disposed reaction before component mounted.\n                    // 2. React \"re-mounts\" same component without calling render in between (typically <StrictMode>).\n                    // We have to recreate reaction and schedule re-render to recreate subscriptions,\n                    // even if state did not change.\n                    createReaction(adm)\n                    // `onStoreChange` won't force update if subsequent `getSnapshot` returns same value.\n                    // So we make sure that is not the case\n                    adm.stateVersion = Symbol()\n                }\n\n                return () => {\n                    // Do NOT access admRef here!\n                    adm.onStoreChange = null\n                    adm.reaction?.dispose()\n                    adm.reaction = null\n                }\n            },\n            getSnapshot() {\n                // Do NOT access admRef here!\n                return adm.stateVersion\n            }\n        }\n\n        admRef.current = adm\n    }\n\n    const adm = admRef.current!\n\n    if (!adm.reaction) {\n        // First render or reaction was disposed by registry before subscribe\n        createReaction(adm)\n        // StrictMode/ConcurrentMode/Suspense may mean that our component is\n        // rendered and abandoned multiple times, so we need to track leaked\n        // Reactions.\n        observerFinalizationRegistry.register(admRef, adm, adm)\n    }\n\n    React.useDebugValue(adm.reaction!, printDebugValue)\n\n    useSyncExternalStore(\n        // Both of these must be stable, otherwise it would keep resubscribing every render.\n        adm.subscribe,\n        adm.getSnapshot,\n        adm.getSnapshot\n    )\n\n    // render the original component, but have the\n    // reaction track the observables, so that rendering\n    // can be invalidated (see above) once a dependency changes\n    let renderResult!: T\n    let exception\n    adm.reaction!.track(() => {\n        try {\n            renderResult = render()\n        } catch (e) {\n            exception = e\n        }\n    })\n\n    if (exception) {\n        throw exception // re-throw any exceptions caught during rendering\n    }\n\n    return renderResult\n}\n", "import { getDependencyTree, Reaction } from \"mobx\"\n\nexport function printDebugValue(v: Reaction) {\n    return getDependencyTree(v)\n}\n", "let globalIsUsingStaticRendering = false\n\nexport function enableStaticRendering(enable: boolean) {\n    globalIsUsingStaticRendering = enable\n}\n\nexport function isUsingStaticRendering(): boolean {\n    return globalIsUsingStaticRendering\n}\n", "export declare class FinalizationRegistryType<T> {\n    constructor(finalize: (value: T) => void)\n    register(target: object, value: T, token?: object): void\n    unregister(token: object): void\n}\n\ndeclare const FinalizationRegistry: typeof FinalizationRegistryType | undefined\n\nexport const REGISTRY_FINALIZE_AFTER = 10_000\nexport const REGISTRY_SWEEP_INTERVAL = 10_000\n\nexport class TimerBasedFinalizationRegistry<T> implements FinalizationRegistryType<T> {\n    private registrations: Map<unknown, { value: T; registeredAt: number }> = new Map()\n    private sweepTimeout: ReturnType<typeof setTimeout> | undefined\n\n    constructor(private readonly finalize: (value: T) => void) {}\n\n    // Token is actually required with this impl\n    register(target: object, value: T, token?: object) {\n        this.registrations.set(token, {\n            value,\n            registeredAt: Date.now()\n        })\n        this.scheduleSweep()\n    }\n\n    unregister(token: unknown) {\n        this.registrations.delete(token)\n    }\n\n    // Bound so it can be used directly as setTimeout callback.\n    sweep = (maxAge = REGISTRY_FINALIZE_AFTER) => {\n        // cancel timeout so we can force sweep anytime\n        clearTimeout(this.sweepTimeout)\n        this.sweepTimeout = undefined\n\n        const now = Date.now()\n        this.registrations.forEach((registration, token) => {\n            if (now - registration.registeredAt >= maxAge) {\n                this.finalize(registration.value)\n                this.registrations.delete(token)\n            }\n        })\n\n        if (this.registrations.size > 0) {\n            this.scheduleSweep()\n        }\n    }\n\n    // Bound so it can be exported directly as clearTimers test utility.\n    finalizeAllImmediately = () => {\n        this.sweep(0)\n    }\n\n    private scheduleSweep() {\n        if (this.sweepTimeout === undefined) {\n            this.sweepTimeout = setTimeout(this.sweep, REGISTRY_SWEEP_INTERVAL)\n        }\n    }\n}\n\nexport const UniversalFinalizationRegistry =\n    typeof FinalizationRegistry !== \"undefined\"\n        ? FinalizationRegistry\n        : TimerBasedFinalizationRegistry\n", "import { Reaction } from \"mobx\"\nimport { UniversalFinalizationRegistry } from \"./UniversalFinalizationRegistry\"\n\nexport const observerFinalizationRegistry = new UniversalFinalizationRegistry(\n    (adm: { reaction: Reaction | null }) => {\n        adm.reaction?.dispose()\n        adm.reaction = null\n    }\n)\n", "import { forwardRef, memo } from \"react\"\n\nimport { isUsingStaticRendering } from \"./staticRendering\"\nimport { useObserver } from \"./useObserver\"\n\nlet warnObserverOptionsDeprecated = true\nlet warnLegacyContextTypes = true\n\nconst hasSymbol = typeof Symbol === \"function\" && Symbol.for\nconst isFunctionNameConfigurable =\n    Object.getOwnPropertyDescriptor(() => {}, \"name\")?.configurable ?? false\n\n// Using react-is had some issues (and operates on elements, not on types), see #608 / #609\nconst ReactForwardRefSymbol = hasSymbol\n    ? Symbol.for(\"react.forward_ref\")\n    : typeof forwardRef === \"function\" && forwardRef((props: any) => null)[\"$$typeof\"]\n\nconst ReactMemoSymbol = hasSymbol\n    ? Symbol.for(\"react.memo\")\n    : typeof memo === \"function\" && memo((props: any) => null)[\"$$typeof\"]\n\n/**\n * @deprecated Observer options will be removed in the next major version of mobx-react-lite.\n * Look at the individual properties for alternatives.\n */\nexport interface IObserverOptions {\n    /**\n     * @deprecated Pass a `React.forwardRef` component to observer instead of using the options object\n     * e.g. `observer(React.forwardRef(fn))`\n     */\n    readonly forwardRef?: boolean\n}\n\nexport function observer<P extends object, TRef = {}>(\n    baseComponent: React.ForwardRefRenderFunction<TRef, P>,\n    options: IObserverOptions & {\n        /**\n         * @deprecated Pass a `React.forwardRef` component to observer instead of using the options object\n         * e.g. `observer(React.forwardRef(fn))`\n         */\n        forwardRef: true\n    }\n): React.MemoExoticComponent<\n    React.ForwardRefExoticComponent<React.PropsWithoutRef<P> & React.RefAttributes<TRef>>\n>\n\nexport function observer<P extends object, TRef = {}>(\n    baseComponent: React.ForwardRefExoticComponent<\n        React.PropsWithoutRef<P> & React.RefAttributes<TRef>\n    >\n): React.MemoExoticComponent<\n    React.ForwardRefExoticComponent<React.PropsWithoutRef<P> & React.RefAttributes<TRef>>\n>\n\nexport function observer<P extends object>(\n    baseComponent: React.FunctionComponent<P>,\n    options?: IObserverOptions\n): React.FunctionComponent<P>\n\nexport function observer<\n    C extends React.FunctionComponent<any> | React.ForwardRefRenderFunction<any>,\n    Options extends IObserverOptions\n>(\n    baseComponent: C,\n    options?: Options\n): Options extends { forwardRef: true }\n    ? C extends React.ForwardRefRenderFunction<infer TRef, infer P>\n        ? C &\n              React.MemoExoticComponent<\n                  React.ForwardRefExoticComponent<\n                      React.PropsWithoutRef<P> & React.RefAttributes<TRef>\n                  >\n              >\n        : never /* forwardRef set for a non forwarding component */\n    : C & { displayName: string }\n\n// n.b. base case is not used for actual typings or exported in the typing files\nexport function observer<P extends object, TRef = {}>(\n    baseComponent:\n        | React.ForwardRefRenderFunction<TRef, P>\n        | React.FunctionComponent<P>\n        | React.ForwardRefExoticComponent<React.PropsWithoutRef<P> & React.RefAttributes<TRef>>,\n    // TODO remove in next major\n    options?: IObserverOptions\n) {\n    if (process.env.NODE_ENV !== \"production\" && warnObserverOptionsDeprecated && options) {\n        warnObserverOptionsDeprecated = false\n        console.warn(\n            `[mobx-react-lite] \\`observer(fn, { forwardRef: true })\\` is deprecated, use \\`observer(React.forwardRef(fn))\\``\n        )\n    }\n\n    if (ReactMemoSymbol && baseComponent[\"$$typeof\"] === ReactMemoSymbol) {\n        throw new Error(\n            `[mobx-react-lite] You are trying to use \\`observer\\` on a function component wrapped in either another \\`observer\\` or \\`React.memo\\`. The observer already applies 'React.memo' for you.`\n        )\n    }\n\n    // The working of observer is explained step by step in this talk: https://www.youtube.com/watch?v=cPF4iBedoF0&feature=youtu.be&t=1307\n    if (isUsingStaticRendering()) {\n        return baseComponent\n    }\n\n    let useForwardRef = options?.forwardRef ?? false\n    let render = baseComponent\n\n    const baseComponentName = baseComponent.displayName || baseComponent.name\n\n    // If already wrapped with forwardRef, unwrap,\n    // so we can patch render and apply memo\n    if (ReactForwardRefSymbol && baseComponent[\"$$typeof\"] === ReactForwardRefSymbol) {\n        useForwardRef = true\n        render = baseComponent[\"render\"]\n        if (typeof render !== \"function\") {\n            throw new Error(\n                `[mobx-react-lite] \\`render\\` property of ForwardRef was not a function`\n            )\n        }\n    }\n\n    let observerComponent = (props: any, ref: React.Ref<TRef>) => {\n        return useObserver(() => render(props, ref), baseComponentName)\n    }\n\n    // Inherit original name and displayName, see #3438\n    ;(observerComponent as React.FunctionComponent).displayName = baseComponent.displayName\n\n    if (isFunctionNameConfigurable) {\n        Object.defineProperty(observerComponent, \"name\", {\n            value: baseComponent.name,\n            writable: true,\n            configurable: true\n        })\n    }\n\n    // Support legacy context: `contextTypes` must be applied before `memo`\n    if ((baseComponent as any).contextTypes) {\n        ;(observerComponent as React.FunctionComponent).contextTypes = (\n            baseComponent as any\n        ).contextTypes\n\n        if (process.env.NODE_ENV !== \"production\" && warnLegacyContextTypes) {\n            warnLegacyContextTypes = false\n            console.warn(\n                `[mobx-react-lite] Support for Legacy Context in function components will be removed in the next major release.`\n            )\n        }\n    }\n\n    if (useForwardRef) {\n        // `forwardRef` must be applied prior `memo`\n        // `forwardRef(observer(cmp))` throws:\n        // \"forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))\"\n        observerComponent = forwardRef(observerComponent)\n    }\n\n    // memo; we are not interested in deep updates\n    // in props; we assume that if deep objects are changed,\n    // this is in observables, which would have been tracked anyway\n    observerComponent = memo(observerComponent)\n\n    copyStaticProperties(baseComponent, observerComponent)\n\n    if (\"production\" !== process.env.NODE_ENV) {\n        Object.defineProperty(observerComponent, \"contextTypes\", {\n            set() {\n                throw new Error(\n                    `[mobx-react-lite] \\`${\n                        this.displayName || this.type?.displayName || this.type?.name || \"Component\"\n                    }.contextTypes\\` must be set before applying \\`observer\\`.`\n                )\n            }\n        })\n    }\n\n    return observerComponent\n}\n\n// based on https://github.com/mridgway/hoist-non-react-statics/blob/master/src/index.js\nconst hoistBlackList: any = {\n    $$typeof: true,\n    render: true,\n    compare: true,\n    type: true,\n    // Don't redefine `displayName`,\n    // it's defined as getter-setter pair on `memo` (see #3192).\n    displayName: true\n}\n\nfunction copyStaticProperties(base: any, target: any) {\n    Object.keys(base).forEach(key => {\n        if (!hoistBlackList[key]) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(base, key)!)\n        }\n    })\n}\n", "import { useObserver } from \"./useObserver\"\n\n// TODO: this type could be improved in the next major release:\n// type IObserverProps = { children: () => React.ReactNode, render?: never } | { children?: never, render: () => React.ReactNode }\ninterface IObserverProps {\n    children?(): React.ReactElement | null\n    render?(): React.ReactElement | null\n}\n\nfunction ObserverComponent({ children, render }: IObserverProps) {\n    if (children && render) {\n        console.error(\n            \"MobX Observer: Do not use children and render in the same time in `Observer`\"\n        )\n    }\n    const component = children || render\n    if (typeof component !== \"function\") {\n        return null\n    }\n    return useObserver(component)\n}\nif (\"production\" !== process.env.NODE_ENV) {\n    ObserverComponent.propTypes = {\n        children: ObserverPropsCheck,\n        render: ObserverPropsCheck\n    }\n}\nObserverComponent.displayName = \"Observer\"\n\nexport { ObserverComponent as Observer }\n\nfunction ObserverPropsCheck(\n    props: { [k: string]: any },\n    key: string,\n    componentName: string,\n    location: any,\n    propFullName: string\n) {\n    const extraKey = key === \"children\" ? \"render\" : \"children\"\n    const hasProp = typeof props[key] === \"function\"\n    const hasExtraProp = typeof props[extraKey] === \"function\"\n    if (hasProp && hasExtraProp) {\n        return new Error(\n            \"MobX Observer: Do not use children and render in the same time in`\" + componentName\n        )\n    }\n\n    if (hasProp || hasExtraProp) {\n        return null\n    }\n    return new Error(\n        \"Invalid prop `\" +\n            propFullName +\n            \"` of type `\" +\n            typeof props[key] +\n            \"` supplied to\" +\n            \" `\" +\n            componentName +\n            \"`, expected `function`.\"\n    )\n}\n", "import { observable, AnnotationsMap } from \"mobx\"\nimport { useState } from \"react\"\n\nexport function useLocalObservable<TStore extends Record<string, any>>(\n    initializer: () => TStore,\n    annotations?: AnnotationsMap<TStore, never>\n): TStore {\n    return useState(() => observable(initializer(), annotations, { autoBind: true }))[0]\n}\n", "import { observable } from \"mobx\"\nimport { useState } from \"react\"\n\nimport { useDeprecated } from \"./utils/utils\"\nimport { useAsObservableSource } from \"./useAsObservableSource\"\n\nexport function useLocalStore<TStore extends Record<string, any>>(initializer: () => TStore): TStore\nexport function useLocalStore<TStore extends Record<string, any>, TSource extends object>(\n    initializer: (source: TSource) => TStore,\n    current: TSource\n): TStore\nexport function useLocalStore<TStore extends Record<string, any>, TSource extends object>(\n    initializer: (source?: TSource) => TStore,\n    current?: TSource\n): TStore {\n    if (\"production\" !== process.env.NODE_ENV) {\n        useDeprecated(\n            \"[mobx-react-lite] 'useLocalStore' is deprecated, use 'useLocalObservable' instead.\"\n        )\n    }\n    const source = current && useAsObservableSource(current)\n    return useState(() => observable(initializer(source), undefined, { autoBind: true }))[0]\n}\n", "import { useDeprecated } from \"./utils/utils\"\nimport { observable, runInAction } from \"mobx\"\nimport { useState } from \"react\"\n\nexport function useAsObservableSource<TSource extends object>(current: TSource): TSource {\n    if (\"production\" !== process.env.NODE_ENV)\n        useDeprecated(\n            \"[mobx-react-lite] 'useAsObservableSource' is deprecated, please store the values directly in an observable, for example by using 'useLocalObservable', and sync future updates using 'useEffect' when needed. See the README for examples.\"\n        )\n    // We're deliberately not using idiomatic destructuring for the hook here.\n    // Accessing the state value as an array element prevents TypeScript from generating unnecessary helpers in the resulting code.\n    // For further details, please refer to mobxjs/mobx#3842.\n    const res = useState(() => observable(current, {}, { deep: false }))[0]\n    runInAction(() => {\n        Object.assign(res, current)\n    })\n    return res\n}\n", "import \"./utils/assertEnvironment\"\n\nimport { unstable_batchedUpdates as batch } from \"./utils/reactBatchedUpdates\"\nimport { observerBatching } from \"./utils/observerBatching\"\nimport { useDeprecated } from \"./utils/utils\"\nimport { useObserver as useObserverOriginal } from \"./useObserver\"\nimport { enableStaticRendering } from \"./staticRendering\"\nimport { observerFinalizationRegistry } from \"./utils/observerFinalizationRegistry\"\n\nobserverBatching(batch)\n\nexport { isUsingStaticRendering, enableStaticRendering } from \"./staticRendering\"\nexport { observer, IObserverOptions } from \"./observer\"\nexport { Observer } from \"./ObserverComponent\"\nexport { useLocalObservable } from \"./useLocalObservable\"\nexport { useLocalStore } from \"./useLocalStore\"\nexport { useAsObservableSource } from \"./useAsObservableSource\"\n\nexport { observerFinalizationRegistry as _observerFinalizationRegistry }\nexport const clearTimers = observerFinalizationRegistry[\"finalizeAllImmediately\"] ?? (() => {})\n\nexport function useObserver<T>(fn: () => T, baseComponentName: string = \"observed\"): T {\n    if (\"production\" !== process.env.NODE_ENV) {\n        useDeprecated(\n            \"[mobx-react-lite] 'useObserver(fn)' is deprecated. Use `<Observer>{fn}</Observer>` instead, or wrap the entire component in `observer`.\"\n        )\n    }\n    return useObserverOriginal(fn, baseComponentName)\n}\n\nexport { isObserverBatched, observerBatching } from \"./utils/observerBatching\"\n\nexport function useStaticRendering(enable: boolean) {\n    if (\"production\" !== process.env.NODE_ENV) {\n        console.warn(\n            \"[mobx-react-lite] 'useStaticRendering' is deprecated, use 'enableStaticRendering' instead\"\n        )\n    }\n    enableStaticRendering(enable)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,mBAAyB;AAEzB,IAAI,CAAC,uBAAU;AACX,QAAM,IAAI,MAAM,mDAAmD;AACvE;AACA,IAAI,CAAC,gBAAgB;AACjB,QAAM,IAAI,MAAM,oEAAoE;AACxF;;;ACRA,uBAAwC;;;ACElC,SAAU,iBAAiB,UAAoB;AACjD,WAAQ;AACZ;AAEM,SAAU,iBAAiB,mBAAsB;AACnD,MAAI,CAAC,mBAAmB;AACpB,wBAAoB;AACpB,QAAI,MAAuC;AACvC,cAAQ,KACJ,6EAA6E;IAErF;EACJ;AACA,YAAU,EAAE,kBAAiB,CAAE;AACnC;AAEO,IAAM,oBAAoB,WAAA;AAC7B,MAAI,MAAuC;AACvC,YAAQ,KAAK,mBAAmB;EACpC;AAEA,SAAO;AACX;;;ACxBA,IAAM,qBAA+B,CAAA;AAE/B,SAAU,cAAc,KAAW;AACrC,MAAI,CAAC,mBAAmB,SAAS,GAAG,GAAG;AACnC,uBAAmB,KAAK,GAAG;AAC3B,YAAQ,KAAK,GAAG;EACpB;AACJ;;;ACNA,IAAAA,gBAAkB;;;ACCZ,SAAU,gBAAgB,GAAW;AACvC,SAAO,kBAAkB,CAAC;AAC9B;;;ACJA,IAAI,+BAA+B;AAE7B,SAAU,sBAAsB,QAAe;AACjD,iCAA+B;AACnC;AAEM,SAAU,yBAAsB;AAClC,SAAO;AACX;;;ACAO,IAAM,0BAA0B;AAChC,IAAM,0BAA0B;AAEvC,IAAA;;EAAA,WAAA;AAII,aAAAC,gCAA6B,UAA4B;AAAzD,UAAA,QAAA;AAAY,aAAA,eAAA,MAAA,YAAA;;;;eAAiB;;AAHrB,aAAA,eAAA,MAAA,iBAAA;;;;eAAkE,oBAAI,IAAG;;AACzE,aAAA,eAAA,MAAA,gBAAA;;;;;;AAkBR,aAAA,eAAA,MAAA,SAAA;;;;eAAQ,SAAC,QAAgC;AAAhC,cAAA,WAAA,QAAA;AAAA,qBAAA;UAAgC;AAErC,uBAAa,MAAK,YAAY;AAC9B,gBAAK,eAAe;AAEpB,cAAM,MAAM,KAAK,IAAG;AACpB,gBAAK,cAAc,QAAQ,SAAC,cAAc,OAAK;AAC3C,gBAAI,MAAM,aAAa,gBAAgB,QAAQ;AAC3C,oBAAK,SAAS,aAAa,KAAK;AAChC,oBAAK,cAAc,OAAO,KAAK;YACnC;UACJ,CAAC;AAED,cAAI,MAAK,cAAc,OAAO,GAAG;AAC7B,kBAAK,cAAa;UACtB;QACJ;;AAGA,aAAA,eAAA,MAAA,0BAAA;;;;eAAyB,WAAA;AACrB,gBAAK,MAAM,CAAC;QAChB;;IArC4D;;;;;aAG5D,SAAS,QAAgB,OAAU,OAAc;AAC7C,aAAK,cAAc,IAAI,OAAO;UAC1B;UACA,cAAc,KAAK,IAAG;SACzB;AACD,aAAK,cAAa;MACtB;;;;;;aAEA,SAAW,OAAc;AACrB,aAAK,cAAc,OAAO,KAAK;MACnC;;;;;;aA0BA,WAAA;AACI,YAAI,KAAK,iBAAiB,QAAW;AACjC,eAAK,eAAe,WAAW,KAAK,OAAO,uBAAuB;QACtE;MACJ;;AACJ,WAAAA;EAAA,EAhDA;;AAkDO,IAAM,gCACT,OAAO,yBAAyB,cAC1B,uBACA;;;AC7DH,IAAM,+BAA+B,IAAI,8BAC5C,SAAC,KAAkC;;AAC/B,GAAAC,MAAA,IAAI,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAO;AACrB,MAAI,WAAW;AACnB,CAAC;;;AJFL,kBAAqC;AAmBrC,SAAS,eAAe,KAA2B;AAC/C,MAAI,WAAW,IAAI,SAAS,WAAA,OAAW,IAAI,IAAI,GAAI,WAAA;;AAC/C,QAAI,eAAe,OAAM;AAIzB,KAAAC,MAAA,IAAI,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,GAAA;EACrB,CAAC;AACL;AAEM,SAAU,YAAe,QAAiB,mBAAsC;AAAtC,MAAA,sBAAA,QAAA;AAAA,wBAAA;EAAsC;AAClF,MAAI,uBAAsB,GAAI;AAC1B,WAAO,OAAM;EACjB;AAEA,MAAM,SAAS,cAAAC,QAAM,OAAsC,IAAI;AAE/D,MAAI,CAAC,OAAO,SAAS;AAEjB,QAAM,QAA8B;MAChC,UAAU;MACV,eAAe;MACf,cAAc,OAAM;MACpB,MAAM;MACN,WAAS,SAAC,eAAyB;AAE/B,qCAA6B,WAAW,KAAG;AAC3C,cAAI,gBAAgB;AACpB,YAAI,CAAC,MAAI,UAAU;AAMf,yBAAe,KAAG;AAGlB,gBAAI,eAAe,OAAM;QAC7B;AAEA,eAAO,WAAA;;AAEH,gBAAI,gBAAgB;AACpB,WAAAD,MAAA,MAAI,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAO;AACrB,gBAAI,WAAW;QACnB;MACJ;MACA,aAAW,WAAA;AAEP,eAAO,MAAI;MACf;;AAGJ,WAAO,UAAU;EACrB;AAEA,MAAM,MAAM,OAAO;AAEnB,MAAI,CAAC,IAAI,UAAU;AAEf,mBAAe,GAAG;AAIlB,iCAA6B,SAAS,QAAQ,KAAK,GAAG;EAC1D;AAEA,gBAAAC,QAAM,cAAc,IAAI,UAAW,eAAe;AAElD;;IAEI,IAAI;IACJ,IAAI;IACJ,IAAI;EAAW;AAMnB,MAAI;AACJ,MAAI;AACJ,MAAI,SAAU,MAAM,WAAA;AAChB,QAAI;AACA,qBAAe,OAAM;IACzB,SAAS,GAAG;AACR,kBAAY;IAChB;EACJ,CAAC;AAED,MAAI,WAAW;AACX,UAAM;EACV;AAEA,SAAO;AACX;;;AKtHA,IAAAC,gBAAiC;;;AAKjC,IAAI,gCAAgC;AACpC,IAAI,yBAAyB;AAE7B,IAAM,YAAY,OAAO,WAAW,cAAc,OAAO;AACzD,IAAM,8BACF,MAAA,KAAA,OAAO,yBAAyB,WAAA;AAAO,GAAG,MAAM,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;AAGvE,IAAM,wBAAwB,YACxB,OAAO,IAAI,mBAAmB,IAC9B,OAAO,6BAAe,kBAAc,0BAAW,SAAC,OAAU;AAAK,SAAA;AAAA,CAAI,EAAE,UAAU;AAErF,IAAM,kBAAkB,YAClB,OAAO,IAAI,YAAY,IACvB,OAAO,uBAAS,kBAAc,oBAAK,SAAC,OAAU;AAAK,SAAA;AAAA,CAAI,EAAE,UAAU;AA0DnE,SAAU,SACZ,eAKA,SAA0B;;AAE1B,MAA6C,iCAAiC,SAAS;AACnF,oCAAgC;AAChC,YAAQ,KACJ,4GAAgH;EAExH;AAEA,MAAI,mBAAmB,cAAc,UAAU,MAAM,iBAAiB;AAClE,UAAM,IAAI,MACN,qLAA2L;EAEnM;AAGA,MAAI,uBAAsB,GAAI;AAC1B,WAAO;EACX;AAEA,MAAI,iBAAgBC,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAU,QAAAA,QAAA,SAAAA,MAAI;AAC3C,MAAI,SAAS;AAEb,MAAM,oBAAoB,cAAc,eAAe,cAAc;AAIrE,MAAI,yBAAyB,cAAc,UAAU,MAAM,uBAAuB;AAC9E,oBAAgB;AAChB,aAAS,cAAc,QAAQ;AAC/B,QAAI,OAAO,WAAW,YAAY;AAC9B,YAAM,IAAI,MACN,sEAAwE;IAEhF;EACJ;AAEA,MAAI,oBAAoB,SAAC,OAAY,KAAoB;AACrD,WAAO,YAAY,WAAA;AAAM,aAAA,OAAO,OAAO,GAAG;IAAjB,GAAoB,iBAAiB;EAClE;AAGE,oBAA8C,cAAc,cAAc;AAE5E,MAAI,4BAA4B;AAC5B,WAAO,eAAe,mBAAmB,QAAQ;MAC7C,OAAO,cAAc;MACrB,UAAU;MACV,cAAc;KACjB;EACL;AAGA,MAAK,cAAsB,cAAc;AACrC;AAAE,sBAA8C,eAC5C,cACF;AAEF,QAA6C,wBAAwB;AACjE,+BAAyB;AACzB,cAAQ,KACJ,gHAAgH;IAExH;EACJ;AAEA,MAAI,eAAe;AAIf,4BAAoB,0BAAW,iBAAiB;EACpD;AAKA,0BAAoB,oBAAK,iBAAiB;AAE1C,uBAAqB,eAAe,iBAAiB;AAErD,MAAI,MAAuC;AACvC,WAAO,eAAe,mBAAmB,gBAAgB;MACrD,KAAG,WAAA;;AACC,cAAM,IAAI,MACN,sBAAA,OACI,KAAK,iBAAeA,MAAA,KAAK,UAAI,QAAAA,QAAA,SAAA,SAAAA,IAAE,kBAAeC,MAAA,KAAK,UAAI,QAAAA,QAAA,SAAA,SAAAA,IAAE,SAAQ,aAAW,wDAAA,CACrB;MAEnE;KACH;EACL;AAEA,SAAO;AACX;AAGA,IAAM,iBAAsB;EACxB,UAAU;EACV,QAAQ;EACR,SAAS;EACT,MAAM;;;EAGN,aAAa;;AAGjB,SAAS,qBAAqB,MAAW,QAAW;AAChD,SAAO,KAAK,IAAI,EAAE,QAAQ,SAAA,KAAG;AACzB,QAAI,CAAC,eAAe,GAAG,GAAG;AACtB,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,MAAM,GAAG,CAAE;IAClF;EACJ,CAAC;AACL;;;AC1LA,SAAS,kBAAkBC,KAAoC;MAAlC,WAAQA,IAAA,UAAE,SAAMA,IAAA;AACzC,MAAI,YAAY,QAAQ;AACpB,YAAQ,MACJ,8EAA8E;EAEtF;AACA,MAAM,YAAY,YAAY;AAC9B,MAAI,OAAO,cAAc,YAAY;AACjC,WAAO;EACX;AACA,SAAO,YAAY,SAAS;AAChC;AACA,IAAI,MAAuC;AACvC,oBAAkB,YAAY;IAC1B,UAAU;IACV,QAAQ;;AAEhB;AACA,kBAAkB,cAAc;AAIhC,SAAS,mBACL,OACA,KACA,eACA,UACA,cAAoB;AAEpB,MAAM,WAAW,QAAQ,aAAa,WAAW;AACjD,MAAM,UAAU,OAAO,MAAM,GAAG,MAAM;AACtC,MAAM,eAAe,OAAO,MAAM,QAAQ,MAAM;AAChD,MAAI,WAAW,cAAc;AACzB,WAAO,IAAI,MACP,uEAAuE,aAAa;EAE5F;AAEA,MAAI,WAAW,cAAc;AACzB,WAAO;EACX;AACA,SAAO,IAAI,MACP,mBACI,eACA,gBACA,OAAO,MAAM,GAAG,IAChB,oBAEA,gBACA,yBAAyB;AAErC;;;AC3DA,IAAAC,gBAAyB;AAEnB,SAAU,mBACZ,aACA,aAA2C;AAE3C,aAAO,wBAAS,WAAA;AAAM,WAAA,WAAW,YAAW,GAAI,aAAa,EAAE,UAAU,KAAI,CAAE;EAAzD,CAA0D,EAAE,CAAC;AACvF;;;ACPA,IAAAC,gBAAyB;;;ACCzB,IAAAC,gBAAyB;AAEnB,SAAU,sBAA8C,SAAgB;AAC1E,MAAI;AACA,kBACI,4OAA4O;AAKpP,MAAM,UAAM,wBAAS,WAAA;AAAM,WAAA,WAAW,SAAS,CAAA,GAAI,EAAE,MAAM,MAAK,CAAE;EAAvC,CAAwC,EAAE,CAAC;AACtE,cAAY,WAAA;AACR,WAAO,OAAO,KAAK,OAAO;EAC9B,CAAC;AACD,SAAO;AACX;;;ADNM,SAAU,cACZ,aACA,SAAiB;AAEjB,MAAI,MAAuC;AACvC,kBACI,oFAAoF;EAE5F;AACA,MAAM,SAAS,WAAW,sBAAsB,OAAO;AACvD,aAAO,wBAAS,WAAA;AAAM,WAAA,WAAW,YAAY,MAAM,GAAG,QAAW,EAAE,UAAU,KAAI,CAAE;EAA7D,CAA8D,EAAE,CAAC;AAC3F;;;;AEbA,iBAAiB,wCAAK;AAUf,IAAM,eAAcC,MAAA,6BAA6B,wBAAwB,OAAC,QAAAA,QAAA,SAAAA,MAAK,WAAA;AAAO;AAEvF,SAAUC,aAAe,IAAa,mBAAsC;AAAtC,MAAA,sBAAA,QAAA;AAAA,wBAAA;EAAsC;AAC9E,MAAI,MAAuC;AACvC,kBACI,yIAAyI;EAEjJ;AACA,SAAO,YAAoB,IAAI,iBAAiB;AACpD;AAIM,SAAU,mBAAmB,QAAe;AAC9C,MAAI,MAAuC;AACvC,YAAQ,KACJ,2FAA2F;EAEnG;AACA,wBAAsB,MAAM;AAChC;", "names": ["import_react", "TimerBasedFinalizationRegistry", "_a", "_a", "React", "import_react", "_a", "_b", "_a", "import_react", "import_react", "import_react", "_a", "useObserver"]}