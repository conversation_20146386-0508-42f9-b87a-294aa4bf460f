import {
  __privateAdd,
  __privateGet,
  __privateMethod,
  __privateSet,
  __privateWrapper,
  __publicField
} from "./chunk-OL46QLBJ.js";

// node_modules/.pnpm/mqtt@5.13.3/node_modules/mqtt/dist/mqtt.esm.js
var cs = Object.defineProperty;
var Hg = Object.getOwnPropertyDescriptor;
var Vg = Object.getOwnPropertyNames;
var zg = Object.prototype.hasOwnProperty;
var Ae = (t, e) => () => (t && (e = t(t = 0)), e);
var O = (t, e) => () => (e || t((e = { exports: {} }).exports, e), e.exports);
var Ar = (t, e) => {
  for (var r in e) cs(t, r, { get: e[r], enumerable: true });
};
var Kg = (t, e, r, i) => {
  if (e && typeof e == "object" || typeof e == "function") for (let n of Vg(e)) !zg.call(t, n) && n !== r && cs(t, n, { get: () => e[n], enumerable: !(i = Hg(e, n)) || i.enumerable });
  return t;
};
var G = (t) => Kg(cs({}, "__esModule", { value: true }), t);
var _ = Ae(() => {
});
var R = {};
Ar(R, { _debugEnd: () => fu, _debugProcess: () => cu, _events: () => Ru, _eventsCount: () => Cu, _exiting: () => Vl, _fatalExceptions: () => ou, _getActiveHandles: () => Gl, _getActiveRequests: () => Ql, _kill: () => Jl, _linkedBinding: () => $l, _maxListeners: () => Tu, _preload_modules: () => Su, _rawDebug: () => Fl, _startProfilerIdleNotifier: () => hu, _stopProfilerIdleNotifier: () => du, _tickCallback: () => uu, abort: () => bu, addListener: () => ku, allowedNodeEnvironmentFlags: () => iu, arch: () => Cl, argv: () => Bl, argv0: () => Eu, assert: () => nu, binding: () => ql, chdir: () => Dl, config: () => zl, cpuUsage: () => Fi, cwd: () => Nl, debugPort: () => vu, default: () => Nu, dlopen: () => Kl, domain: () => Hl, emit: () => Mu, emitWarning: () => Ll, env: () => Pl, execArgv: () => xl, execPath: () => mu, exit: () => tu, features: () => su, hasUncaughtExceptionCaptureCallback: () => lu, hrtime: () => ji, kill: () => eu, listeners: () => Uu, memoryUsage: () => Zl, moduleLoadList: () => Wl, nextTick: () => Il, off: () => Bu, on: () => yt, once: () => Pu, openStdin: () => ru, pid: () => wu, platform: () => kl, ppid: () => _u, prependListener: () => Lu, prependOnceListener: () => qu, reallyExit: () => Yl, release: () => jl, removeAllListeners: () => Ou, removeListener: () => xu, resourceUsage: () => Xl, setSourceMapsEnabled: () => Au, setUncaughtExceptionCaptureCallback: () => au, stderr: () => gu, stdin: () => yu, stdout: () => pu, title: () => Rl, umask: () => Ul, uptime: () => Iu, version: () => Ol, versions: () => Ml });
function ds(t) {
  throw new Error("Node.js process " + t + " is not supported by JSPM core outside of Node.js");
}
function Qg() {
  !Ir || !Qt || (Ir = false, Qt.length ? gt = Qt.concat(gt) : Di = -1, gt.length && Al());
}
function Al() {
  if (!Ir) {
    var t = setTimeout(Qg, 0);
    Ir = true;
    for (var e = gt.length; e; ) {
      for (Qt = gt, gt = []; ++Di < e; ) Qt && Qt[Di].run();
      Di = -1, e = gt.length;
    }
    Qt = null, Ir = false, clearTimeout(t);
  }
}
function Il(t) {
  var e = new Array(arguments.length - 1);
  if (arguments.length > 1) for (var r = 1; r < arguments.length; r++) e[r - 1] = arguments[r];
  gt.push(new Tl(t, e)), gt.length === 1 && !Ir && setTimeout(Al, 0);
}
function Tl(t, e) {
  this.fun = t, this.array = e;
}
function _e() {
}
function $l(t) {
  ds("_linkedBinding");
}
function Kl(t) {
  ds("dlopen");
}
function Ql() {
  return [];
}
function Gl() {
  return [];
}
function nu(t, e) {
  if (!t) throw new Error(e || "assertion error");
}
function lu() {
  return false;
}
function Iu() {
  return Pt.now() / 1e3;
}
function ji(t) {
  var e = Math.floor((Date.now() - Pt.now()) * 1e-3), r = Pt.now() * 1e-3, i = Math.floor(r) + e, n = Math.floor(r % 1 * 1e9);
  return t && (i = i - t[0], n = n - t[1], n < 0 && (i--, n += hs)), [i, n];
}
function yt() {
  return Nu;
}
function Uu(t) {
  return [];
}
var gt;
var Ir;
var Qt;
var Di;
var Rl;
var Cl;
var kl;
var Pl;
var Bl;
var xl;
var Ol;
var Ml;
var Ll;
var ql;
var Ul;
var Nl;
var Dl;
var jl;
var Fl;
var Wl;
var Hl;
var Vl;
var zl;
var Yl;
var Jl;
var Fi;
var Xl;
var Zl;
var eu;
var tu;
var ru;
var iu;
var su;
var ou;
var au;
var uu;
var cu;
var fu;
var hu;
var du;
var pu;
var gu;
var yu;
var bu;
var wu;
var _u;
var mu;
var vu;
var Eu;
var Su;
var Au;
var Pt;
var fs;
var hs;
var Tu;
var Ru;
var Cu;
var ku;
var Pu;
var Bu;
var xu;
var Ou;
var Mu;
var Lu;
var qu;
var Nu;
var Du = Ae(() => {
  _();
  v();
  m();
  gt = [], Ir = false, Di = -1;
  Tl.prototype.run = function() {
    this.fun.apply(null, this.array);
  };
  Rl = "browser", Cl = "x64", kl = "browser", Pl = { PATH: "/usr/bin", LANG: navigator.language + ".UTF-8", PWD: "/", HOME: "/home", TMP: "/tmp" }, Bl = ["/usr/bin/node"], xl = [], Ol = "v16.8.0", Ml = {}, Ll = function(t, e) {
    console.warn((e ? e + ": " : "") + t);
  }, ql = function(t) {
    ds("binding");
  }, Ul = function(t) {
    return 0;
  }, Nl = function() {
    return "/";
  }, Dl = function(t) {
  }, jl = { name: "node", sourceUrl: "", headersUrl: "", libUrl: "" };
  Fl = _e, Wl = [];
  Hl = {}, Vl = false, zl = {};
  Yl = _e, Jl = _e, Fi = function() {
    return {};
  }, Xl = Fi, Zl = Fi, eu = _e, tu = _e, ru = _e, iu = {};
  su = { inspector: false, debug: false, uv: false, ipv6: false, tls_alpn: false, tls_sni: false, tls_ocsp: false, tls: false, cached_builtins: true }, ou = _e, au = _e;
  uu = _e, cu = _e, fu = _e, hu = _e, du = _e, pu = void 0, gu = void 0, yu = void 0, bu = _e, wu = 2, _u = 1, mu = "/bin/usr/node", vu = 9229, Eu = "node", Su = [], Au = _e, Pt = { now: typeof performance < "u" ? performance.now.bind(performance) : void 0, timing: typeof performance < "u" ? performance.timing : void 0 };
  Pt.now === void 0 && (fs = Date.now(), Pt.timing && Pt.timing.navigationStart && (fs = Pt.timing.navigationStart), Pt.now = () => Date.now() - fs);
  hs = 1e9;
  ji.bigint = function(t) {
    var e = ji(t);
    return typeof BigInt > "u" ? e[0] * hs + e[1] : BigInt(e[0] * hs) + BigInt(e[1]);
  };
  Tu = 10, Ru = {}, Cu = 0;
  ku = yt, Pu = yt, Bu = yt, xu = yt, Ou = yt, Mu = _e, Lu = yt, qu = yt;
  Nu = { version: Ol, versions: Ml, arch: Cl, platform: kl, release: jl, _rawDebug: Fl, moduleLoadList: Wl, binding: ql, _linkedBinding: $l, _events: Ru, _eventsCount: Cu, _maxListeners: Tu, on: yt, addListener: ku, once: Pu, off: Bu, removeListener: xu, removeAllListeners: Ou, emit: Mu, prependListener: Lu, prependOnceListener: qu, listeners: Uu, domain: Hl, _exiting: Vl, config: zl, dlopen: Kl, uptime: Iu, _getActiveRequests: Ql, _getActiveHandles: Gl, reallyExit: Yl, _kill: Jl, cpuUsage: Fi, resourceUsage: Xl, memoryUsage: Zl, kill: eu, exit: tu, openStdin: ru, allowedNodeEnvironmentFlags: iu, assert: nu, features: su, _fatalExceptions: ou, setUncaughtExceptionCaptureCallback: au, hasUncaughtExceptionCaptureCallback: lu, emitWarning: Ll, nextTick: Il, _tickCallback: uu, _debugProcess: cu, _debugEnd: fu, _startProfilerIdleNotifier: hu, _stopProfilerIdleNotifier: du, stdout: pu, stdin: yu, stderr: gu, abort: bu, umask: Ul, chdir: Dl, cwd: Nl, env: Pl, title: Rl, argv: Bl, execArgv: xl, pid: wu, ppid: _u, execPath: mu, debugPort: vu, hrtime: ji, argv0: Eu, _preload_modules: Su, setSourceMapsEnabled: Au };
});
var m = Ae(() => {
  Du();
});
var be = {};
Ar(be, { Buffer: () => x, INSPECT_MAX_BYTES: () => Xg, default: () => Bt, kMaxLength: () => Zg });
function Gg() {
  if (ju) return si;
  ju = true, si.byteLength = a, si.toByteArray = f, si.fromByteArray = g;
  for (var t = [], e = [], r = typeof Uint8Array < "u" ? Uint8Array : Array, i = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", n = 0, o = i.length; n < o; ++n) t[n] = i[n], e[i.charCodeAt(n)] = n;
  e[45] = 62, e[95] = 63;
  function s(y) {
    var E = y.length;
    if (E % 4 > 0) throw new Error("Invalid string. Length must be a multiple of 4");
    var w = y.indexOf("=");
    w === -1 && (w = E);
    var S = w === E ? 0 : 4 - w % 4;
    return [w, S];
  }
  function a(y) {
    var E = s(y), w = E[0], S = E[1];
    return (w + S) * 3 / 4 - S;
  }
  function u(y, E, w) {
    return (E + w) * 3 / 4 - w;
  }
  function f(y) {
    var E, w = s(y), S = w[0], I = w[1], C = new r(u(y, S, I)), k = 0, M = I > 0 ? S - 4 : S, q;
    for (q = 0; q < M; q += 4) E = e[y.charCodeAt(q)] << 18 | e[y.charCodeAt(q + 1)] << 12 | e[y.charCodeAt(q + 2)] << 6 | e[y.charCodeAt(q + 3)], C[k++] = E >> 16 & 255, C[k++] = E >> 8 & 255, C[k++] = E & 255;
    return I === 2 && (E = e[y.charCodeAt(q)] << 2 | e[y.charCodeAt(q + 1)] >> 4, C[k++] = E & 255), I === 1 && (E = e[y.charCodeAt(q)] << 10 | e[y.charCodeAt(q + 1)] << 4 | e[y.charCodeAt(q + 2)] >> 2, C[k++] = E >> 8 & 255, C[k++] = E & 255), C;
  }
  function d(y) {
    return t[y >> 18 & 63] + t[y >> 12 & 63] + t[y >> 6 & 63] + t[y & 63];
  }
  function h(y, E, w) {
    for (var S, I = [], C = E; C < w; C += 3) S = (y[C] << 16 & 16711680) + (y[C + 1] << 8 & 65280) + (y[C + 2] & 255), I.push(d(S));
    return I.join("");
  }
  function g(y) {
    for (var E, w = y.length, S = w % 3, I = [], C = 16383, k = 0, M = w - S; k < M; k += C) I.push(h(y, k, k + C > M ? M : k + C));
    return S === 1 ? (E = y[w - 1], I.push(t[E >> 2] + t[E << 4 & 63] + "==")) : S === 2 && (E = (y[w - 2] << 8) + y[w - 1], I.push(t[E >> 10] + t[E >> 4 & 63] + t[E << 2 & 63] + "=")), I.join("");
  }
  return si;
}
function Yg() {
  if (Fu) return Wi;
  Fu = true;
  return Wi.read = function(t, e, r, i, n) {
    var o, s, a = n * 8 - i - 1, u = (1 << a) - 1, f = u >> 1, d = -7, h = r ? n - 1 : 0, g = r ? -1 : 1, y = t[e + h];
    for (h += g, o = y & (1 << -d) - 1, y >>= -d, d += a; d > 0; o = o * 256 + t[e + h], h += g, d -= 8) ;
    for (s = o & (1 << -d) - 1, o >>= -d, d += i; d > 0; s = s * 256 + t[e + h], h += g, d -= 8) ;
    if (o === 0) o = 1 - f;
    else {
      if (o === u) return s ? NaN : (y ? -1 : 1) * (1 / 0);
      s = s + Math.pow(2, i), o = o - f;
    }
    return (y ? -1 : 1) * s * Math.pow(2, o - i);
  }, Wi.write = function(t, e, r, i, n, o) {
    var s, a, u, f = o * 8 - n - 1, d = (1 << f) - 1, h = d >> 1, g = n === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0, y = i ? 0 : o - 1, E = i ? 1 : -1, w = e < 0 || e === 0 && 1 / e < 0 ? 1 : 0;
    for (e = Math.abs(e), isNaN(e) || e === 1 / 0 ? (a = isNaN(e) ? 1 : 0, s = d) : (s = Math.floor(Math.log(e) / Math.LN2), e * (u = Math.pow(2, -s)) < 1 && (s--, u *= 2), s + h >= 1 ? e += g / u : e += g * Math.pow(2, 1 - h), e * u >= 2 && (s++, u /= 2), s + h >= d ? (a = 0, s = d) : s + h >= 1 ? (a = (e * u - 1) * Math.pow(2, n), s = s + h) : (a = e * Math.pow(2, h - 1) * Math.pow(2, n), s = 0)); n >= 8; t[r + y] = a & 255, y += E, a /= 256, n -= 8) ;
    for (s = s << n | a, f += n; f > 0; t[r + y] = s & 255, y += E, s /= 256, f -= 8) ;
    t[r + y - E] |= w * 128;
  }, Wi;
}
function Jg() {
  if (Wu) return Gt;
  Wu = true;
  let t = Gg(), e = Yg(), r = typeof Symbol == "function" && typeof Symbol.for == "function" ? Symbol.for("nodejs.util.inspect.custom") : null;
  Gt.Buffer = s, Gt.SlowBuffer = I, Gt.INSPECT_MAX_BYTES = 50;
  let i = 2147483647;
  Gt.kMaxLength = i, s.TYPED_ARRAY_SUPPORT = n(), !s.TYPED_ARRAY_SUPPORT && typeof console < "u" && typeof console.error == "function" && console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");
  function n() {
    try {
      let p = new Uint8Array(1), l = { foo: function() {
        return 42;
      } };
      return Object.setPrototypeOf(l, Uint8Array.prototype), Object.setPrototypeOf(p, l), p.foo() === 42;
    } catch {
      return false;
    }
  }
  Object.defineProperty(s.prototype, "parent", { enumerable: true, get: function() {
    if (s.isBuffer(this)) return this.buffer;
  } }), Object.defineProperty(s.prototype, "offset", { enumerable: true, get: function() {
    if (s.isBuffer(this)) return this.byteOffset;
  } });
  function o(p) {
    if (p > i) throw new RangeError('The value "' + p + '" is invalid for option "size"');
    let l = new Uint8Array(p);
    return Object.setPrototypeOf(l, s.prototype), l;
  }
  function s(p, l, c) {
    if (typeof p == "number") {
      if (typeof l == "string") throw new TypeError('The "string" argument must be of type string. Received type number');
      return d(p);
    }
    return a(p, l, c);
  }
  s.poolSize = 8192;
  function a(p, l, c) {
    if (typeof p == "string") return h(p, l);
    if (ArrayBuffer.isView(p)) return y(p);
    if (p == null) throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof p);
    if (Ge(p, ArrayBuffer) || p && Ge(p.buffer, ArrayBuffer) || typeof SharedArrayBuffer < "u" && (Ge(p, SharedArrayBuffer) || p && Ge(p.buffer, SharedArrayBuffer))) return E(p, l, c);
    if (typeof p == "number") throw new TypeError('The "value" argument must not be of type number. Received type number');
    let b = p.valueOf && p.valueOf();
    if (b != null && b !== p) return s.from(b, l, c);
    let A = w(p);
    if (A) return A;
    if (typeof Symbol < "u" && Symbol.toPrimitive != null && typeof p[Symbol.toPrimitive] == "function") return s.from(p[Symbol.toPrimitive]("string"), l, c);
    throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof p);
  }
  s.from = function(p, l, c) {
    return a(p, l, c);
  }, Object.setPrototypeOf(s.prototype, Uint8Array.prototype), Object.setPrototypeOf(s, Uint8Array);
  function u(p) {
    if (typeof p != "number") throw new TypeError('"size" argument must be of type number');
    if (p < 0) throw new RangeError('The value "' + p + '" is invalid for option "size"');
  }
  function f(p, l, c) {
    return u(p), p <= 0 ? o(p) : l !== void 0 ? typeof c == "string" ? o(p).fill(l, c) : o(p).fill(l) : o(p);
  }
  s.alloc = function(p, l, c) {
    return f(p, l, c);
  };
  function d(p) {
    return u(p), o(p < 0 ? 0 : S(p) | 0);
  }
  s.allocUnsafe = function(p) {
    return d(p);
  }, s.allocUnsafeSlow = function(p) {
    return d(p);
  };
  function h(p, l) {
    if ((typeof l != "string" || l === "") && (l = "utf8"), !s.isEncoding(l)) throw new TypeError("Unknown encoding: " + l);
    let c = C(p, l) | 0, b = o(c), A = b.write(p, l);
    return A !== c && (b = b.slice(0, A)), b;
  }
  function g(p) {
    let l = p.length < 0 ? 0 : S(p.length) | 0, c = o(l);
    for (let b = 0; b < l; b += 1) c[b] = p[b] & 255;
    return c;
  }
  function y(p) {
    if (Ge(p, Uint8Array)) {
      let l = new Uint8Array(p);
      return E(l.buffer, l.byteOffset, l.byteLength);
    }
    return g(p);
  }
  function E(p, l, c) {
    if (l < 0 || p.byteLength < l) throw new RangeError('"offset" is outside of buffer bounds');
    if (p.byteLength < l + (c || 0)) throw new RangeError('"length" is outside of buffer bounds');
    let b;
    return l === void 0 && c === void 0 ? b = new Uint8Array(p) : c === void 0 ? b = new Uint8Array(p, l) : b = new Uint8Array(p, l, c), Object.setPrototypeOf(b, s.prototype), b;
  }
  function w(p) {
    if (s.isBuffer(p)) {
      let l = S(p.length) | 0, c = o(l);
      return c.length === 0 || p.copy(c, 0, 0, l), c;
    }
    if (p.length !== void 0) return typeof p.length != "number" || us(p.length) ? o(0) : g(p);
    if (p.type === "Buffer" && Array.isArray(p.data)) return g(p.data);
  }
  function S(p) {
    if (p >= i) throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x" + i.toString(16) + " bytes");
    return p | 0;
  }
  function I(p) {
    return +p != p && (p = 0), s.alloc(+p);
  }
  s.isBuffer = function(l) {
    return l != null && l._isBuffer === true && l !== s.prototype;
  }, s.compare = function(l, c) {
    if (Ge(l, Uint8Array) && (l = s.from(l, l.offset, l.byteLength)), Ge(c, Uint8Array) && (c = s.from(c, c.offset, c.byteLength)), !s.isBuffer(l) || !s.isBuffer(c)) throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');
    if (l === c) return 0;
    let b = l.length, A = c.length;
    for (let T = 0, P = Math.min(b, A); T < P; ++T) if (l[T] !== c[T]) {
      b = l[T], A = c[T];
      break;
    }
    return b < A ? -1 : A < b ? 1 : 0;
  }, s.isEncoding = function(l) {
    switch (String(l).toLowerCase()) {
      case "hex":
      case "utf8":
      case "utf-8":
      case "ascii":
      case "latin1":
      case "binary":
      case "base64":
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
        return true;
      default:
        return false;
    }
  }, s.concat = function(l, c) {
    if (!Array.isArray(l)) throw new TypeError('"list" argument must be an Array of Buffers');
    if (l.length === 0) return s.alloc(0);
    let b;
    if (c === void 0) for (c = 0, b = 0; b < l.length; ++b) c += l[b].length;
    let A = s.allocUnsafe(c), T = 0;
    for (b = 0; b < l.length; ++b) {
      let P = l[b];
      if (Ge(P, Uint8Array)) T + P.length > A.length ? (s.isBuffer(P) || (P = s.from(P)), P.copy(A, T)) : Uint8Array.prototype.set.call(A, P, T);
      else if (s.isBuffer(P)) P.copy(A, T);
      else throw new TypeError('"list" argument must be an Array of Buffers');
      T += P.length;
    }
    return A;
  };
  function C(p, l) {
    if (s.isBuffer(p)) return p.length;
    if (ArrayBuffer.isView(p) || Ge(p, ArrayBuffer)) return p.byteLength;
    if (typeof p != "string") throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type ' + typeof p);
    let c = p.length, b = arguments.length > 2 && arguments[2] === true;
    if (!b && c === 0) return 0;
    let A = false;
    for (; ; ) switch (l) {
      case "ascii":
      case "latin1":
      case "binary":
        return c;
      case "utf8":
      case "utf-8":
        return ls(p).length;
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
        return c * 2;
      case "hex":
        return c >>> 1;
      case "base64":
        return Sl(p).length;
      default:
        if (A) return b ? -1 : ls(p).length;
        l = ("" + l).toLowerCase(), A = true;
    }
  }
  s.byteLength = C;
  function k(p, l, c) {
    let b = false;
    if ((l === void 0 || l < 0) && (l = 0), l > this.length || ((c === void 0 || c > this.length) && (c = this.length), c <= 0) || (c >>>= 0, l >>>= 0, c <= l)) return "";
    for (p || (p = "utf8"); ; ) switch (p) {
      case "hex":
        return Lg(this, l, c);
      case "utf8":
      case "utf-8":
        return Se(this, l, c);
      case "ascii":
        return os(this, l, c);
      case "latin1":
      case "binary":
        return Mg(this, l, c);
      case "base64":
        return Fe(this, l, c);
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
        return qg(this, l, c);
      default:
        if (b) throw new TypeError("Unknown encoding: " + p);
        p = (p + "").toLowerCase(), b = true;
    }
  }
  s.prototype._isBuffer = true;
  function M(p, l, c) {
    let b = p[l];
    p[l] = p[c], p[c] = b;
  }
  s.prototype.swap16 = function() {
    let l = this.length;
    if (l % 2 !== 0) throw new RangeError("Buffer size must be a multiple of 16-bits");
    for (let c = 0; c < l; c += 2) M(this, c, c + 1);
    return this;
  }, s.prototype.swap32 = function() {
    let l = this.length;
    if (l % 4 !== 0) throw new RangeError("Buffer size must be a multiple of 32-bits");
    for (let c = 0; c < l; c += 4) M(this, c, c + 3), M(this, c + 1, c + 2);
    return this;
  }, s.prototype.swap64 = function() {
    let l = this.length;
    if (l % 8 !== 0) throw new RangeError("Buffer size must be a multiple of 64-bits");
    for (let c = 0; c < l; c += 8) M(this, c, c + 7), M(this, c + 1, c + 6), M(this, c + 2, c + 5), M(this, c + 3, c + 4);
    return this;
  }, s.prototype.toString = function() {
    let l = this.length;
    return l === 0 ? "" : arguments.length === 0 ? Se(this, 0, l) : k.apply(this, arguments);
  }, s.prototype.toLocaleString = s.prototype.toString, s.prototype.equals = function(l) {
    if (!s.isBuffer(l)) throw new TypeError("Argument must be a Buffer");
    return this === l ? true : s.compare(this, l) === 0;
  }, s.prototype.inspect = function() {
    let l = "", c = Gt.INSPECT_MAX_BYTES;
    return l = this.toString("hex", 0, c).replace(/(.{2})/g, "$1 ").trim(), this.length > c && (l += " ... "), "<Buffer " + l + ">";
  }, r && (s.prototype[r] = s.prototype.inspect), s.prototype.compare = function(l, c, b, A, T) {
    if (Ge(l, Uint8Array) && (l = s.from(l, l.offset, l.byteLength)), !s.isBuffer(l)) throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type ' + typeof l);
    if (c === void 0 && (c = 0), b === void 0 && (b = l ? l.length : 0), A === void 0 && (A = 0), T === void 0 && (T = this.length), c < 0 || b > l.length || A < 0 || T > this.length) throw new RangeError("out of range index");
    if (A >= T && c >= b) return 0;
    if (A >= T) return -1;
    if (c >= b) return 1;
    if (c >>>= 0, b >>>= 0, A >>>= 0, T >>>= 0, this === l) return 0;
    let P = T - A, W = b - c, ae = Math.min(P, W), re = this.slice(A, T), le = l.slice(c, b);
    for (let J = 0; J < ae; ++J) if (re[J] !== le[J]) {
      P = re[J], W = le[J];
      break;
    }
    return P < W ? -1 : W < P ? 1 : 0;
  };
  function q(p, l, c, b, A) {
    if (p.length === 0) return -1;
    if (typeof c == "string" ? (b = c, c = 0) : c > 2147483647 ? c = 2147483647 : c < -2147483648 && (c = -2147483648), c = +c, us(c) && (c = A ? 0 : p.length - 1), c < 0 && (c = p.length + c), c >= p.length) {
      if (A) return -1;
      c = p.length - 1;
    } else if (c < 0) if (A) c = 0;
    else return -1;
    if (typeof l == "string" && (l = s.from(l, b)), s.isBuffer(l)) return l.length === 0 ? -1 : z(p, l, c, b, A);
    if (typeof l == "number") return l = l & 255, typeof Uint8Array.prototype.indexOf == "function" ? A ? Uint8Array.prototype.indexOf.call(p, l, c) : Uint8Array.prototype.lastIndexOf.call(p, l, c) : z(p, [l], c, b, A);
    throw new TypeError("val must be string, number or Buffer");
  }
  function z(p, l, c, b, A) {
    let T = 1, P = p.length, W = l.length;
    if (b !== void 0 && (b = String(b).toLowerCase(), b === "ucs2" || b === "ucs-2" || b === "utf16le" || b === "utf-16le")) {
      if (p.length < 2 || l.length < 2) return -1;
      T = 2, P /= 2, W /= 2, c /= 2;
    }
    function ae(le, J) {
      return T === 1 ? le[J] : le.readUInt16BE(J * T);
    }
    let re;
    if (A) {
      let le = -1;
      for (re = c; re < P; re++) if (ae(p, re) === ae(l, le === -1 ? 0 : re - le)) {
        if (le === -1 && (le = re), re - le + 1 === W) return le * T;
      } else le !== -1 && (re -= re - le), le = -1;
    } else for (c + W > P && (c = P - W), re = c; re >= 0; re--) {
      let le = true;
      for (let J = 0; J < W; J++) if (ae(p, re + J) !== ae(l, J)) {
        le = false;
        break;
      }
      if (le) return re;
    }
    return -1;
  }
  s.prototype.includes = function(l, c, b) {
    return this.indexOf(l, c, b) !== -1;
  }, s.prototype.indexOf = function(l, c, b) {
    return q(this, l, c, b, true);
  }, s.prototype.lastIndexOf = function(l, c, b) {
    return q(this, l, c, b, false);
  };
  function j(p, l, c, b) {
    c = Number(c) || 0;
    let A = p.length - c;
    b ? (b = Number(b), b > A && (b = A)) : b = A;
    let T = l.length;
    b > T / 2 && (b = T / 2);
    let P;
    for (P = 0; P < b; ++P) {
      let W = parseInt(l.substr(P * 2, 2), 16);
      if (us(W)) return P;
      p[c + P] = W;
    }
    return P;
  }
  function Q(p, l, c, b) {
    return Ni(ls(l, p.length - c), p, c, b);
  }
  function $(p, l, c, b) {
    return Ni(jg(l), p, c, b);
  }
  function te(p, l, c, b) {
    return Ni(Sl(l), p, c, b);
  }
  function pt(p, l, c, b) {
    return Ni(Fg(l, p.length - c), p, c, b);
  }
  s.prototype.write = function(l, c, b, A) {
    if (c === void 0) A = "utf8", b = this.length, c = 0;
    else if (b === void 0 && typeof c == "string") A = c, b = this.length, c = 0;
    else if (isFinite(c)) c = c >>> 0, isFinite(b) ? (b = b >>> 0, A === void 0 && (A = "utf8")) : (A = b, b = void 0);
    else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");
    let T = this.length - c;
    if ((b === void 0 || b > T) && (b = T), l.length > 0 && (b < 0 || c < 0) || c > this.length) throw new RangeError("Attempt to write outside buffer bounds");
    A || (A = "utf8");
    let P = false;
    for (; ; ) switch (A) {
      case "hex":
        return j(this, l, c, b);
      case "utf8":
      case "utf-8":
        return Q(this, l, c, b);
      case "ascii":
      case "latin1":
      case "binary":
        return $(this, l, c, b);
      case "base64":
        return te(this, l, c, b);
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
        return pt(this, l, c, b);
      default:
        if (P) throw new TypeError("Unknown encoding: " + A);
        A = ("" + A).toLowerCase(), P = true;
    }
  }, s.prototype.toJSON = function() {
    return { type: "Buffer", data: Array.prototype.slice.call(this._arr || this, 0) };
  };
  function Fe(p, l, c) {
    return l === 0 && c === p.length ? t.fromByteArray(p) : t.fromByteArray(p.slice(l, c));
  }
  function Se(p, l, c) {
    c = Math.min(p.length, c);
    let b = [], A = l;
    for (; A < c; ) {
      let T = p[A], P = null, W = T > 239 ? 4 : T > 223 ? 3 : T > 191 ? 2 : 1;
      if (A + W <= c) {
        let ae, re, le, J;
        switch (W) {
          case 1:
            T < 128 && (P = T);
            break;
          case 2:
            ae = p[A + 1], (ae & 192) === 128 && (J = (T & 31) << 6 | ae & 63, J > 127 && (P = J));
            break;
          case 3:
            ae = p[A + 1], re = p[A + 2], (ae & 192) === 128 && (re & 192) === 128 && (J = (T & 15) << 12 | (ae & 63) << 6 | re & 63, J > 2047 && (J < 55296 || J > 57343) && (P = J));
            break;
          case 4:
            ae = p[A + 1], re = p[A + 2], le = p[A + 3], (ae & 192) === 128 && (re & 192) === 128 && (le & 192) === 128 && (J = (T & 15) << 18 | (ae & 63) << 12 | (re & 63) << 6 | le & 63, J > 65535 && J < 1114112 && (P = J));
        }
      }
      P === null ? (P = 65533, W = 1) : P > 65535 && (P -= 65536, b.push(P >>> 10 & 1023 | 55296), P = 56320 | P & 1023), b.push(P), A += W;
    }
    return vr(b);
  }
  let mr = 4096;
  function vr(p) {
    let l = p.length;
    if (l <= mr) return String.fromCharCode.apply(String, p);
    let c = "", b = 0;
    for (; b < l; ) c += String.fromCharCode.apply(String, p.slice(b, b += mr));
    return c;
  }
  function os(p, l, c) {
    let b = "";
    c = Math.min(p.length, c);
    for (let A = l; A < c; ++A) b += String.fromCharCode(p[A] & 127);
    return b;
  }
  function Mg(p, l, c) {
    let b = "";
    c = Math.min(p.length, c);
    for (let A = l; A < c; ++A) b += String.fromCharCode(p[A]);
    return b;
  }
  function Lg(p, l, c) {
    let b = p.length;
    (!l || l < 0) && (l = 0), (!c || c < 0 || c > b) && (c = b);
    let A = "";
    for (let T = l; T < c; ++T) A += Wg[p[T]];
    return A;
  }
  function qg(p, l, c) {
    let b = p.slice(l, c), A = "";
    for (let T = 0; T < b.length - 1; T += 2) A += String.fromCharCode(b[T] + b[T + 1] * 256);
    return A;
  }
  s.prototype.slice = function(l, c) {
    let b = this.length;
    l = ~~l, c = c === void 0 ? b : ~~c, l < 0 ? (l += b, l < 0 && (l = 0)) : l > b && (l = b), c < 0 ? (c += b, c < 0 && (c = 0)) : c > b && (c = b), c < l && (c = l);
    let A = this.subarray(l, c);
    return Object.setPrototypeOf(A, s.prototype), A;
  };
  function ye(p, l, c) {
    if (p % 1 !== 0 || p < 0) throw new RangeError("offset is not uint");
    if (p + l > c) throw new RangeError("Trying to access beyond buffer length");
  }
  s.prototype.readUintLE = s.prototype.readUIntLE = function(l, c, b) {
    l = l >>> 0, c = c >>> 0, b || ye(l, c, this.length);
    let A = this[l], T = 1, P = 0;
    for (; ++P < c && (T *= 256); ) A += this[l + P] * T;
    return A;
  }, s.prototype.readUintBE = s.prototype.readUIntBE = function(l, c, b) {
    l = l >>> 0, c = c >>> 0, b || ye(l, c, this.length);
    let A = this[l + --c], T = 1;
    for (; c > 0 && (T *= 256); ) A += this[l + --c] * T;
    return A;
  }, s.prototype.readUint8 = s.prototype.readUInt8 = function(l, c) {
    return l = l >>> 0, c || ye(l, 1, this.length), this[l];
  }, s.prototype.readUint16LE = s.prototype.readUInt16LE = function(l, c) {
    return l = l >>> 0, c || ye(l, 2, this.length), this[l] | this[l + 1] << 8;
  }, s.prototype.readUint16BE = s.prototype.readUInt16BE = function(l, c) {
    return l = l >>> 0, c || ye(l, 2, this.length), this[l] << 8 | this[l + 1];
  }, s.prototype.readUint32LE = s.prototype.readUInt32LE = function(l, c) {
    return l = l >>> 0, c || ye(l, 4, this.length), (this[l] | this[l + 1] << 8 | this[l + 2] << 16) + this[l + 3] * 16777216;
  }, s.prototype.readUint32BE = s.prototype.readUInt32BE = function(l, c) {
    return l = l >>> 0, c || ye(l, 4, this.length), this[l] * 16777216 + (this[l + 1] << 16 | this[l + 2] << 8 | this[l + 3]);
  }, s.prototype.readBigUInt64LE = kt(function(l) {
    l = l >>> 0, Sr(l, "offset");
    let c = this[l], b = this[l + 7];
    (c === void 0 || b === void 0) && ni(l, this.length - 8);
    let A = c + this[++l] * 2 ** 8 + this[++l] * 2 ** 16 + this[++l] * 2 ** 24, T = this[++l] + this[++l] * 2 ** 8 + this[++l] * 2 ** 16 + b * 2 ** 24;
    return BigInt(A) + (BigInt(T) << BigInt(32));
  }), s.prototype.readBigUInt64BE = kt(function(l) {
    l = l >>> 0, Sr(l, "offset");
    let c = this[l], b = this[l + 7];
    (c === void 0 || b === void 0) && ni(l, this.length - 8);
    let A = c * 2 ** 24 + this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + this[++l], T = this[++l] * 2 ** 24 + this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + b;
    return (BigInt(A) << BigInt(32)) + BigInt(T);
  }), s.prototype.readIntLE = function(l, c, b) {
    l = l >>> 0, c = c >>> 0, b || ye(l, c, this.length);
    let A = this[l], T = 1, P = 0;
    for (; ++P < c && (T *= 256); ) A += this[l + P] * T;
    return T *= 128, A >= T && (A -= Math.pow(2, 8 * c)), A;
  }, s.prototype.readIntBE = function(l, c, b) {
    l = l >>> 0, c = c >>> 0, b || ye(l, c, this.length);
    let A = c, T = 1, P = this[l + --A];
    for (; A > 0 && (T *= 256); ) P += this[l + --A] * T;
    return T *= 128, P >= T && (P -= Math.pow(2, 8 * c)), P;
  }, s.prototype.readInt8 = function(l, c) {
    return l = l >>> 0, c || ye(l, 1, this.length), this[l] & 128 ? (255 - this[l] + 1) * -1 : this[l];
  }, s.prototype.readInt16LE = function(l, c) {
    l = l >>> 0, c || ye(l, 2, this.length);
    let b = this[l] | this[l + 1] << 8;
    return b & 32768 ? b | 4294901760 : b;
  }, s.prototype.readInt16BE = function(l, c) {
    l = l >>> 0, c || ye(l, 2, this.length);
    let b = this[l + 1] | this[l] << 8;
    return b & 32768 ? b | 4294901760 : b;
  }, s.prototype.readInt32LE = function(l, c) {
    return l = l >>> 0, c || ye(l, 4, this.length), this[l] | this[l + 1] << 8 | this[l + 2] << 16 | this[l + 3] << 24;
  }, s.prototype.readInt32BE = function(l, c) {
    return l = l >>> 0, c || ye(l, 4, this.length), this[l] << 24 | this[l + 1] << 16 | this[l + 2] << 8 | this[l + 3];
  }, s.prototype.readBigInt64LE = kt(function(l) {
    l = l >>> 0, Sr(l, "offset");
    let c = this[l], b = this[l + 7];
    (c === void 0 || b === void 0) && ni(l, this.length - 8);
    let A = this[l + 4] + this[l + 5] * 2 ** 8 + this[l + 6] * 2 ** 16 + (b << 24);
    return (BigInt(A) << BigInt(32)) + BigInt(c + this[++l] * 2 ** 8 + this[++l] * 2 ** 16 + this[++l] * 2 ** 24);
  }), s.prototype.readBigInt64BE = kt(function(l) {
    l = l >>> 0, Sr(l, "offset");
    let c = this[l], b = this[l + 7];
    (c === void 0 || b === void 0) && ni(l, this.length - 8);
    let A = (c << 24) + this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + this[++l];
    return (BigInt(A) << BigInt(32)) + BigInt(this[++l] * 2 ** 24 + this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + b);
  }), s.prototype.readFloatLE = function(l, c) {
    return l = l >>> 0, c || ye(l, 4, this.length), e.read(this, l, true, 23, 4);
  }, s.prototype.readFloatBE = function(l, c) {
    return l = l >>> 0, c || ye(l, 4, this.length), e.read(this, l, false, 23, 4);
  }, s.prototype.readDoubleLE = function(l, c) {
    return l = l >>> 0, c || ye(l, 8, this.length), e.read(this, l, true, 52, 8);
  }, s.prototype.readDoubleBE = function(l, c) {
    return l = l >>> 0, c || ye(l, 8, this.length), e.read(this, l, false, 52, 8);
  };
  function Ce(p, l, c, b, A, T) {
    if (!s.isBuffer(p)) throw new TypeError('"buffer" argument must be a Buffer instance');
    if (l > A || l < T) throw new RangeError('"value" argument is out of bounds');
    if (c + b > p.length) throw new RangeError("Index out of range");
  }
  s.prototype.writeUintLE = s.prototype.writeUIntLE = function(l, c, b, A) {
    if (l = +l, c = c >>> 0, b = b >>> 0, !A) {
      let W = Math.pow(2, 8 * b) - 1;
      Ce(this, l, c, b, W, 0);
    }
    let T = 1, P = 0;
    for (this[c] = l & 255; ++P < b && (T *= 256); ) this[c + P] = l / T & 255;
    return c + b;
  }, s.prototype.writeUintBE = s.prototype.writeUIntBE = function(l, c, b, A) {
    if (l = +l, c = c >>> 0, b = b >>> 0, !A) {
      let W = Math.pow(2, 8 * b) - 1;
      Ce(this, l, c, b, W, 0);
    }
    let T = b - 1, P = 1;
    for (this[c + T] = l & 255; --T >= 0 && (P *= 256); ) this[c + T] = l / P & 255;
    return c + b;
  }, s.prototype.writeUint8 = s.prototype.writeUInt8 = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 1, 255, 0), this[c] = l & 255, c + 1;
  }, s.prototype.writeUint16LE = s.prototype.writeUInt16LE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 2, 65535, 0), this[c] = l & 255, this[c + 1] = l >>> 8, c + 2;
  }, s.prototype.writeUint16BE = s.prototype.writeUInt16BE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 2, 65535, 0), this[c] = l >>> 8, this[c + 1] = l & 255, c + 2;
  }, s.prototype.writeUint32LE = s.prototype.writeUInt32LE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 4, 4294967295, 0), this[c + 3] = l >>> 24, this[c + 2] = l >>> 16, this[c + 1] = l >>> 8, this[c] = l & 255, c + 4;
  }, s.prototype.writeUint32BE = s.prototype.writeUInt32BE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 4, 4294967295, 0), this[c] = l >>> 24, this[c + 1] = l >>> 16, this[c + 2] = l >>> 8, this[c + 3] = l & 255, c + 4;
  };
  function yl(p, l, c, b, A) {
    El(l, b, A, p, c, 7);
    let T = Number(l & BigInt(4294967295));
    p[c++] = T, T = T >> 8, p[c++] = T, T = T >> 8, p[c++] = T, T = T >> 8, p[c++] = T;
    let P = Number(l >> BigInt(32) & BigInt(4294967295));
    return p[c++] = P, P = P >> 8, p[c++] = P, P = P >> 8, p[c++] = P, P = P >> 8, p[c++] = P, c;
  }
  function bl(p, l, c, b, A) {
    El(l, b, A, p, c, 7);
    let T = Number(l & BigInt(4294967295));
    p[c + 7] = T, T = T >> 8, p[c + 6] = T, T = T >> 8, p[c + 5] = T, T = T >> 8, p[c + 4] = T;
    let P = Number(l >> BigInt(32) & BigInt(4294967295));
    return p[c + 3] = P, P = P >> 8, p[c + 2] = P, P = P >> 8, p[c + 1] = P, P = P >> 8, p[c] = P, c + 8;
  }
  s.prototype.writeBigUInt64LE = kt(function(l, c = 0) {
    return yl(this, l, c, BigInt(0), BigInt("0xffffffffffffffff"));
  }), s.prototype.writeBigUInt64BE = kt(function(l, c = 0) {
    return bl(this, l, c, BigInt(0), BigInt("0xffffffffffffffff"));
  }), s.prototype.writeIntLE = function(l, c, b, A) {
    if (l = +l, c = c >>> 0, !A) {
      let ae = Math.pow(2, 8 * b - 1);
      Ce(this, l, c, b, ae - 1, -ae);
    }
    let T = 0, P = 1, W = 0;
    for (this[c] = l & 255; ++T < b && (P *= 256); ) l < 0 && W === 0 && this[c + T - 1] !== 0 && (W = 1), this[c + T] = (l / P >> 0) - W & 255;
    return c + b;
  }, s.prototype.writeIntBE = function(l, c, b, A) {
    if (l = +l, c = c >>> 0, !A) {
      let ae = Math.pow(2, 8 * b - 1);
      Ce(this, l, c, b, ae - 1, -ae);
    }
    let T = b - 1, P = 1, W = 0;
    for (this[c + T] = l & 255; --T >= 0 && (P *= 256); ) l < 0 && W === 0 && this[c + T + 1] !== 0 && (W = 1), this[c + T] = (l / P >> 0) - W & 255;
    return c + b;
  }, s.prototype.writeInt8 = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 1, 127, -128), l < 0 && (l = 255 + l + 1), this[c] = l & 255, c + 1;
  }, s.prototype.writeInt16LE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 2, 32767, -32768), this[c] = l & 255, this[c + 1] = l >>> 8, c + 2;
  }, s.prototype.writeInt16BE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 2, 32767, -32768), this[c] = l >>> 8, this[c + 1] = l & 255, c + 2;
  }, s.prototype.writeInt32LE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 4, 2147483647, -2147483648), this[c] = l & 255, this[c + 1] = l >>> 8, this[c + 2] = l >>> 16, this[c + 3] = l >>> 24, c + 4;
  }, s.prototype.writeInt32BE = function(l, c, b) {
    return l = +l, c = c >>> 0, b || Ce(this, l, c, 4, 2147483647, -2147483648), l < 0 && (l = 4294967295 + l + 1), this[c] = l >>> 24, this[c + 1] = l >>> 16, this[c + 2] = l >>> 8, this[c + 3] = l & 255, c + 4;
  }, s.prototype.writeBigInt64LE = kt(function(l, c = 0) {
    return yl(this, l, c, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
  }), s.prototype.writeBigInt64BE = kt(function(l, c = 0) {
    return bl(this, l, c, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
  });
  function wl(p, l, c, b, A, T) {
    if (c + b > p.length) throw new RangeError("Index out of range");
    if (c < 0) throw new RangeError("Index out of range");
  }
  function _l(p, l, c, b, A) {
    return l = +l, c = c >>> 0, A || wl(p, l, c, 4), e.write(p, l, c, b, 23, 4), c + 4;
  }
  s.prototype.writeFloatLE = function(l, c, b) {
    return _l(this, l, c, true, b);
  }, s.prototype.writeFloatBE = function(l, c, b) {
    return _l(this, l, c, false, b);
  };
  function ml(p, l, c, b, A) {
    return l = +l, c = c >>> 0, A || wl(p, l, c, 8), e.write(p, l, c, b, 52, 8), c + 8;
  }
  s.prototype.writeDoubleLE = function(l, c, b) {
    return ml(this, l, c, true, b);
  }, s.prototype.writeDoubleBE = function(l, c, b) {
    return ml(this, l, c, false, b);
  }, s.prototype.copy = function(l, c, b, A) {
    if (!s.isBuffer(l)) throw new TypeError("argument should be a Buffer");
    if (b || (b = 0), !A && A !== 0 && (A = this.length), c >= l.length && (c = l.length), c || (c = 0), A > 0 && A < b && (A = b), A === b || l.length === 0 || this.length === 0) return 0;
    if (c < 0) throw new RangeError("targetStart out of bounds");
    if (b < 0 || b >= this.length) throw new RangeError("Index out of range");
    if (A < 0) throw new RangeError("sourceEnd out of bounds");
    A > this.length && (A = this.length), l.length - c < A - b && (A = l.length - c + b);
    let T = A - b;
    return this === l && typeof Uint8Array.prototype.copyWithin == "function" ? this.copyWithin(c, b, A) : Uint8Array.prototype.set.call(l, this.subarray(b, A), c), T;
  }, s.prototype.fill = function(l, c, b, A) {
    if (typeof l == "string") {
      if (typeof c == "string" ? (A = c, c = 0, b = this.length) : typeof b == "string" && (A = b, b = this.length), A !== void 0 && typeof A != "string") throw new TypeError("encoding must be a string");
      if (typeof A == "string" && !s.isEncoding(A)) throw new TypeError("Unknown encoding: " + A);
      if (l.length === 1) {
        let P = l.charCodeAt(0);
        (A === "utf8" && P < 128 || A === "latin1") && (l = P);
      }
    } else typeof l == "number" ? l = l & 255 : typeof l == "boolean" && (l = Number(l));
    if (c < 0 || this.length < c || this.length < b) throw new RangeError("Out of range index");
    if (b <= c) return this;
    c = c >>> 0, b = b === void 0 ? this.length : b >>> 0, l || (l = 0);
    let T;
    if (typeof l == "number") for (T = c; T < b; ++T) this[T] = l;
    else {
      let P = s.isBuffer(l) ? l : s.from(l, A), W = P.length;
      if (W === 0) throw new TypeError('The value "' + l + '" is invalid for argument "value"');
      for (T = 0; T < b - c; ++T) this[T + c] = P[T % W];
    }
    return this;
  };
  let Er = {};
  function as(p, l, c) {
    Er[p] = class extends c {
      constructor() {
        super(), Object.defineProperty(this, "message", { value: l.apply(this, arguments), writable: true, configurable: true }), this.name = `${this.name} [${p}]`, this.stack, delete this.name;
      }
      get code() {
        return p;
      }
      set code(A) {
        Object.defineProperty(this, "code", { configurable: true, enumerable: true, value: A, writable: true });
      }
      toString() {
        return `${this.name} [${p}]: ${this.message}`;
      }
    };
  }
  as("ERR_BUFFER_OUT_OF_BOUNDS", function(p) {
    return p ? `${p} is outside of buffer bounds` : "Attempt to access memory outside buffer bounds";
  }, RangeError), as("ERR_INVALID_ARG_TYPE", function(p, l) {
    return `The "${p}" argument must be of type number. Received type ${typeof l}`;
  }, TypeError), as("ERR_OUT_OF_RANGE", function(p, l, c) {
    let b = `The value of "${p}" is out of range.`, A = c;
    return Number.isInteger(c) && Math.abs(c) > 2 ** 32 ? A = vl(String(c)) : typeof c == "bigint" && (A = String(c), (c > BigInt(2) ** BigInt(32) || c < -(BigInt(2) ** BigInt(32))) && (A = vl(A)), A += "n"), b += ` It must be ${l}. Received ${A}`, b;
  }, RangeError);
  function vl(p) {
    let l = "", c = p.length, b = p[0] === "-" ? 1 : 0;
    for (; c >= b + 4; c -= 3) l = `_${p.slice(c - 3, c)}${l}`;
    return `${p.slice(0, c)}${l}`;
  }
  function Ug(p, l, c) {
    Sr(l, "offset"), (p[l] === void 0 || p[l + c] === void 0) && ni(l, p.length - (c + 1));
  }
  function El(p, l, c, b, A, T) {
    if (p > c || p < l) {
      let P = typeof l == "bigint" ? "n" : "", W;
      throw T > 3 ? l === 0 || l === BigInt(0) ? W = `>= 0${P} and < 2${P} ** ${(T + 1) * 8}${P}` : W = `>= -(2${P} ** ${(T + 1) * 8 - 1}${P}) and < 2 ** ${(T + 1) * 8 - 1}${P}` : W = `>= ${l}${P} and <= ${c}${P}`, new Er.ERR_OUT_OF_RANGE("value", W, p);
    }
    Ug(b, A, T);
  }
  function Sr(p, l) {
    if (typeof p != "number") throw new Er.ERR_INVALID_ARG_TYPE(l, "number", p);
  }
  function ni(p, l, c) {
    throw Math.floor(p) !== p ? (Sr(p, c), new Er.ERR_OUT_OF_RANGE(c || "offset", "an integer", p)) : l < 0 ? new Er.ERR_BUFFER_OUT_OF_BOUNDS() : new Er.ERR_OUT_OF_RANGE(c || "offset", `>= ${c ? 1 : 0} and <= ${l}`, p);
  }
  let Ng = /[^+/0-9A-Za-z-_]/g;
  function Dg(p) {
    if (p = p.split("=")[0], p = p.trim().replace(Ng, ""), p.length < 2) return "";
    for (; p.length % 4 !== 0; ) p = p + "=";
    return p;
  }
  function ls(p, l) {
    l = l || 1 / 0;
    let c, b = p.length, A = null, T = [];
    for (let P = 0; P < b; ++P) {
      if (c = p.charCodeAt(P), c > 55295 && c < 57344) {
        if (!A) {
          if (c > 56319) {
            (l -= 3) > -1 && T.push(239, 191, 189);
            continue;
          } else if (P + 1 === b) {
            (l -= 3) > -1 && T.push(239, 191, 189);
            continue;
          }
          A = c;
          continue;
        }
        if (c < 56320) {
          (l -= 3) > -1 && T.push(239, 191, 189), A = c;
          continue;
        }
        c = (A - 55296 << 10 | c - 56320) + 65536;
      } else A && (l -= 3) > -1 && T.push(239, 191, 189);
      if (A = null, c < 128) {
        if ((l -= 1) < 0) break;
        T.push(c);
      } else if (c < 2048) {
        if ((l -= 2) < 0) break;
        T.push(c >> 6 | 192, c & 63 | 128);
      } else if (c < 65536) {
        if ((l -= 3) < 0) break;
        T.push(c >> 12 | 224, c >> 6 & 63 | 128, c & 63 | 128);
      } else if (c < 1114112) {
        if ((l -= 4) < 0) break;
        T.push(c >> 18 | 240, c >> 12 & 63 | 128, c >> 6 & 63 | 128, c & 63 | 128);
      } else throw new Error("Invalid code point");
    }
    return T;
  }
  function jg(p) {
    let l = [];
    for (let c = 0; c < p.length; ++c) l.push(p.charCodeAt(c) & 255);
    return l;
  }
  function Fg(p, l) {
    let c, b, A, T = [];
    for (let P = 0; P < p.length && !((l -= 2) < 0); ++P) c = p.charCodeAt(P), b = c >> 8, A = c % 256, T.push(A), T.push(b);
    return T;
  }
  function Sl(p) {
    return t.toByteArray(Dg(p));
  }
  function Ni(p, l, c, b) {
    let A;
    for (A = 0; A < b && !(A + c >= l.length || A >= p.length); ++A) l[A + c] = p[A];
    return A;
  }
  function Ge(p, l) {
    return p instanceof l || p != null && p.constructor != null && p.constructor.name != null && p.constructor.name === l.name;
  }
  function us(p) {
    return p !== p;
  }
  let Wg = function() {
    let p = "0123456789abcdef", l = new Array(256);
    for (let c = 0; c < 16; ++c) {
      let b = c * 16;
      for (let A = 0; A < 16; ++A) l[b + A] = p[c] + p[A];
    }
    return l;
  }();
  function kt(p) {
    return typeof BigInt > "u" ? $g : p;
  }
  function $g() {
    throw new Error("BigInt not supported");
  }
  return Gt;
}
var si;
var ju;
var Wi;
var Fu;
var Gt;
var Wu;
var Bt;
var x;
var Xg;
var Zg;
var he = Ae(() => {
  _();
  v();
  m();
  si = {}, ju = false;
  Wi = {}, Fu = false;
  Gt = {}, Wu = false;
  Bt = Jg();
  Bt.Buffer;
  Bt.SlowBuffer;
  Bt.INSPECT_MAX_BYTES;
  Bt.kMaxLength;
  x = Bt.Buffer, Xg = Bt.INSPECT_MAX_BYTES, Zg = Bt.kMaxLength;
});
var v = Ae(() => {
  he();
});
var ie = O((LA, $u) => {
  "use strict";
  _();
  v();
  m();
  var ps = class extends Error {
    constructor(e) {
      if (!Array.isArray(e)) throw new TypeError(`Expected input to be an Array, got ${typeof e}`);
      let r = "";
      for (let i = 0; i < e.length; i++) r += `    ${e[i].stack}
`;
      super(r), this.name = "AggregateError", this.errors = e;
    }
  };
  $u.exports = { AggregateError: ps, ArrayIsArray(t) {
    return Array.isArray(t);
  }, ArrayPrototypeIncludes(t, e) {
    return t.includes(e);
  }, ArrayPrototypeIndexOf(t, e) {
    return t.indexOf(e);
  }, ArrayPrototypeJoin(t, e) {
    return t.join(e);
  }, ArrayPrototypeMap(t, e) {
    return t.map(e);
  }, ArrayPrototypePop(t, e) {
    return t.pop(e);
  }, ArrayPrototypePush(t, e) {
    return t.push(e);
  }, ArrayPrototypeSlice(t, e, r) {
    return t.slice(e, r);
  }, Error, FunctionPrototypeCall(t, e, ...r) {
    return t.call(e, ...r);
  }, FunctionPrototypeSymbolHasInstance(t, e) {
    return Function.prototype[Symbol.hasInstance].call(t, e);
  }, MathFloor: Math.floor, Number, NumberIsInteger: Number.isInteger, NumberIsNaN: Number.isNaN, NumberMAX_SAFE_INTEGER: Number.MAX_SAFE_INTEGER, NumberMIN_SAFE_INTEGER: Number.MIN_SAFE_INTEGER, NumberParseInt: Number.parseInt, ObjectDefineProperties(t, e) {
    return Object.defineProperties(t, e);
  }, ObjectDefineProperty(t, e, r) {
    return Object.defineProperty(t, e, r);
  }, ObjectGetOwnPropertyDescriptor(t, e) {
    return Object.getOwnPropertyDescriptor(t, e);
  }, ObjectKeys(t) {
    return Object.keys(t);
  }, ObjectSetPrototypeOf(t, e) {
    return Object.setPrototypeOf(t, e);
  }, Promise, PromisePrototypeCatch(t, e) {
    return t.catch(e);
  }, PromisePrototypeThen(t, e, r) {
    return t.then(e, r);
  }, PromiseReject(t) {
    return Promise.reject(t);
  }, PromiseResolve(t) {
    return Promise.resolve(t);
  }, ReflectApply: Reflect.apply, RegExpPrototypeTest(t, e) {
    return t.test(e);
  }, SafeSet: Set, String, StringPrototypeSlice(t, e, r) {
    return t.slice(e, r);
  }, StringPrototypeToLowerCase(t) {
    return t.toLowerCase();
  }, StringPrototypeToUpperCase(t) {
    return t.toUpperCase();
  }, StringPrototypeTrim(t) {
    return t.trim();
  }, Symbol, SymbolFor: Symbol.for, SymbolAsyncIterator: Symbol.asyncIterator, SymbolHasInstance: Symbol.hasInstance, SymbolIterator: Symbol.iterator, SymbolDispose: Symbol.dispose || Symbol("Symbol.dispose"), SymbolAsyncDispose: Symbol.asyncDispose || Symbol("Symbol.asyncDispose"), TypedArrayPrototypeSet(t, e, r) {
    return t.set(e, r);
  }, Boolean, Uint8Array };
});
var gs = O((VA, Hu) => {
  "use strict";
  _();
  v();
  m();
  Hu.exports = { format(t, ...e) {
    return t.replace(/%([sdifj])/g, function(...[r, i]) {
      let n = e.shift();
      return i === "f" ? n.toFixed(6) : i === "j" ? JSON.stringify(n) : i === "s" && typeof n == "object" ? `${n.constructor !== Object ? n.constructor.name : ""} {}`.trim() : n.toString();
    });
  }, inspect(t) {
    switch (typeof t) {
      case "string":
        if (t.includes("'")) if (t.includes('"')) {
          if (!t.includes("`") && !t.includes("${")) return `\`${t}\``;
        } else return `"${t}"`;
        return `'${t}'`;
      case "number":
        return isNaN(t) ? "NaN" : Object.is(t, -0) ? String(t) : t;
      case "bigint":
        return `${String(t)}n`;
      case "boolean":
      case "undefined":
        return String(t);
      case "object":
        return "{}";
    }
  } };
});
var me = O((tI, Ku) => {
  "use strict";
  _();
  v();
  m();
  var { format: ey, inspect: $i } = gs(), { AggregateError: ty } = ie(), ry = globalThis.AggregateError || ty, iy = Symbol("kIsNodeError"), ny = ["string", "function", "number", "object", "Function", "Object", "boolean", "bigint", "symbol"], sy = /^([A-Z][a-z0-9]*)+$/, oy = "__node_internal_", Hi = {};
  function Yt(t, e) {
    if (!t) throw new Hi.ERR_INTERNAL_ASSERTION(e);
  }
  function Vu(t) {
    let e = "", r = t.length, i = t[0] === "-" ? 1 : 0;
    for (; r >= i + 4; r -= 3) e = `_${t.slice(r - 3, r)}${e}`;
    return `${t.slice(0, r)}${e}`;
  }
  function ay(t, e, r) {
    if (typeof e == "function") return Yt(e.length <= r.length, `Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${e.length}).`), e(...r);
    let i = (e.match(/%[dfijoOs]/g) || []).length;
    return Yt(i === r.length, `Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${i}).`), r.length === 0 ? e : ey(e, ...r);
  }
  function we(t, e, r) {
    r || (r = Error);
    class i extends r {
      constructor(...o) {
        super(ay(t, e, o));
      }
      toString() {
        return `${this.name} [${t}]: ${this.message}`;
      }
    }
    Object.defineProperties(i.prototype, { name: { value: r.name, writable: true, enumerable: false, configurable: true }, toString: { value() {
      return `${this.name} [${t}]: ${this.message}`;
    }, writable: true, enumerable: false, configurable: true } }), i.prototype.code = t, i.prototype[iy] = true, Hi[t] = i;
  }
  function zu(t) {
    let e = oy + t.name;
    return Object.defineProperty(t, "name", { value: e }), t;
  }
  function ly(t, e) {
    if (t && e && t !== e) {
      if (Array.isArray(e.errors)) return e.errors.push(t), e;
      let r = new ry([e, t], e.message);
      return r.code = e.code, r;
    }
    return t || e;
  }
  var ys = class extends Error {
    constructor(e = "The operation was aborted", r = void 0) {
      if (r !== void 0 && typeof r != "object") throw new Hi.ERR_INVALID_ARG_TYPE("options", "Object", r);
      super(e, r), this.code = "ABORT_ERR", this.name = "AbortError";
    }
  };
  we("ERR_ASSERTION", "%s", Error);
  we("ERR_INVALID_ARG_TYPE", (t, e, r) => {
    Yt(typeof t == "string", "'name' must be a string"), Array.isArray(e) || (e = [e]);
    let i = "The ";
    t.endsWith(" argument") ? i += `${t} ` : i += `"${t}" ${t.includes(".") ? "property" : "argument"} `, i += "must be ";
    let n = [], o = [], s = [];
    for (let u of e) Yt(typeof u == "string", "All expected entries have to be of type string"), ny.includes(u) ? n.push(u.toLowerCase()) : sy.test(u) ? o.push(u) : (Yt(u !== "object", 'The value "object" should be written as "Object"'), s.push(u));
    if (o.length > 0) {
      let u = n.indexOf("object");
      u !== -1 && (n.splice(n, u, 1), o.push("Object"));
    }
    if (n.length > 0) {
      switch (n.length) {
        case 1:
          i += `of type ${n[0]}`;
          break;
        case 2:
          i += `one of type ${n[0]} or ${n[1]}`;
          break;
        default: {
          let u = n.pop();
          i += `one of type ${n.join(", ")}, or ${u}`;
        }
      }
      (o.length > 0 || s.length > 0) && (i += " or ");
    }
    if (o.length > 0) {
      switch (o.length) {
        case 1:
          i += `an instance of ${o[0]}`;
          break;
        case 2:
          i += `an instance of ${o[0]} or ${o[1]}`;
          break;
        default: {
          let u = o.pop();
          i += `an instance of ${o.join(", ")}, or ${u}`;
        }
      }
      s.length > 0 && (i += " or ");
    }
    switch (s.length) {
      case 0:
        break;
      case 1:
        s[0].toLowerCase() !== s[0] && (i += "an "), i += `${s[0]}`;
        break;
      case 2:
        i += `one of ${s[0]} or ${s[1]}`;
        break;
      default: {
        let u = s.pop();
        i += `one of ${s.join(", ")}, or ${u}`;
      }
    }
    if (r == null) i += `. Received ${r}`;
    else if (typeof r == "function" && r.name) i += `. Received function ${r.name}`;
    else if (typeof r == "object") {
      var a;
      if ((a = r.constructor) !== null && a !== void 0 && a.name) i += `. Received an instance of ${r.constructor.name}`;
      else {
        let u = $i(r, { depth: -1 });
        i += `. Received ${u}`;
      }
    } else {
      let u = $i(r, { colors: false });
      u.length > 25 && (u = `${u.slice(0, 25)}...`), i += `. Received type ${typeof r} (${u})`;
    }
    return i;
  }, TypeError);
  we("ERR_INVALID_ARG_VALUE", (t, e, r = "is invalid") => {
    let i = $i(e);
    return i.length > 128 && (i = i.slice(0, 128) + "..."), `The ${t.includes(".") ? "property" : "argument"} '${t}' ${r}. Received ${i}`;
  }, TypeError);
  we("ERR_INVALID_RETURN_VALUE", (t, e, r) => {
    var i;
    let n = r != null && (i = r.constructor) !== null && i !== void 0 && i.name ? `instance of ${r.constructor.name}` : `type ${typeof r}`;
    return `Expected ${t} to be returned from the "${e}" function but got ${n}.`;
  }, TypeError);
  we("ERR_MISSING_ARGS", (...t) => {
    Yt(t.length > 0, "At least one arg needs to be specified");
    let e, r = t.length;
    switch (t = (Array.isArray(t) ? t : [t]).map((i) => `"${i}"`).join(" or "), r) {
      case 1:
        e += `The ${t[0]} argument`;
        break;
      case 2:
        e += `The ${t[0]} and ${t[1]} arguments`;
        break;
      default:
        {
          let i = t.pop();
          e += `The ${t.join(", ")}, and ${i} arguments`;
        }
        break;
    }
    return `${e} must be specified`;
  }, TypeError);
  we("ERR_OUT_OF_RANGE", (t, e, r) => {
    Yt(e, 'Missing "range" argument');
    let i;
    if (Number.isInteger(r) && Math.abs(r) > 2 ** 32) i = Vu(String(r));
    else if (typeof r == "bigint") {
      i = String(r);
      let n = BigInt(2) ** BigInt(32);
      (r > n || r < -n) && (i = Vu(i)), i += "n";
    } else i = $i(r);
    return `The value of "${t}" is out of range. It must be ${e}. Received ${i}`;
  }, RangeError);
  we("ERR_MULTIPLE_CALLBACK", "Callback called multiple times", Error);
  we("ERR_METHOD_NOT_IMPLEMENTED", "The %s method is not implemented", Error);
  we("ERR_STREAM_ALREADY_FINISHED", "Cannot call %s after a stream was finished", Error);
  we("ERR_STREAM_CANNOT_PIPE", "Cannot pipe, not readable", Error);
  we("ERR_STREAM_DESTROYED", "Cannot call %s after a stream was destroyed", Error);
  we("ERR_STREAM_NULL_VALUES", "May not write null values to stream", TypeError);
  we("ERR_STREAM_PREMATURE_CLOSE", "Premature close", Error);
  we("ERR_STREAM_PUSH_AFTER_EOF", "stream.push() after EOF", Error);
  we("ERR_STREAM_UNSHIFT_AFTER_END_EVENT", "stream.unshift() after end event", Error);
  we("ERR_STREAM_WRITE_AFTER_END", "write after end", Error);
  we("ERR_UNKNOWN_ENCODING", "Unknown encoding: %s", TypeError);
  Ku.exports = { AbortError: ys, aggregateTwoErrors: zu(ly), hideStackFrames: zu, codes: Hi };
});
var Tr = O((fI, Vi) => {
  "use strict";
  _();
  v();
  m();
  var { AbortController: Qu, AbortSignal: uy } = typeof self < "u" ? self : typeof window < "u" ? window : void 0;
  Vi.exports = Qu;
  Vi.exports.AbortSignal = uy;
  Vi.exports.default = Qu;
});
function Y() {
  Y.init.call(this);
}
function zi(t) {
  if (typeof t != "function") throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof t);
}
function nc(t) {
  return t._maxListeners === void 0 ? Y.defaultMaxListeners : t._maxListeners;
}
function Xu(t, e, r, i) {
  var n, o, s, a;
  if (zi(r), (o = t._events) === void 0 ? (o = t._events = /* @__PURE__ */ Object.create(null), t._eventsCount = 0) : (o.newListener !== void 0 && (t.emit("newListener", e, r.listener ? r.listener : r), o = t._events), s = o[e]), s === void 0) s = o[e] = r, ++t._eventsCount;
  else if (typeof s == "function" ? s = o[e] = i ? [r, s] : [s, r] : i ? s.unshift(r) : s.push(r), (n = nc(t)) > 0 && s.length > n && !s.warned) {
    s.warned = true;
    var u = new Error("Possible EventEmitter memory leak detected. " + s.length + " " + String(e) + " listeners added. Use emitter.setMaxListeners() to increase limit");
    u.name = "MaxListenersExceededWarning", u.emitter = t, u.type = e, u.count = s.length, a = u, console && console.warn && console.warn(a);
  }
  return t;
}
function cy() {
  if (!this.fired) return this.target.removeListener(this.type, this.wrapFn), this.fired = true, arguments.length === 0 ? this.listener.call(this.target) : this.listener.apply(this.target, arguments);
}
function Zu(t, e, r) {
  var i = { fired: false, wrapFn: void 0, target: t, type: e, listener: r }, n = cy.bind(i);
  return n.listener = r, i.wrapFn = n, n;
}
function ec(t, e, r) {
  var i = t._events;
  if (i === void 0) return [];
  var n = i[e];
  return n === void 0 ? [] : typeof n == "function" ? r ? [n.listener || n] : [n] : r ? function(o) {
    for (var s = new Array(o.length), a = 0; a < s.length; ++a) s[a] = o[a].listener || o[a];
    return s;
  }(n) : sc(n, n.length);
}
function tc(t) {
  var e = this._events;
  if (e !== void 0) {
    var r = e[t];
    if (typeof r == "function") return 1;
    if (r !== void 0) return r.length;
  }
  return 0;
}
function sc(t, e) {
  for (var r = new Array(e), i = 0; i < e; ++i) r[i] = t[i];
  return r;
}
var rc;
var ic;
var Rr;
var Gu;
var Yu;
var Ju;
var ke;
var bs = Ae(() => {
  _();
  v();
  m();
  Rr = typeof Reflect == "object" ? Reflect : null, Gu = Rr && typeof Rr.apply == "function" ? Rr.apply : function(t, e, r) {
    return Function.prototype.apply.call(t, e, r);
  };
  ic = Rr && typeof Rr.ownKeys == "function" ? Rr.ownKeys : Object.getOwnPropertySymbols ? function(t) {
    return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t));
  } : function(t) {
    return Object.getOwnPropertyNames(t);
  };
  Yu = Number.isNaN || function(t) {
    return t != t;
  };
  rc = Y, Y.EventEmitter = Y, Y.prototype._events = void 0, Y.prototype._eventsCount = 0, Y.prototype._maxListeners = void 0;
  Ju = 10;
  Object.defineProperty(Y, "defaultMaxListeners", { enumerable: true, get: function() {
    return Ju;
  }, set: function(t) {
    if (typeof t != "number" || t < 0 || Yu(t)) throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + t + ".");
    Ju = t;
  } }), Y.init = function() {
    this._events !== void 0 && this._events !== Object.getPrototypeOf(this)._events || (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0), this._maxListeners = this._maxListeners || void 0;
  }, Y.prototype.setMaxListeners = function(t) {
    if (typeof t != "number" || t < 0 || Yu(t)) throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + t + ".");
    return this._maxListeners = t, this;
  }, Y.prototype.getMaxListeners = function() {
    return nc(this);
  }, Y.prototype.emit = function(t) {
    for (var e = [], r = 1; r < arguments.length; r++) e.push(arguments[r]);
    var i = t === "error", n = this._events;
    if (n !== void 0) i = i && n.error === void 0;
    else if (!i) return false;
    if (i) {
      var o;
      if (e.length > 0 && (o = e[0]), o instanceof Error) throw o;
      var s = new Error("Unhandled error." + (o ? " (" + o.message + ")" : ""));
      throw s.context = o, s;
    }
    var a = n[t];
    if (a === void 0) return false;
    if (typeof a == "function") Gu(a, this, e);
    else {
      var u = a.length, f = sc(a, u);
      for (r = 0; r < u; ++r) Gu(f[r], this, e);
    }
    return true;
  }, Y.prototype.addListener = function(t, e) {
    return Xu(this, t, e, false);
  }, Y.prototype.on = Y.prototype.addListener, Y.prototype.prependListener = function(t, e) {
    return Xu(this, t, e, true);
  }, Y.prototype.once = function(t, e) {
    return zi(e), this.on(t, Zu(this, t, e)), this;
  }, Y.prototype.prependOnceListener = function(t, e) {
    return zi(e), this.prependListener(t, Zu(this, t, e)), this;
  }, Y.prototype.removeListener = function(t, e) {
    var r, i, n, o, s;
    if (zi(e), (i = this._events) === void 0) return this;
    if ((r = i[t]) === void 0) return this;
    if (r === e || r.listener === e) --this._eventsCount == 0 ? this._events = /* @__PURE__ */ Object.create(null) : (delete i[t], i.removeListener && this.emit("removeListener", t, r.listener || e));
    else if (typeof r != "function") {
      for (n = -1, o = r.length - 1; o >= 0; o--) if (r[o] === e || r[o].listener === e) {
        s = r[o].listener, n = o;
        break;
      }
      if (n < 0) return this;
      n === 0 ? r.shift() : function(a, u) {
        for (; u + 1 < a.length; u++) a[u] = a[u + 1];
        a.pop();
      }(r, n), r.length === 1 && (i[t] = r[0]), i.removeListener !== void 0 && this.emit("removeListener", t, s || e);
    }
    return this;
  }, Y.prototype.off = Y.prototype.removeListener, Y.prototype.removeAllListeners = function(t) {
    var e, r, i;
    if ((r = this._events) === void 0) return this;
    if (r.removeListener === void 0) return arguments.length === 0 ? (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0) : r[t] !== void 0 && (--this._eventsCount == 0 ? this._events = /* @__PURE__ */ Object.create(null) : delete r[t]), this;
    if (arguments.length === 0) {
      var n, o = Object.keys(r);
      for (i = 0; i < o.length; ++i) (n = o[i]) !== "removeListener" && this.removeAllListeners(n);
      return this.removeAllListeners("removeListener"), this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0, this;
    }
    if (typeof (e = r[t]) == "function") this.removeListener(t, e);
    else if (e !== void 0) for (i = e.length - 1; i >= 0; i--) this.removeListener(t, e[i]);
    return this;
  }, Y.prototype.listeners = function(t) {
    return ec(this, t, true);
  }, Y.prototype.rawListeners = function(t) {
    return ec(this, t, false);
  }, Y.listenerCount = function(t, e) {
    return typeof t.listenerCount == "function" ? t.listenerCount(e) : tc.call(t, e);
  }, Y.prototype.listenerCount = tc, Y.prototype.eventNames = function() {
    return this._eventsCount > 0 ? ic(this._events) : [];
  };
  ke = rc;
  ke.EventEmitter;
  ke.defaultMaxListeners;
  ke.init;
  ke.listenerCount;
  ke.EventEmitter;
  ke.defaultMaxListeners;
  ke.init;
  ke.listenerCount;
});
var xt = {};
Ar(xt, { EventEmitter: () => fy, default: () => ke, defaultMaxListeners: () => hy, init: () => dy, listenerCount: () => py, on: () => gy, once: () => yy });
var fy;
var hy;
var dy;
var py;
var gy;
var yy;
var Ot = Ae(() => {
  _();
  v();
  m();
  bs();
  bs();
  ke.once = function(t, e) {
    return new Promise((r, i) => {
      function n(...s) {
        o !== void 0 && t.removeListener("error", o), r(s);
      }
      let o;
      e !== "error" && (o = (s) => {
        t.removeListener(name, n), i(s);
      }, t.once("error", o)), t.once(e, n);
    });
  };
  ke.on = function(t, e) {
    let r = [], i = [], n = null, o = false, s = { async next() {
      let f = r.shift();
      if (f) return createIterResult(f, false);
      if (n) {
        let d = Promise.reject(n);
        return n = null, d;
      }
      return o ? createIterResult(void 0, true) : new Promise((d, h) => i.push({ resolve: d, reject: h }));
    }, async return() {
      t.removeListener(e, a), t.removeListener("error", u), o = true;
      for (let f of i) f.resolve(createIterResult(void 0, true));
      return createIterResult(void 0, true);
    }, throw(f) {
      n = f, t.removeListener(e, a), t.removeListener("error", u);
    }, [Symbol.asyncIterator]() {
      return this;
    } };
    return t.on(e, a), t.on("error", u), s;
    function a(...f) {
      let d = i.shift();
      d ? d.resolve(createIterResult(f, false)) : r.push(f);
    }
    function u(f) {
      o = true;
      let d = i.shift();
      d ? d.reject(f) : n = f, s.return();
    }
  };
  ({ EventEmitter: fy, defaultMaxListeners: hy, init: dy, listenerCount: py, on: gy, once: yy } = ke);
});
var Ie = O((WI, _s) => {
  "use strict";
  _();
  v();
  m();
  var by = (he(), G(be)), { format: wy, inspect: _y } = gs(), { codes: { ERR_INVALID_ARG_TYPE: ws } } = me(), { kResistStopPropagation: my, AggregateError: vy, SymbolDispose: Ey } = ie(), Sy = globalThis.AbortSignal || Tr().AbortSignal, Ay = globalThis.AbortController || Tr().AbortController, Iy = Object.getPrototypeOf(async function() {
  }).constructor, oc = globalThis.Blob || by.Blob, Ty = typeof oc < "u" ? function(e) {
    return e instanceof oc;
  } : function(e) {
    return false;
  }, ac = (t, e) => {
    if (t !== void 0 && (t === null || typeof t != "object" || !("aborted" in t))) throw new ws(e, "AbortSignal", t);
  }, Ry = (t, e) => {
    if (typeof t != "function") throw new ws(e, "Function", t);
  };
  _s.exports = { AggregateError: vy, kEmptyObject: Object.freeze({}), once(t) {
    let e = false;
    return function(...r) {
      e || (e = true, t.apply(this, r));
    };
  }, createDeferredPromise: function() {
    let t, e;
    return { promise: new Promise((i, n) => {
      t = i, e = n;
    }), resolve: t, reject: e };
  }, promisify(t) {
    return new Promise((e, r) => {
      t((i, ...n) => i ? r(i) : e(...n));
    });
  }, debuglog() {
    return function() {
    };
  }, format: wy, inspect: _y, types: { isAsyncFunction(t) {
    return t instanceof Iy;
  }, isArrayBufferView(t) {
    return ArrayBuffer.isView(t);
  } }, isBlob: Ty, deprecate(t, e) {
    return t;
  }, addAbortListener: (Ot(), G(xt)).addAbortListener || function(e, r) {
    if (e === void 0) throw new ws("signal", "AbortSignal", e);
    ac(e, "signal"), Ry(r, "listener");
    let i;
    return e.aborted ? queueMicrotask(() => r()) : (e.addEventListener("abort", r, { __proto__: null, once: true, [my]: true }), i = () => {
      e.removeEventListener("abort", r);
    }), { __proto__: null, [Ey]() {
      var n;
      (n = i) === null || n === void 0 || n();
    } };
  }, AbortSignalAny: Sy.any || function(e) {
    if (e.length === 1) return e[0];
    let r = new Ay(), i = () => r.abort();
    return e.forEach((n) => {
      ac(n, "signals"), n.addEventListener("abort", i, { once: true });
    }), r.signal.addEventListener("abort", () => {
      e.forEach((n) => n.removeEventListener("abort", i));
    }, { once: true }), r.signal;
  } };
  _s.exports.promisify.custom = Symbol.for("nodejs.util.promisify.custom");
});
var kr = O((XI, bc) => {
  "use strict";
  _();
  v();
  m();
  var { ArrayIsArray: vs, ArrayPrototypeIncludes: fc, ArrayPrototypeJoin: hc, ArrayPrototypeMap: Cy, NumberIsInteger: Es, NumberIsNaN: ky, NumberMAX_SAFE_INTEGER: Py, NumberMIN_SAFE_INTEGER: By, NumberParseInt: xy, ObjectPrototypeHasOwnProperty: Oy, RegExpPrototypeExec: dc, String: My, StringPrototypeToUpperCase: Ly, StringPrototypeTrim: qy } = ie(), { hideStackFrames: Me, codes: { ERR_SOCKET_BAD_PORT: Uy, ERR_INVALID_ARG_TYPE: ve, ERR_INVALID_ARG_VALUE: Cr, ERR_OUT_OF_RANGE: Jt, ERR_UNKNOWN_SIGNAL: lc } } = me(), { normalizeEncoding: Ny } = Ie(), { isAsyncFunction: Dy, isArrayBufferView: jy } = Ie().types, uc = {};
  function Fy(t) {
    return t === (t | 0);
  }
  function Wy(t) {
    return t === t >>> 0;
  }
  var $y = /^[0-7]+$/, Hy = "must be a 32-bit unsigned integer or an octal string";
  function Vy(t, e, r) {
    if (typeof t > "u" && (t = r), typeof t == "string") {
      if (dc($y, t) === null) throw new Cr(e, t, Hy);
      t = xy(t, 8);
    }
    return pc(t, e), t;
  }
  var zy = Me((t, e, r = By, i = Py) => {
    if (typeof t != "number") throw new ve(e, "number", t);
    if (!Es(t)) throw new Jt(e, "an integer", t);
    if (t < r || t > i) throw new Jt(e, `>= ${r} && <= ${i}`, t);
  }), Ky = Me((t, e, r = -2147483648, i = 2147483647) => {
    if (typeof t != "number") throw new ve(e, "number", t);
    if (!Es(t)) throw new Jt(e, "an integer", t);
    if (t < r || t > i) throw new Jt(e, `>= ${r} && <= ${i}`, t);
  }), pc = Me((t, e, r = false) => {
    if (typeof t != "number") throw new ve(e, "number", t);
    if (!Es(t)) throw new Jt(e, "an integer", t);
    let i = r ? 1 : 0, n = 4294967295;
    if (t < i || t > n) throw new Jt(e, `>= ${i} && <= ${n}`, t);
  });
  function Ss(t, e) {
    if (typeof t != "string") throw new ve(e, "string", t);
  }
  function Qy(t, e, r = void 0, i) {
    if (typeof t != "number") throw new ve(e, "number", t);
    if (r != null && t < r || i != null && t > i || (r != null || i != null) && ky(t)) throw new Jt(e, `${r != null ? `>= ${r}` : ""}${r != null && i != null ? " && " : ""}${i != null ? `<= ${i}` : ""}`, t);
  }
  var Gy = Me((t, e, r) => {
    if (!fc(r, t)) {
      let n = "must be one of: " + hc(Cy(r, (o) => typeof o == "string" ? `'${o}'` : My(o)), ", ");
      throw new Cr(e, t, n);
    }
  });
  function gc(t, e) {
    if (typeof t != "boolean") throw new ve(e, "boolean", t);
  }
  function ms(t, e, r) {
    return t == null || !Oy(t, e) ? r : t[e];
  }
  var Yy = Me((t, e, r = null) => {
    let i = ms(r, "allowArray", false), n = ms(r, "allowFunction", false);
    if (!ms(r, "nullable", false) && t === null || !i && vs(t) || typeof t != "object" && (!n || typeof t != "function")) throw new ve(e, "Object", t);
  }), Jy = Me((t, e) => {
    if (t != null && typeof t != "object" && typeof t != "function") throw new ve(e, "a dictionary", t);
  }), Ki = Me((t, e, r = 0) => {
    if (!vs(t)) throw new ve(e, "Array", t);
    if (t.length < r) {
      let i = `must be longer than ${r}`;
      throw new Cr(e, t, i);
    }
  });
  function Xy(t, e) {
    Ki(t, e);
    for (let r = 0; r < t.length; r++) Ss(t[r], `${e}[${r}]`);
  }
  function Zy(t, e) {
    Ki(t, e);
    for (let r = 0; r < t.length; r++) gc(t[r], `${e}[${r}]`);
  }
  function eb(t, e) {
    Ki(t, e);
    for (let r = 0; r < t.length; r++) {
      let i = t[r], n = `${e}[${r}]`;
      if (i == null) throw new ve(n, "AbortSignal", i);
      yc(i, n);
    }
  }
  function tb(t, e = "signal") {
    if (Ss(t, e), uc[t] === void 0) throw uc[Ly(t)] !== void 0 ? new lc(t + " (signals must use all capital letters)") : new lc(t);
  }
  var rb = Me((t, e = "buffer") => {
    if (!jy(t)) throw new ve(e, ["Buffer", "TypedArray", "DataView"], t);
  });
  function ib(t, e) {
    let r = Ny(e), i = t.length;
    if (r === "hex" && i % 2 !== 0) throw new Cr("encoding", e, `is invalid for data of length ${i}`);
  }
  function nb(t, e = "Port", r = true) {
    if (typeof t != "number" && typeof t != "string" || typeof t == "string" && qy(t).length === 0 || +t !== +t >>> 0 || t > 65535 || t === 0 && !r) throw new Uy(e, t, r);
    return t | 0;
  }
  var yc = Me((t, e) => {
    if (t !== void 0 && (t === null || typeof t != "object" || !("aborted" in t))) throw new ve(e, "AbortSignal", t);
  }), sb = Me((t, e) => {
    if (typeof t != "function") throw new ve(e, "Function", t);
  }), ob = Me((t, e) => {
    if (typeof t != "function" || Dy(t)) throw new ve(e, "Function", t);
  }), ab = Me((t, e) => {
    if (t !== void 0) throw new ve(e, "undefined", t);
  });
  function lb(t, e, r) {
    if (!fc(r, t)) throw new ve(e, `('${hc(r, "|")}')`, t);
  }
  var ub = /^(?:<[^>]*>)(?:\s*;\s*[^;"\s]+(?:=(")?[^;"\s]*\1)?)*$/;
  function cc(t, e) {
    if (typeof t > "u" || !dc(ub, t)) throw new Cr(e, t, 'must be an array or string of format "</styles.css>; rel=preload; as=style"');
  }
  function cb(t) {
    if (typeof t == "string") return cc(t, "hints"), t;
    if (vs(t)) {
      let e = t.length, r = "";
      if (e === 0) return r;
      for (let i = 0; i < e; i++) {
        let n = t[i];
        cc(n, "hints"), r += n, i !== e - 1 && (r += ", ");
      }
      return r;
    }
    throw new Cr("hints", t, 'must be an array or string of format "</styles.css>; rel=preload; as=style"');
  }
  bc.exports = { isInt32: Fy, isUint32: Wy, parseFileMode: Vy, validateArray: Ki, validateStringArray: Xy, validateBooleanArray: Zy, validateAbortSignalArray: eb, validateBoolean: gc, validateBuffer: rb, validateDictionary: Jy, validateEncoding: ib, validateFunction: sb, validateInt32: Ky, validateInteger: zy, validateNumber: Qy, validateObject: Yy, validateOneOf: Gy, validatePlainFunction: ob, validatePort: nb, validateSignalName: tb, validateString: Ss, validateUint32: pc, validateUndefined: ab, validateUnion: lb, validateAbortSignal: yc, validateLinkHeaderValue: cb };
});
var Mt = O((lT, vc) => {
  _();
  v();
  m();
  var ue = vc.exports = {}, Ye, Je;
  function As() {
    throw new Error("setTimeout has not been defined");
  }
  function Is() {
    throw new Error("clearTimeout has not been defined");
  }
  (function() {
    try {
      typeof setTimeout == "function" ? Ye = setTimeout : Ye = As;
    } catch {
      Ye = As;
    }
    try {
      typeof clearTimeout == "function" ? Je = clearTimeout : Je = Is;
    } catch {
      Je = Is;
    }
  })();
  function wc(t) {
    if (Ye === setTimeout) return setTimeout(t, 0);
    if ((Ye === As || !Ye) && setTimeout) return Ye = setTimeout, setTimeout(t, 0);
    try {
      return Ye(t, 0);
    } catch {
      try {
        return Ye.call(null, t, 0);
      } catch {
        return Ye.call(this, t, 0);
      }
    }
  }
  function fb(t) {
    if (Je === clearTimeout) return clearTimeout(t);
    if ((Je === Is || !Je) && clearTimeout) return Je = clearTimeout, clearTimeout(t);
    try {
      return Je(t);
    } catch {
      try {
        return Je.call(null, t);
      } catch {
        return Je.call(this, t);
      }
    }
  }
  var bt = [], Pr = false, Xt, Qi = -1;
  function hb() {
    !Pr || !Xt || (Pr = false, Xt.length ? bt = Xt.concat(bt) : Qi = -1, bt.length && _c());
  }
  function _c() {
    if (!Pr) {
      var t = wc(hb);
      Pr = true;
      for (var e = bt.length; e; ) {
        for (Xt = bt, bt = []; ++Qi < e; ) Xt && Xt[Qi].run();
        Qi = -1, e = bt.length;
      }
      Xt = null, Pr = false, fb(t);
    }
  }
  ue.nextTick = function(t) {
    var e = new Array(arguments.length - 1);
    if (arguments.length > 1) for (var r = 1; r < arguments.length; r++) e[r - 1] = arguments[r];
    bt.push(new mc(t, e)), bt.length === 1 && !Pr && wc(_c);
  };
  function mc(t, e) {
    this.fun = t, this.array = e;
  }
  mc.prototype.run = function() {
    this.fun.apply(null, this.array);
  };
  ue.title = "browser";
  ue.browser = true;
  ue.env = {};
  ue.argv = [];
  ue.version = "";
  ue.versions = {};
  function wt() {
  }
  ue.on = wt;
  ue.addListener = wt;
  ue.once = wt;
  ue.off = wt;
  ue.removeListener = wt;
  ue.removeAllListeners = wt;
  ue.emit = wt;
  ue.prependListener = wt;
  ue.prependOnceListener = wt;
  ue.listeners = function(t) {
    return [];
  };
  ue.binding = function(t) {
    throw new Error("process.binding is not supported");
  };
  ue.cwd = function() {
    return "/";
  };
  ue.chdir = function(t) {
    throw new Error("process.chdir is not supported");
  };
  ue.umask = function() {
    return 0;
  };
});
var Ze = O((wT, qc) => {
  "use strict";
  _();
  v();
  m();
  var { SymbolAsyncIterator: Ec, SymbolIterator: Sc, SymbolFor: Zt } = ie(), Ac = Zt("nodejs.stream.destroyed"), Ic = Zt("nodejs.stream.errored"), Ts = Zt("nodejs.stream.readable"), Rs = Zt("nodejs.stream.writable"), Tc = Zt("nodejs.stream.disturbed"), db = Zt("nodejs.webstream.isClosedPromise"), pb = Zt("nodejs.webstream.controllerErrorFunction");
  function Gi(t, e = false) {
    var r;
    return !!(t && typeof t.pipe == "function" && typeof t.on == "function" && (!e || typeof t.pause == "function" && typeof t.resume == "function") && (!t._writableState || ((r = t._readableState) === null || r === void 0 ? void 0 : r.readable) !== false) && (!t._writableState || t._readableState));
  }
  function Yi(t) {
    var e;
    return !!(t && typeof t.write == "function" && typeof t.on == "function" && (!t._readableState || ((e = t._writableState) === null || e === void 0 ? void 0 : e.writable) !== false));
  }
  function gb(t) {
    return !!(t && typeof t.pipe == "function" && t._readableState && typeof t.on == "function" && typeof t.write == "function");
  }
  function Xe(t) {
    return t && (t._readableState || t._writableState || typeof t.write == "function" && typeof t.on == "function" || typeof t.pipe == "function" && typeof t.on == "function");
  }
  function Rc(t) {
    return !!(t && !Xe(t) && typeof t.pipeThrough == "function" && typeof t.getReader == "function" && typeof t.cancel == "function");
  }
  function Cc(t) {
    return !!(t && !Xe(t) && typeof t.getWriter == "function" && typeof t.abort == "function");
  }
  function kc(t) {
    return !!(t && !Xe(t) && typeof t.readable == "object" && typeof t.writable == "object");
  }
  function yb(t) {
    return Rc(t) || Cc(t) || kc(t);
  }
  function bb(t, e) {
    return t == null ? false : e === true ? typeof t[Ec] == "function" : e === false ? typeof t[Sc] == "function" : typeof t[Ec] == "function" || typeof t[Sc] == "function";
  }
  function Ji(t) {
    if (!Xe(t)) return null;
    let e = t._writableState, r = t._readableState, i = e || r;
    return !!(t.destroyed || t[Ac] || i != null && i.destroyed);
  }
  function Pc(t) {
    if (!Yi(t)) return null;
    if (t.writableEnded === true) return true;
    let e = t._writableState;
    return e != null && e.errored ? false : typeof (e == null ? void 0 : e.ended) != "boolean" ? null : e.ended;
  }
  function wb(t, e) {
    if (!Yi(t)) return null;
    if (t.writableFinished === true) return true;
    let r = t._writableState;
    return r != null && r.errored ? false : typeof (r == null ? void 0 : r.finished) != "boolean" ? null : !!(r.finished || e === false && r.ended === true && r.length === 0);
  }
  function _b(t) {
    if (!Gi(t)) return null;
    if (t.readableEnded === true) return true;
    let e = t._readableState;
    return !e || e.errored ? false : typeof (e == null ? void 0 : e.ended) != "boolean" ? null : e.ended;
  }
  function Bc(t, e) {
    if (!Gi(t)) return null;
    let r = t._readableState;
    return r != null && r.errored ? false : typeof (r == null ? void 0 : r.endEmitted) != "boolean" ? null : !!(r.endEmitted || e === false && r.ended === true && r.length === 0);
  }
  function xc(t) {
    return t && t[Ts] != null ? t[Ts] : typeof (t == null ? void 0 : t.readable) != "boolean" ? null : Ji(t) ? false : Gi(t) && t.readable && !Bc(t);
  }
  function Oc(t) {
    return t && t[Rs] != null ? t[Rs] : typeof (t == null ? void 0 : t.writable) != "boolean" ? null : Ji(t) ? false : Yi(t) && t.writable && !Pc(t);
  }
  function mb(t, e) {
    return Xe(t) ? Ji(t) ? true : !((e == null ? void 0 : e.readable) !== false && xc(t) || (e == null ? void 0 : e.writable) !== false && Oc(t)) : null;
  }
  function vb(t) {
    var e, r;
    return Xe(t) ? t.writableErrored ? t.writableErrored : (e = (r = t._writableState) === null || r === void 0 ? void 0 : r.errored) !== null && e !== void 0 ? e : null : null;
  }
  function Eb(t) {
    var e, r;
    return Xe(t) ? t.readableErrored ? t.readableErrored : (e = (r = t._readableState) === null || r === void 0 ? void 0 : r.errored) !== null && e !== void 0 ? e : null : null;
  }
  function Sb(t) {
    if (!Xe(t)) return null;
    if (typeof t.closed == "boolean") return t.closed;
    let e = t._writableState, r = t._readableState;
    return typeof (e == null ? void 0 : e.closed) == "boolean" || typeof (r == null ? void 0 : r.closed) == "boolean" ? (e == null ? void 0 : e.closed) || (r == null ? void 0 : r.closed) : typeof t._closed == "boolean" && Mc(t) ? t._closed : null;
  }
  function Mc(t) {
    return typeof t._closed == "boolean" && typeof t._defaultKeepAlive == "boolean" && typeof t._removedConnection == "boolean" && typeof t._removedContLen == "boolean";
  }
  function Lc(t) {
    return typeof t._sent100 == "boolean" && Mc(t);
  }
  function Ab(t) {
    var e;
    return typeof t._consuming == "boolean" && typeof t._dumped == "boolean" && ((e = t.req) === null || e === void 0 ? void 0 : e.upgradeOrConnect) === void 0;
  }
  function Ib(t) {
    if (!Xe(t)) return null;
    let e = t._writableState, r = t._readableState, i = e || r;
    return !i && Lc(t) || !!(i && i.autoDestroy && i.emitClose && i.closed === false);
  }
  function Tb(t) {
    var e;
    return !!(t && ((e = t[Tc]) !== null && e !== void 0 ? e : t.readableDidRead || t.readableAborted));
  }
  function Rb(t) {
    var e, r, i, n, o, s, a, u, f, d;
    return !!(t && ((e = (r = (i = (n = (o = (s = t[Ic]) !== null && s !== void 0 ? s : t.readableErrored) !== null && o !== void 0 ? o : t.writableErrored) !== null && n !== void 0 ? n : (a = t._readableState) === null || a === void 0 ? void 0 : a.errorEmitted) !== null && i !== void 0 ? i : (u = t._writableState) === null || u === void 0 ? void 0 : u.errorEmitted) !== null && r !== void 0 ? r : (f = t._readableState) === null || f === void 0 ? void 0 : f.errored) !== null && e !== void 0 ? e : !((d = t._writableState) === null || d === void 0) && d.errored));
  }
  qc.exports = { isDestroyed: Ji, kIsDestroyed: Ac, isDisturbed: Tb, kIsDisturbed: Tc, isErrored: Rb, kIsErrored: Ic, isReadable: xc, kIsReadable: Ts, kIsClosedPromise: db, kControllerErrorFunction: pb, kIsWritable: Rs, isClosed: Sb, isDuplexNodeStream: gb, isFinished: mb, isIterable: bb, isReadableNodeStream: Gi, isReadableStream: Rc, isReadableEnded: _b, isReadableFinished: Bc, isReadableErrored: Eb, isNodeStream: Xe, isWebStream: yb, isWritable: Oc, isWritableNodeStream: Yi, isWritableStream: Cc, isWritableEnded: Pc, isWritableFinished: wb, isWritableErrored: vb, isServerRequest: Ab, isServerResponse: Lc, willEmitClose: Ib, isTransformStream: kc };
});
var _t = O((CT, xs) => {
  "use strict";
  _();
  v();
  m();
  var Lt = Mt(), { AbortError: Vc, codes: Cb } = me(), { ERR_INVALID_ARG_TYPE: kb, ERR_STREAM_PREMATURE_CLOSE: Uc } = Cb, { kEmptyObject: ks, once: Ps } = Ie(), { validateAbortSignal: Pb, validateFunction: Bb, validateObject: xb, validateBoolean: Ob } = kr(), { Promise: Mb, PromisePrototypeThen: Lb, SymbolDispose: zc } = ie(), { isClosed: qb, isReadable: Nc, isReadableNodeStream: Cs, isReadableStream: Ub, isReadableFinished: Dc, isReadableErrored: jc, isWritable: Fc, isWritableNodeStream: Wc, isWritableStream: Nb, isWritableFinished: $c, isWritableErrored: Hc, isNodeStream: Db, willEmitClose: jb, kIsClosedPromise: Fb } = Ze(), Br;
  function Wb(t) {
    return t.setHeader && typeof t.abort == "function";
  }
  var Bs = () => {
  };
  function Kc(t, e, r) {
    var i, n;
    if (arguments.length === 2 ? (r = e, e = ks) : e == null ? e = ks : xb(e, "options"), Bb(r, "callback"), Pb(e.signal, "options.signal"), r = Ps(r), Ub(t) || Nb(t)) return $b(t, e, r);
    if (!Db(t)) throw new kb("stream", ["ReadableStream", "WritableStream", "Stream"], t);
    let o = (i = e.readable) !== null && i !== void 0 ? i : Cs(t), s = (n = e.writable) !== null && n !== void 0 ? n : Wc(t), a = t._writableState, u = t._readableState, f = () => {
      t.writable || g();
    }, d = jb(t) && Cs(t) === o && Wc(t) === s, h = $c(t, false), g = () => {
      h = true, t.destroyed && (d = false), !(d && (!t.readable || o)) && (!o || y) && r.call(t);
    }, y = Dc(t, false), E = () => {
      y = true, t.destroyed && (d = false), !(d && (!t.writable || s)) && (!s || h) && r.call(t);
    }, w = (q) => {
      r.call(t, q);
    }, S = qb(t), I = () => {
      S = true;
      let q = Hc(t) || jc(t);
      if (q && typeof q != "boolean") return r.call(t, q);
      if (o && !y && Cs(t, true) && !Dc(t, false)) return r.call(t, new Uc());
      if (s && !h && !$c(t, false)) return r.call(t, new Uc());
      r.call(t);
    }, C = () => {
      S = true;
      let q = Hc(t) || jc(t);
      if (q && typeof q != "boolean") return r.call(t, q);
      r.call(t);
    }, k = () => {
      t.req.on("finish", g);
    };
    Wb(t) ? (t.on("complete", g), d || t.on("abort", I), t.req ? k() : t.on("request", k)) : s && !a && (t.on("end", f), t.on("close", f)), !d && typeof t.aborted == "boolean" && t.on("aborted", I), t.on("end", E), t.on("finish", g), e.error !== false && t.on("error", w), t.on("close", I), S ? Lt.nextTick(I) : a != null && a.errorEmitted || u != null && u.errorEmitted ? d || Lt.nextTick(C) : (!o && (!d || Nc(t)) && (h || Fc(t) === false) || !s && (!d || Fc(t)) && (y || Nc(t) === false) || u && t.req && t.aborted) && Lt.nextTick(C);
    let M = () => {
      r = Bs, t.removeListener("aborted", I), t.removeListener("complete", g), t.removeListener("abort", I), t.removeListener("request", k), t.req && t.req.removeListener("finish", g), t.removeListener("end", f), t.removeListener("close", f), t.removeListener("finish", g), t.removeListener("end", E), t.removeListener("error", w), t.removeListener("close", I);
    };
    if (e.signal && !S) {
      let q = () => {
        let z = r;
        M(), z.call(t, new Vc(void 0, { cause: e.signal.reason }));
      };
      if (e.signal.aborted) Lt.nextTick(q);
      else {
        Br = Br || Ie().addAbortListener;
        let z = Br(e.signal, q), j = r;
        r = Ps((...Q) => {
          z[zc](), j.apply(t, Q);
        });
      }
    }
    return M;
  }
  function $b(t, e, r) {
    let i = false, n = Bs;
    if (e.signal) if (n = () => {
      i = true, r.call(t, new Vc(void 0, { cause: e.signal.reason }));
    }, e.signal.aborted) Lt.nextTick(n);
    else {
      Br = Br || Ie().addAbortListener;
      let s = Br(e.signal, n), a = r;
      r = Ps((...u) => {
        s[zc](), a.apply(t, u);
      });
    }
    let o = (...s) => {
      i || Lt.nextTick(() => r.apply(t, s));
    };
    return Lb(t[Fb].promise, o, o), Bs;
  }
  function Hb(t, e) {
    var r;
    let i = false;
    return e === null && (e = ks), (r = e) !== null && r !== void 0 && r.cleanup && (Ob(e.cleanup, "cleanup"), i = e.cleanup), new Mb((n, o) => {
      let s = Kc(t, e, (a) => {
        i && s(), a ? o(a) : n();
      });
    });
  }
  xs.exports = Kc;
  xs.exports.finished = Hb;
});
var er = O((NT, tf) => {
  "use strict";
  _();
  v();
  m();
  var et = Mt(), { aggregateTwoErrors: Vb, codes: { ERR_MULTIPLE_CALLBACK: zb }, AbortError: Kb } = me(), { Symbol: Yc } = ie(), { kIsDestroyed: Qb, isDestroyed: Gb, isFinished: Yb, isServerRequest: Jb } = Ze(), Jc = Yc("kDestroy"), Os = Yc("kConstruct");
  function Xc(t, e, r) {
    t && (t.stack, e && !e.errored && (e.errored = t), r && !r.errored && (r.errored = t));
  }
  function Xb(t, e) {
    let r = this._readableState, i = this._writableState, n = i || r;
    return i != null && i.destroyed || r != null && r.destroyed ? (typeof e == "function" && e(), this) : (Xc(t, i, r), i && (i.destroyed = true), r && (r.destroyed = true), n.constructed ? Qc(this, t, e) : this.once(Jc, function(o) {
      Qc(this, Vb(o, t), e);
    }), this);
  }
  function Qc(t, e, r) {
    let i = false;
    function n(o) {
      if (i) return;
      i = true;
      let s = t._readableState, a = t._writableState;
      Xc(o, a, s), a && (a.closed = true), s && (s.closed = true), typeof r == "function" && r(o), o ? et.nextTick(Zb, t, o) : et.nextTick(Zc, t);
    }
    try {
      t._destroy(e || null, n);
    } catch (o) {
      n(o);
    }
  }
  function Zb(t, e) {
    Ms(t, e), Zc(t);
  }
  function Zc(t) {
    let e = t._readableState, r = t._writableState;
    r && (r.closeEmitted = true), e && (e.closeEmitted = true), (r != null && r.emitClose || e != null && e.emitClose) && t.emit("close");
  }
  function Ms(t, e) {
    let r = t._readableState, i = t._writableState;
    i != null && i.errorEmitted || r != null && r.errorEmitted || (i && (i.errorEmitted = true), r && (r.errorEmitted = true), t.emit("error", e));
  }
  function ew() {
    let t = this._readableState, e = this._writableState;
    t && (t.constructed = true, t.closed = false, t.closeEmitted = false, t.destroyed = false, t.errored = null, t.errorEmitted = false, t.reading = false, t.ended = t.readable === false, t.endEmitted = t.readable === false), e && (e.constructed = true, e.destroyed = false, e.closed = false, e.closeEmitted = false, e.errored = null, e.errorEmitted = false, e.finalCalled = false, e.prefinished = false, e.ended = e.writable === false, e.ending = e.writable === false, e.finished = e.writable === false);
  }
  function Ls(t, e, r) {
    let i = t._readableState, n = t._writableState;
    if (n != null && n.destroyed || i != null && i.destroyed) return this;
    i != null && i.autoDestroy || n != null && n.autoDestroy ? t.destroy(e) : e && (e.stack, n && !n.errored && (n.errored = e), i && !i.errored && (i.errored = e), r ? et.nextTick(Ms, t, e) : Ms(t, e));
  }
  function tw(t, e) {
    if (typeof t._construct != "function") return;
    let r = t._readableState, i = t._writableState;
    r && (r.constructed = false), i && (i.constructed = false), t.once(Os, e), !(t.listenerCount(Os) > 1) && et.nextTick(rw, t);
  }
  function rw(t) {
    let e = false;
    function r(i) {
      if (e) {
        Ls(t, i ?? new zb());
        return;
      }
      e = true;
      let n = t._readableState, o = t._writableState, s = o || n;
      n && (n.constructed = true), o && (o.constructed = true), s.destroyed ? t.emit(Jc, i) : i ? Ls(t, i, true) : et.nextTick(iw, t);
    }
    try {
      t._construct((i) => {
        et.nextTick(r, i);
      });
    } catch (i) {
      et.nextTick(r, i);
    }
  }
  function iw(t) {
    t.emit(Os);
  }
  function Gc(t) {
    return (t == null ? void 0 : t.setHeader) && typeof t.abort == "function";
  }
  function ef(t) {
    t.emit("close");
  }
  function nw(t, e) {
    t.emit("error", e), et.nextTick(ef, t);
  }
  function sw(t, e) {
    !t || Gb(t) || (!e && !Yb(t) && (e = new Kb()), Jb(t) ? (t.socket = null, t.destroy(e)) : Gc(t) ? t.abort() : Gc(t.req) ? t.req.abort() : typeof t.destroy == "function" ? t.destroy(e) : typeof t.close == "function" ? t.close() : e ? et.nextTick(nw, t, e) : et.nextTick(ef, t), t.destroyed || (t[Qb] = true));
  }
  tf.exports = { construct: tw, destroyer: sw, destroy: Xb, undestroy: ew, errorOrDestroy: Ls };
});
var en = O((QT, nf) => {
  "use strict";
  _();
  v();
  m();
  var { ArrayIsArray: ow, ObjectSetPrototypeOf: rf } = ie(), { EventEmitter: Xi } = (Ot(), G(xt));
  function Zi(t) {
    Xi.call(this, t);
  }
  rf(Zi.prototype, Xi.prototype);
  rf(Zi, Xi);
  Zi.prototype.pipe = function(t, e) {
    let r = this;
    function i(d) {
      t.writable && t.write(d) === false && r.pause && r.pause();
    }
    r.on("data", i);
    function n() {
      r.readable && r.resume && r.resume();
    }
    t.on("drain", n), !t._isStdio && (!e || e.end !== false) && (r.on("end", s), r.on("close", a));
    let o = false;
    function s() {
      o || (o = true, t.end());
    }
    function a() {
      o || (o = true, typeof t.destroy == "function" && t.destroy());
    }
    function u(d) {
      f(), Xi.listenerCount(this, "error") === 0 && this.emit("error", d);
    }
    qs(r, "error", u), qs(t, "error", u);
    function f() {
      r.removeListener("data", i), t.removeListener("drain", n), r.removeListener("end", s), r.removeListener("close", a), r.removeListener("error", u), t.removeListener("error", u), r.removeListener("end", f), r.removeListener("close", f), t.removeListener("close", f);
    }
    return r.on("end", f), r.on("close", f), t.on("close", f), t.emit("pipe", r), t;
  };
  function qs(t, e, r) {
    if (typeof t.prependListener == "function") return t.prependListener(e, r);
    !t._events || !t._events[e] ? t.on(e, r) : ow(t._events[e]) ? t._events[e].unshift(r) : t._events[e] = [r, t._events[e]];
  }
  nf.exports = { Stream: Zi, prependListener: qs };
});
var oi = O((nR, tn) => {
  "use strict";
  _();
  v();
  m();
  var { SymbolDispose: aw } = ie(), { AbortError: sf, codes: lw } = me(), { isNodeStream: of, isWebStream: uw, kControllerErrorFunction: cw } = Ze(), fw = _t(), { ERR_INVALID_ARG_TYPE: af } = lw, Us, hw = (t, e) => {
    if (typeof t != "object" || !("aborted" in t)) throw new af(e, "AbortSignal", t);
  };
  tn.exports.addAbortSignal = function(e, r) {
    if (hw(e, "signal"), !of(r) && !uw(r)) throw new af("stream", ["ReadableStream", "WritableStream", "Stream"], r);
    return tn.exports.addAbortSignalNoValidate(e, r);
  };
  tn.exports.addAbortSignalNoValidate = function(t, e) {
    if (typeof t != "object" || !("aborted" in t)) return e;
    let r = of(e) ? () => {
      e.destroy(new sf(void 0, { cause: t.reason }));
    } : () => {
      e[cw](new sf(void 0, { cause: t.reason }));
    };
    if (t.aborted) r();
    else {
      Us = Us || Ie().addAbortListener;
      let i = Us(t, r);
      fw(e, i[aw]);
    }
    return e;
  };
});
var cf = O((gR, uf) => {
  "use strict";
  _();
  v();
  m();
  var { StringPrototypeSlice: lf, SymbolIterator: dw, TypedArrayPrototypeSet: rn, Uint8Array: pw } = ie(), { Buffer: Ns } = (he(), G(be)), { inspect: gw } = Ie();
  uf.exports = class {
    constructor() {
      this.head = null, this.tail = null, this.length = 0;
    }
    push(e) {
      let r = { data: e, next: null };
      this.length > 0 ? this.tail.next = r : this.head = r, this.tail = r, ++this.length;
    }
    unshift(e) {
      let r = { data: e, next: this.head };
      this.length === 0 && (this.tail = r), this.head = r, ++this.length;
    }
    shift() {
      if (this.length === 0) return;
      let e = this.head.data;
      return this.length === 1 ? this.head = this.tail = null : this.head = this.head.next, --this.length, e;
    }
    clear() {
      this.head = this.tail = null, this.length = 0;
    }
    join(e) {
      if (this.length === 0) return "";
      let r = this.head, i = "" + r.data;
      for (; (r = r.next) !== null; ) i += e + r.data;
      return i;
    }
    concat(e) {
      if (this.length === 0) return Ns.alloc(0);
      let r = Ns.allocUnsafe(e >>> 0), i = this.head, n = 0;
      for (; i; ) rn(r, i.data, n), n += i.data.length, i = i.next;
      return r;
    }
    consume(e, r) {
      let i = this.head.data;
      if (e < i.length) {
        let n = i.slice(0, e);
        return this.head.data = i.slice(e), n;
      }
      return e === i.length ? this.shift() : r ? this._getString(e) : this._getBuffer(e);
    }
    first() {
      return this.head.data;
    }
    *[dw]() {
      for (let e = this.head; e; e = e.next) yield e.data;
    }
    _getString(e) {
      let r = "", i = this.head, n = 0;
      do {
        let o = i.data;
        if (e > o.length) r += o, e -= o.length;
        else {
          e === o.length ? (r += o, ++n, i.next ? this.head = i.next : this.head = this.tail = null) : (r += lf(o, 0, e), this.head = i, i.data = lf(o, e));
          break;
        }
        ++n;
      } while ((i = i.next) !== null);
      return this.length -= n, r;
    }
    _getBuffer(e) {
      let r = Ns.allocUnsafe(e), i = e, n = this.head, o = 0;
      do {
        let s = n.data;
        if (e > s.length) rn(r, s, i - e), e -= s.length;
        else {
          e === s.length ? (rn(r, s, i - e), ++o, n.next ? this.head = n.next : this.head = this.tail = null) : (rn(r, new pw(s.buffer, s.byteOffset, e), i - e), this.head = n, n.data = s.slice(e));
          break;
        }
        ++o;
      } while ((n = n.next) !== null);
      return this.length -= o, r;
    }
    [Symbol.for("nodejs.util.inspect.custom")](e, r) {
      return gw(this, { ...r, depth: 0, customInspect: false });
    }
  };
});
var ai = O((IR, pf) => {
  "use strict";
  _();
  v();
  m();
  var { MathFloor: yw, NumberIsInteger: bw } = ie(), { validateInteger: ww } = kr(), { ERR_INVALID_ARG_VALUE: _w } = me().codes, ff = 16 * 1024, hf = 16;
  function mw(t, e, r) {
    return t.highWaterMark != null ? t.highWaterMark : e ? t[r] : null;
  }
  function df(t) {
    return t ? hf : ff;
  }
  function vw(t, e) {
    ww(e, "value", 0), t ? hf = e : ff = e;
  }
  function Ew(t, e, r, i) {
    let n = mw(e, i, r);
    if (n != null) {
      if (!bw(n) || n < 0) {
        let o = i ? `options.${r}` : "options.highWaterMark";
        throw new _w(o, n);
      }
      return yw(n);
    }
    return df(t.objectMode);
  }
  pf.exports = { getHighWaterMark: Ew, getDefaultHighWaterMark: df, setDefaultHighWaterMark: vw };
});
var bf = O((Ds, yf) => {
  _();
  v();
  m();
  var nn = (he(), G(be)), tt = nn.Buffer;
  function gf(t, e) {
    for (var r in t) e[r] = t[r];
  }
  tt.from && tt.alloc && tt.allocUnsafe && tt.allocUnsafeSlow ? yf.exports = nn : (gf(nn, Ds), Ds.Buffer = tr);
  function tr(t, e, r) {
    return tt(t, e, r);
  }
  tr.prototype = Object.create(tt.prototype);
  gf(tt, tr);
  tr.from = function(t, e, r) {
    if (typeof t == "number") throw new TypeError("Argument must not be a number");
    return tt(t, e, r);
  };
  tr.alloc = function(t, e, r) {
    if (typeof t != "number") throw new TypeError("Argument must be a number");
    var i = tt(t);
    return e !== void 0 ? typeof r == "string" ? i.fill(e, r) : i.fill(e) : i.fill(0), i;
  };
  tr.allocUnsafe = function(t) {
    if (typeof t != "number") throw new TypeError("Argument must be a number");
    return tt(t);
  };
  tr.allocUnsafeSlow = function(t) {
    if (typeof t != "number") throw new TypeError("Argument must be a number");
    return nn.SlowBuffer(t);
  };
});
var mf = O((_f) => {
  "use strict";
  _();
  v();
  m();
  var Fs = bf().Buffer, wf = Fs.isEncoding || function(t) {
    switch (t = "" + t, t && t.toLowerCase()) {
      case "hex":
      case "utf8":
      case "utf-8":
      case "ascii":
      case "binary":
      case "base64":
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
      case "raw":
        return true;
      default:
        return false;
    }
  };
  function Sw(t) {
    if (!t) return "utf8";
    for (var e; ; ) switch (t) {
      case "utf8":
      case "utf-8":
        return "utf8";
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
        return "utf16le";
      case "latin1":
      case "binary":
        return "latin1";
      case "base64":
      case "ascii":
      case "hex":
        return t;
      default:
        if (e) return;
        t = ("" + t).toLowerCase(), e = true;
    }
  }
  function Aw(t) {
    var e = Sw(t);
    if (typeof e != "string" && (Fs.isEncoding === wf || !wf(t))) throw new Error("Unknown encoding: " + t);
    return e || t;
  }
  _f.StringDecoder = li;
  function li(t) {
    this.encoding = Aw(t);
    var e;
    switch (this.encoding) {
      case "utf16le":
        this.text = Pw, this.end = Bw, e = 4;
        break;
      case "utf8":
        this.fillLast = Rw, e = 4;
        break;
      case "base64":
        this.text = xw, this.end = Ow, e = 3;
        break;
      default:
        this.write = Mw, this.end = Lw;
        return;
    }
    this.lastNeed = 0, this.lastTotal = 0, this.lastChar = Fs.allocUnsafe(e);
  }
  li.prototype.write = function(t) {
    if (t.length === 0) return "";
    var e, r;
    if (this.lastNeed) {
      if (e = this.fillLast(t), e === void 0) return "";
      r = this.lastNeed, this.lastNeed = 0;
    } else r = 0;
    return r < t.length ? e ? e + this.text(t, r) : this.text(t, r) : e || "";
  };
  li.prototype.end = kw;
  li.prototype.text = Cw;
  li.prototype.fillLast = function(t) {
    if (this.lastNeed <= t.length) return t.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed), this.lastChar.toString(this.encoding, 0, this.lastTotal);
    t.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, t.length), this.lastNeed -= t.length;
  };
  function js(t) {
    return t <= 127 ? 0 : t >> 5 === 6 ? 2 : t >> 4 === 14 ? 3 : t >> 3 === 30 ? 4 : t >> 6 === 2 ? -1 : -2;
  }
  function Iw(t, e, r) {
    var i = e.length - 1;
    if (i < r) return 0;
    var n = js(e[i]);
    return n >= 0 ? (n > 0 && (t.lastNeed = n - 1), n) : --i < r || n === -2 ? 0 : (n = js(e[i]), n >= 0 ? (n > 0 && (t.lastNeed = n - 2), n) : --i < r || n === -2 ? 0 : (n = js(e[i]), n >= 0 ? (n > 0 && (n === 2 ? n = 0 : t.lastNeed = n - 3), n) : 0));
  }
  function Tw(t, e, r) {
    if ((e[0] & 192) !== 128) return t.lastNeed = 0, "�";
    if (t.lastNeed > 1 && e.length > 1) {
      if ((e[1] & 192) !== 128) return t.lastNeed = 1, "�";
      if (t.lastNeed > 2 && e.length > 2 && (e[2] & 192) !== 128) return t.lastNeed = 2, "�";
    }
  }
  function Rw(t) {
    var e = this.lastTotal - this.lastNeed, r = Tw(this, t, e);
    if (r !== void 0) return r;
    if (this.lastNeed <= t.length) return t.copy(this.lastChar, e, 0, this.lastNeed), this.lastChar.toString(this.encoding, 0, this.lastTotal);
    t.copy(this.lastChar, e, 0, t.length), this.lastNeed -= t.length;
  }
  function Cw(t, e) {
    var r = Iw(this, t, e);
    if (!this.lastNeed) return t.toString("utf8", e);
    this.lastTotal = r;
    var i = t.length - (r - this.lastNeed);
    return t.copy(this.lastChar, 0, i), t.toString("utf8", e, i);
  }
  function kw(t) {
    var e = t && t.length ? this.write(t) : "";
    return this.lastNeed ? e + "�" : e;
  }
  function Pw(t, e) {
    if ((t.length - e) % 2 === 0) {
      var r = t.toString("utf16le", e);
      if (r) {
        var i = r.charCodeAt(r.length - 1);
        if (i >= 55296 && i <= 56319) return this.lastNeed = 2, this.lastTotal = 4, this.lastChar[0] = t[t.length - 2], this.lastChar[1] = t[t.length - 1], r.slice(0, -1);
      }
      return r;
    }
    return this.lastNeed = 1, this.lastTotal = 2, this.lastChar[0] = t[t.length - 1], t.toString("utf16le", e, t.length - 1);
  }
  function Bw(t) {
    var e = t && t.length ? this.write(t) : "";
    if (this.lastNeed) {
      var r = this.lastTotal - this.lastNeed;
      return e + this.lastChar.toString("utf16le", 0, r);
    }
    return e;
  }
  function xw(t, e) {
    var r = (t.length - e) % 3;
    return r === 0 ? t.toString("base64", e) : (this.lastNeed = 3 - r, this.lastTotal = 3, r === 1 ? this.lastChar[0] = t[t.length - 1] : (this.lastChar[0] = t[t.length - 2], this.lastChar[1] = t[t.length - 1]), t.toString("base64", e, t.length - r));
  }
  function Ow(t) {
    var e = t && t.length ? this.write(t) : "";
    return this.lastNeed ? e + this.lastChar.toString("base64", 0, 3 - this.lastNeed) : e;
  }
  function Mw(t) {
    return t.toString(this.encoding);
  }
  function Lw(t) {
    return t && t.length ? this.write(t) : "";
  }
});
var Ws = O((eC, Af) => {
  "use strict";
  _();
  v();
  m();
  var vf = Mt(), { PromisePrototypeThen: qw, SymbolAsyncIterator: Ef, SymbolIterator: Sf } = ie(), { Buffer: Uw } = (he(), G(be)), { ERR_INVALID_ARG_TYPE: Nw, ERR_STREAM_NULL_VALUES: Dw } = me().codes;
  function jw(t, e, r) {
    let i;
    if (typeof e == "string" || e instanceof Uw) return new t({ objectMode: true, ...r, read() {
      this.push(e), this.push(null);
    } });
    let n;
    if (e && e[Ef]) n = true, i = e[Ef]();
    else if (e && e[Sf]) n = false, i = e[Sf]();
    else throw new Nw("iterable", ["Iterable"], e);
    let o = new t({ objectMode: true, highWaterMark: 1, ...r }), s = false;
    o._read = function() {
      s || (s = true, u());
    }, o._destroy = function(f, d) {
      qw(a(f), () => vf.nextTick(d, f), (h) => vf.nextTick(d, h || f));
    };
    async function a(f) {
      let d = f != null, h = typeof i.throw == "function";
      if (d && h) {
        let { value: g, done: y } = await i.throw(f);
        if (await g, y) return;
      }
      if (typeof i.return == "function") {
        let { value: g } = await i.return();
        await g;
      }
    }
    async function u() {
      for (; ; ) {
        try {
          let { value: f, done: d } = n ? await i.next() : i.next();
          if (d) o.push(null);
          else {
            let h = f && typeof f.then == "function" ? await f : f;
            if (h === null) throw s = false, new Dw();
            if (o.push(h)) continue;
            s = false;
          }
        } catch (f) {
          o.destroy(f);
        }
        break;
      }
    }
    return o;
  }
  Af.exports = jw;
});
var ci = O((cC, Wf) => {
  "use strict";
  _();
  v();
  m();
  var We = Mt(), { ArrayPrototypeIndexOf: Fw, NumberIsInteger: Ww, NumberIsNaN: $w, NumberParseInt: Hw, ObjectDefineProperties: Ys, ObjectKeys: Vw, ObjectSetPrototypeOf: Rf, Promise: Cf, SafeSet: zw, SymbolAsyncDispose: Kw, SymbolAsyncIterator: Qw, Symbol: Gw } = ie();
  Wf.exports = F;
  F.ReadableState = ln;
  var { EventEmitter: Yw } = (Ot(), G(xt)), { Stream: qt, prependListener: Jw } = en(), { Buffer: $s } = (he(), G(be)), { addAbortSignal: Xw } = oi(), kf = _t(), H = Ie().debuglog("stream", (t) => {
    H = t;
  }), Zw = cf(), Mr = er(), { getHighWaterMark: e_, getDefaultHighWaterMark: t_ } = ai(), { aggregateTwoErrors: If, codes: { ERR_INVALID_ARG_TYPE: r_, ERR_METHOD_NOT_IMPLEMENTED: i_, ERR_OUT_OF_RANGE: n_, ERR_STREAM_PUSH_AFTER_EOF: s_, ERR_STREAM_UNSHIFT_AFTER_END_EVENT: o_ }, AbortError: a_ } = me(), { validateObject: l_ } = kr(), rr = Gw("kPaused"), { StringDecoder: Pf } = mf(), u_ = Ws();
  Rf(F.prototype, qt.prototype);
  Rf(F, qt);
  var Hs = () => {
  }, { errorOrDestroy: xr } = Mr, Or = 1, c_ = 2, Bf = 4, ui = 8, xf = 16, sn = 32, on = 64, Of = 128, f_ = 256, h_ = 512, d_ = 1024, Qs = 2048, Gs = 4096, p_ = 8192, g_ = 16384, y_ = 32768, Mf = 65536, b_ = 1 << 17, w_ = 1 << 18;
  function de(t) {
    return { enumerable: false, get() {
      return (this.state & t) !== 0;
    }, set(e) {
      e ? this.state |= t : this.state &= ~t;
    } };
  }
  Ys(ln.prototype, { objectMode: de(Or), ended: de(c_), endEmitted: de(Bf), reading: de(ui), constructed: de(xf), sync: de(sn), needReadable: de(on), emittedReadable: de(Of), readableListening: de(f_), resumeScheduled: de(h_), errorEmitted: de(d_), emitClose: de(Qs), autoDestroy: de(Gs), destroyed: de(p_), closed: de(g_), closeEmitted: de(y_), multiAwaitDrain: de(Mf), readingMore: de(b_), dataEmitted: de(w_) });
  function ln(t, e, r) {
    typeof r != "boolean" && (r = e instanceof rt()), this.state = Qs | Gs | xf | sn, t && t.objectMode && (this.state |= Or), r && t && t.readableObjectMode && (this.state |= Or), this.highWaterMark = t ? e_(this, t, "readableHighWaterMark", r) : t_(false), this.buffer = new Zw(), this.length = 0, this.pipes = [], this.flowing = null, this[rr] = null, t && t.emitClose === false && (this.state &= ~Qs), t && t.autoDestroy === false && (this.state &= ~Gs), this.errored = null, this.defaultEncoding = t && t.defaultEncoding || "utf8", this.awaitDrainWriters = null, this.decoder = null, this.encoding = null, t && t.encoding && (this.decoder = new Pf(t.encoding), this.encoding = t.encoding);
  }
  function F(t) {
    if (!(this instanceof F)) return new F(t);
    let e = this instanceof rt();
    this._readableState = new ln(t, this, e), t && (typeof t.read == "function" && (this._read = t.read), typeof t.destroy == "function" && (this._destroy = t.destroy), typeof t.construct == "function" && (this._construct = t.construct), t.signal && !e && Xw(t.signal, this)), qt.call(this, t), Mr.construct(this, () => {
      this._readableState.needReadable && an(this, this._readableState);
    });
  }
  F.prototype.destroy = Mr.destroy;
  F.prototype._undestroy = Mr.undestroy;
  F.prototype._destroy = function(t, e) {
    e(t);
  };
  F.prototype[Yw.captureRejectionSymbol] = function(t) {
    this.destroy(t);
  };
  F.prototype[Kw] = function() {
    let t;
    return this.destroyed || (t = this.readableEnded ? null : new a_(), this.destroy(t)), new Cf((e, r) => kf(this, (i) => i && i !== t ? r(i) : e(null)));
  };
  F.prototype.push = function(t, e) {
    return Lf(this, t, e, false);
  };
  F.prototype.unshift = function(t, e) {
    return Lf(this, t, e, true);
  };
  function Lf(t, e, r, i) {
    H("readableAddChunk", e);
    let n = t._readableState, o;
    if ((n.state & Or) === 0 && (typeof e == "string" ? (r = r || n.defaultEncoding, n.encoding !== r && (i && n.encoding ? e = $s.from(e, r).toString(n.encoding) : (e = $s.from(e, r), r = ""))) : e instanceof $s ? r = "" : qt._isUint8Array(e) ? (e = qt._uint8ArrayToBuffer(e), r = "") : e != null && (o = new r_("chunk", ["string", "Buffer", "Uint8Array"], e))), o) xr(t, o);
    else if (e === null) n.state &= ~ui, v_(t, n);
    else if ((n.state & Or) !== 0 || e && e.length > 0) if (i) if ((n.state & Bf) !== 0) xr(t, new o_());
    else {
      if (n.destroyed || n.errored) return false;
      Vs(t, n, e, true);
    }
    else if (n.ended) xr(t, new s_());
    else {
      if (n.destroyed || n.errored) return false;
      n.state &= ~ui, n.decoder && !r ? (e = n.decoder.write(e), n.objectMode || e.length !== 0 ? Vs(t, n, e, false) : an(t, n)) : Vs(t, n, e, false);
    }
    else i || (n.state &= ~ui, an(t, n));
    return !n.ended && (n.length < n.highWaterMark || n.length === 0);
  }
  function Vs(t, e, r, i) {
    e.flowing && e.length === 0 && !e.sync && t.listenerCount("data") > 0 ? ((e.state & Mf) !== 0 ? e.awaitDrainWriters.clear() : e.awaitDrainWriters = null, e.dataEmitted = true, t.emit("data", r)) : (e.length += e.objectMode ? 1 : r.length, i ? e.buffer.unshift(r) : e.buffer.push(r), (e.state & on) !== 0 && un(t)), an(t, e);
  }
  F.prototype.isPaused = function() {
    let t = this._readableState;
    return t[rr] === true || t.flowing === false;
  };
  F.prototype.setEncoding = function(t) {
    let e = new Pf(t);
    this._readableState.decoder = e, this._readableState.encoding = this._readableState.decoder.encoding;
    let r = this._readableState.buffer, i = "";
    for (let n of r) i += e.write(n);
    return r.clear(), i !== "" && r.push(i), this._readableState.length = i.length, this;
  };
  var __ = 1073741824;
  function m_(t) {
    if (t > __) throw new n_("size", "<= 1GiB", t);
    return t--, t |= t >>> 1, t |= t >>> 2, t |= t >>> 4, t |= t >>> 8, t |= t >>> 16, t++, t;
  }
  function Tf(t, e) {
    return t <= 0 || e.length === 0 && e.ended ? 0 : (e.state & Or) !== 0 ? 1 : $w(t) ? e.flowing && e.length ? e.buffer.first().length : e.length : t <= e.length ? t : e.ended ? e.length : 0;
  }
  F.prototype.read = function(t) {
    H("read", t), t === void 0 ? t = NaN : Ww(t) || (t = Hw(t, 10));
    let e = this._readableState, r = t;
    if (t > e.highWaterMark && (e.highWaterMark = m_(t)), t !== 0 && (e.state &= ~Of), t === 0 && e.needReadable && ((e.highWaterMark !== 0 ? e.length >= e.highWaterMark : e.length > 0) || e.ended)) return H("read: emitReadable", e.length, e.ended), e.length === 0 && e.ended ? zs(this) : un(this), null;
    if (t = Tf(t, e), t === 0 && e.ended) return e.length === 0 && zs(this), null;
    let i = (e.state & on) !== 0;
    if (H("need readable", i), (e.length === 0 || e.length - t < e.highWaterMark) && (i = true, H("length less than watermark", i)), e.ended || e.reading || e.destroyed || e.errored || !e.constructed) i = false, H("reading, ended or constructing", i);
    else if (i) {
      H("do read"), e.state |= ui | sn, e.length === 0 && (e.state |= on);
      try {
        this._read(e.highWaterMark);
      } catch (o) {
        xr(this, o);
      }
      e.state &= ~sn, e.reading || (t = Tf(r, e));
    }
    let n;
    return t > 0 ? n = jf(t, e) : n = null, n === null ? (e.needReadable = e.length <= e.highWaterMark, t = 0) : (e.length -= t, e.multiAwaitDrain ? e.awaitDrainWriters.clear() : e.awaitDrainWriters = null), e.length === 0 && (e.ended || (e.needReadable = true), r !== t && e.ended && zs(this)), n !== null && !e.errorEmitted && !e.closeEmitted && (e.dataEmitted = true, this.emit("data", n)), n;
  };
  function v_(t, e) {
    if (H("onEofChunk"), !e.ended) {
      if (e.decoder) {
        let r = e.decoder.end();
        r && r.length && (e.buffer.push(r), e.length += e.objectMode ? 1 : r.length);
      }
      e.ended = true, e.sync ? un(t) : (e.needReadable = false, e.emittedReadable = true, qf(t));
    }
  }
  function un(t) {
    let e = t._readableState;
    H("emitReadable", e.needReadable, e.emittedReadable), e.needReadable = false, e.emittedReadable || (H("emitReadable", e.flowing), e.emittedReadable = true, We.nextTick(qf, t));
  }
  function qf(t) {
    let e = t._readableState;
    H("emitReadable_", e.destroyed, e.length, e.ended), !e.destroyed && !e.errored && (e.length || e.ended) && (t.emit("readable"), e.emittedReadable = false), e.needReadable = !e.flowing && !e.ended && e.length <= e.highWaterMark, Nf(t);
  }
  function an(t, e) {
    !e.readingMore && e.constructed && (e.readingMore = true, We.nextTick(E_, t, e));
  }
  function E_(t, e) {
    for (; !e.reading && !e.ended && (e.length < e.highWaterMark || e.flowing && e.length === 0); ) {
      let r = e.length;
      if (H("maybeReadMore read 0"), t.read(0), r === e.length) break;
    }
    e.readingMore = false;
  }
  F.prototype._read = function(t) {
    throw new i_("_read()");
  };
  F.prototype.pipe = function(t, e) {
    let r = this, i = this._readableState;
    i.pipes.length === 1 && (i.multiAwaitDrain || (i.multiAwaitDrain = true, i.awaitDrainWriters = new zw(i.awaitDrainWriters ? [i.awaitDrainWriters] : []))), i.pipes.push(t), H("pipe count=%d opts=%j", i.pipes.length, e);
    let o = (!e || e.end !== false) && t !== We.stdout && t !== We.stderr ? a : S;
    i.endEmitted ? We.nextTick(o) : r.once("end", o), t.on("unpipe", s);
    function s(I, C) {
      H("onunpipe"), I === r && C && C.hasUnpiped === false && (C.hasUnpiped = true, d());
    }
    function a() {
      H("onend"), t.end();
    }
    let u, f = false;
    function d() {
      H("cleanup"), t.removeListener("close", E), t.removeListener("finish", w), u && t.removeListener("drain", u), t.removeListener("error", y), t.removeListener("unpipe", s), r.removeListener("end", a), r.removeListener("end", S), r.removeListener("data", g), f = true, u && i.awaitDrainWriters && (!t._writableState || t._writableState.needDrain) && u();
    }
    function h() {
      f || (i.pipes.length === 1 && i.pipes[0] === t ? (H("false write response, pause", 0), i.awaitDrainWriters = t, i.multiAwaitDrain = false) : i.pipes.length > 1 && i.pipes.includes(t) && (H("false write response, pause", i.awaitDrainWriters.size), i.awaitDrainWriters.add(t)), r.pause()), u || (u = S_(r, t), t.on("drain", u));
    }
    r.on("data", g);
    function g(I) {
      H("ondata");
      let C = t.write(I);
      H("dest.write", C), C === false && h();
    }
    function y(I) {
      if (H("onerror", I), S(), t.removeListener("error", y), t.listenerCount("error") === 0) {
        let C = t._writableState || t._readableState;
        C && !C.errorEmitted ? xr(t, I) : t.emit("error", I);
      }
    }
    Jw(t, "error", y);
    function E() {
      t.removeListener("finish", w), S();
    }
    t.once("close", E);
    function w() {
      H("onfinish"), t.removeListener("close", E), S();
    }
    t.once("finish", w);
    function S() {
      H("unpipe"), r.unpipe(t);
    }
    return t.emit("pipe", r), t.writableNeedDrain === true ? h() : i.flowing || (H("pipe resume"), r.resume()), t;
  };
  function S_(t, e) {
    return function() {
      let i = t._readableState;
      i.awaitDrainWriters === e ? (H("pipeOnDrain", 1), i.awaitDrainWriters = null) : i.multiAwaitDrain && (H("pipeOnDrain", i.awaitDrainWriters.size), i.awaitDrainWriters.delete(e)), (!i.awaitDrainWriters || i.awaitDrainWriters.size === 0) && t.listenerCount("data") && t.resume();
    };
  }
  F.prototype.unpipe = function(t) {
    let e = this._readableState, r = { hasUnpiped: false };
    if (e.pipes.length === 0) return this;
    if (!t) {
      let n = e.pipes;
      e.pipes = [], this.pause();
      for (let o = 0; o < n.length; o++) n[o].emit("unpipe", this, { hasUnpiped: false });
      return this;
    }
    let i = Fw(e.pipes, t);
    return i === -1 ? this : (e.pipes.splice(i, 1), e.pipes.length === 0 && this.pause(), t.emit("unpipe", this, r), this);
  };
  F.prototype.on = function(t, e) {
    let r = qt.prototype.on.call(this, t, e), i = this._readableState;
    return t === "data" ? (i.readableListening = this.listenerCount("readable") > 0, i.flowing !== false && this.resume()) : t === "readable" && !i.endEmitted && !i.readableListening && (i.readableListening = i.needReadable = true, i.flowing = false, i.emittedReadable = false, H("on readable", i.length, i.reading), i.length ? un(this) : i.reading || We.nextTick(A_, this)), r;
  };
  F.prototype.addListener = F.prototype.on;
  F.prototype.removeListener = function(t, e) {
    let r = qt.prototype.removeListener.call(this, t, e);
    return t === "readable" && We.nextTick(Uf, this), r;
  };
  F.prototype.off = F.prototype.removeListener;
  F.prototype.removeAllListeners = function(t) {
    let e = qt.prototype.removeAllListeners.apply(this, arguments);
    return (t === "readable" || t === void 0) && We.nextTick(Uf, this), e;
  };
  function Uf(t) {
    let e = t._readableState;
    e.readableListening = t.listenerCount("readable") > 0, e.resumeScheduled && e[rr] === false ? e.flowing = true : t.listenerCount("data") > 0 ? t.resume() : e.readableListening || (e.flowing = null);
  }
  function A_(t) {
    H("readable nexttick read 0"), t.read(0);
  }
  F.prototype.resume = function() {
    let t = this._readableState;
    return t.flowing || (H("resume"), t.flowing = !t.readableListening, I_(this, t)), t[rr] = false, this;
  };
  function I_(t, e) {
    e.resumeScheduled || (e.resumeScheduled = true, We.nextTick(T_, t, e));
  }
  function T_(t, e) {
    H("resume", e.reading), e.reading || t.read(0), e.resumeScheduled = false, t.emit("resume"), Nf(t), e.flowing && !e.reading && t.read(0);
  }
  F.prototype.pause = function() {
    return H("call pause flowing=%j", this._readableState.flowing), this._readableState.flowing !== false && (H("pause"), this._readableState.flowing = false, this.emit("pause")), this._readableState[rr] = true, this;
  };
  function Nf(t) {
    let e = t._readableState;
    for (H("flow", e.flowing); e.flowing && t.read() !== null; ) ;
  }
  F.prototype.wrap = function(t) {
    let e = false;
    t.on("data", (i) => {
      !this.push(i) && t.pause && (e = true, t.pause());
    }), t.on("end", () => {
      this.push(null);
    }), t.on("error", (i) => {
      xr(this, i);
    }), t.on("close", () => {
      this.destroy();
    }), t.on("destroy", () => {
      this.destroy();
    }), this._read = () => {
      e && t.resume && (e = false, t.resume());
    };
    let r = Vw(t);
    for (let i = 1; i < r.length; i++) {
      let n = r[i];
      this[n] === void 0 && typeof t[n] == "function" && (this[n] = t[n].bind(t));
    }
    return this;
  };
  F.prototype[Qw] = function() {
    return Df(this);
  };
  F.prototype.iterator = function(t) {
    return t !== void 0 && l_(t, "options"), Df(this, t);
  };
  function Df(t, e) {
    typeof t.read != "function" && (t = F.wrap(t, { objectMode: true }));
    let r = R_(t, e);
    return r.stream = t, r;
  }
  async function* R_(t, e) {
    let r = Hs;
    function i(s) {
      this === t ? (r(), r = Hs) : r = s;
    }
    t.on("readable", i);
    let n, o = kf(t, { writable: false }, (s) => {
      n = s ? If(n, s) : null, r(), r = Hs;
    });
    try {
      for (; ; ) {
        let s = t.destroyed ? null : t.read();
        if (s !== null) yield s;
        else {
          if (n) throw n;
          if (n === null) return;
          await new Cf(i);
        }
      }
    } catch (s) {
      throw n = If(n, s), n;
    } finally {
      (n || (e == null ? void 0 : e.destroyOnReturn) !== false) && (n === void 0 || t._readableState.autoDestroy) ? Mr.destroyer(t, null) : (t.off("readable", i), o());
    }
  }
  Ys(F.prototype, { readable: { __proto__: null, get() {
    let t = this._readableState;
    return !!t && t.readable !== false && !t.destroyed && !t.errorEmitted && !t.endEmitted;
  }, set(t) {
    this._readableState && (this._readableState.readable = !!t);
  } }, readableDidRead: { __proto__: null, enumerable: false, get: function() {
    return this._readableState.dataEmitted;
  } }, readableAborted: { __proto__: null, enumerable: false, get: function() {
    return !!(this._readableState.readable !== false && (this._readableState.destroyed || this._readableState.errored) && !this._readableState.endEmitted);
  } }, readableHighWaterMark: { __proto__: null, enumerable: false, get: function() {
    return this._readableState.highWaterMark;
  } }, readableBuffer: { __proto__: null, enumerable: false, get: function() {
    return this._readableState && this._readableState.buffer;
  } }, readableFlowing: { __proto__: null, enumerable: false, get: function() {
    return this._readableState.flowing;
  }, set: function(t) {
    this._readableState && (this._readableState.flowing = t);
  } }, readableLength: { __proto__: null, enumerable: false, get() {
    return this._readableState.length;
  } }, readableObjectMode: { __proto__: null, enumerable: false, get() {
    return this._readableState ? this._readableState.objectMode : false;
  } }, readableEncoding: { __proto__: null, enumerable: false, get() {
    return this._readableState ? this._readableState.encoding : null;
  } }, errored: { __proto__: null, enumerable: false, get() {
    return this._readableState ? this._readableState.errored : null;
  } }, closed: { __proto__: null, get() {
    return this._readableState ? this._readableState.closed : false;
  } }, destroyed: { __proto__: null, enumerable: false, get() {
    return this._readableState ? this._readableState.destroyed : false;
  }, set(t) {
    this._readableState && (this._readableState.destroyed = t);
  } }, readableEnded: { __proto__: null, enumerable: false, get() {
    return this._readableState ? this._readableState.endEmitted : false;
  } } });
  Ys(ln.prototype, { pipesCount: { __proto__: null, get() {
    return this.pipes.length;
  } }, paused: { __proto__: null, get() {
    return this[rr] !== false;
  }, set(t) {
    this[rr] = !!t;
  } } });
  F._fromList = jf;
  function jf(t, e) {
    if (e.length === 0) return null;
    let r;
    return e.objectMode ? r = e.buffer.shift() : !t || t >= e.length ? (e.decoder ? r = e.buffer.join("") : e.buffer.length === 1 ? r = e.buffer.first() : r = e.buffer.concat(e.length), e.buffer.clear()) : r = e.buffer.consume(t, e.decoder), r;
  }
  function zs(t) {
    let e = t._readableState;
    H("endReadable", e.endEmitted), e.endEmitted || (e.ended = true, We.nextTick(C_, e, t));
  }
  function C_(t, e) {
    if (H("endReadableNT", t.endEmitted, t.length), !t.errored && !t.closeEmitted && !t.endEmitted && t.length === 0) {
      if (t.endEmitted = true, e.emit("end"), e.writable && e.allowHalfOpen === false) We.nextTick(k_, e);
      else if (t.autoDestroy) {
        let r = e._writableState;
        (!r || r.autoDestroy && (r.finished || r.writable === false)) && e.destroy();
      }
    }
  }
  function k_(t) {
    t.writable && !t.writableEnded && !t.destroyed && t.end();
  }
  F.from = function(t, e) {
    return u_(F, t, e);
  };
  var Ks;
  function Ff() {
    return Ks === void 0 && (Ks = {}), Ks;
  }
  F.fromWeb = function(t, e) {
    return Ff().newStreamReadableFromReadableStream(t, e);
  };
  F.toWeb = function(t, e) {
    return Ff().newReadableStreamFromStreamReadable(t, e);
  };
  F.wrap = function(t, e) {
    var r, i;
    return new F({ objectMode: (r = (i = t.readableObjectMode) !== null && i !== void 0 ? i : t.objectMode) !== null && r !== void 0 ? r : true, ...e, destroy(n, o) {
      Mr.destroyer(t, n), o(n);
    } }).wrap(t);
  };
});
var pn = O((mC, eh) => {
  "use strict";
  _();
  v();
  m();
  var ir = Mt(), { ArrayPrototypeSlice: Vf, Error: P_, FunctionPrototypeSymbolHasInstance: zf, ObjectDefineProperty: Kf, ObjectDefineProperties: B_, ObjectSetPrototypeOf: Qf, StringPrototypeToLowerCase: x_, Symbol: O_, SymbolHasInstance: M_ } = ie();
  eh.exports = se;
  se.WritableState = di;
  var { EventEmitter: L_ } = (Ot(), G(xt)), fi = en().Stream, { Buffer: cn } = (he(), G(be)), dn = er(), { addAbortSignal: q_ } = oi(), { getHighWaterMark: U_, getDefaultHighWaterMark: N_ } = ai(), { ERR_INVALID_ARG_TYPE: D_, ERR_METHOD_NOT_IMPLEMENTED: j_, ERR_MULTIPLE_CALLBACK: Gf, ERR_STREAM_CANNOT_PIPE: F_, ERR_STREAM_DESTROYED: hi, ERR_STREAM_ALREADY_FINISHED: W_, ERR_STREAM_NULL_VALUES: $_, ERR_STREAM_WRITE_AFTER_END: H_, ERR_UNKNOWN_ENCODING: Yf } = me().codes, { errorOrDestroy: Lr } = dn;
  Qf(se.prototype, fi.prototype);
  Qf(se, fi);
  function Zs() {
  }
  var qr = O_("kOnFinished");
  function di(t, e, r) {
    typeof r != "boolean" && (r = e instanceof rt()), this.objectMode = !!(t && t.objectMode), r && (this.objectMode = this.objectMode || !!(t && t.writableObjectMode)), this.highWaterMark = t ? U_(this, t, "writableHighWaterMark", r) : N_(false), this.finalCalled = false, this.needDrain = false, this.ending = false, this.ended = false, this.finished = false, this.destroyed = false;
    let i = !!(t && t.decodeStrings === false);
    this.decodeStrings = !i, this.defaultEncoding = t && t.defaultEncoding || "utf8", this.length = 0, this.writing = false, this.corked = 0, this.sync = true, this.bufferProcessing = false, this.onwrite = z_.bind(void 0, e), this.writecb = null, this.writelen = 0, this.afterWriteTickInfo = null, hn(this), this.pendingcb = 0, this.constructed = true, this.prefinished = false, this.errorEmitted = false, this.emitClose = !t || t.emitClose !== false, this.autoDestroy = !t || t.autoDestroy !== false, this.errored = null, this.closed = false, this.closeEmitted = false, this[qr] = [];
  }
  function hn(t) {
    t.buffered = [], t.bufferedIndex = 0, t.allBuffers = true, t.allNoop = true;
  }
  di.prototype.getBuffer = function() {
    return Vf(this.buffered, this.bufferedIndex);
  };
  Kf(di.prototype, "bufferedRequestCount", { __proto__: null, get() {
    return this.buffered.length - this.bufferedIndex;
  } });
  function se(t) {
    let e = this instanceof rt();
    if (!e && !zf(se, this)) return new se(t);
    this._writableState = new di(t, this, e), t && (typeof t.write == "function" && (this._write = t.write), typeof t.writev == "function" && (this._writev = t.writev), typeof t.destroy == "function" && (this._destroy = t.destroy), typeof t.final == "function" && (this._final = t.final), typeof t.construct == "function" && (this._construct = t.construct), t.signal && q_(t.signal, this)), fi.call(this, t), dn.construct(this, () => {
      let r = this._writableState;
      r.writing || to(this, r), ro(this, r);
    });
  }
  Kf(se, M_, { __proto__: null, value: function(t) {
    return zf(this, t) ? true : this !== se ? false : t && t._writableState instanceof di;
  } });
  se.prototype.pipe = function() {
    Lr(this, new F_());
  };
  function Jf(t, e, r, i) {
    let n = t._writableState;
    if (typeof r == "function") i = r, r = n.defaultEncoding;
    else {
      if (!r) r = n.defaultEncoding;
      else if (r !== "buffer" && !cn.isEncoding(r)) throw new Yf(r);
      typeof i != "function" && (i = Zs);
    }
    if (e === null) throw new $_();
    if (!n.objectMode) if (typeof e == "string") n.decodeStrings !== false && (e = cn.from(e, r), r = "buffer");
    else if (e instanceof cn) r = "buffer";
    else if (fi._isUint8Array(e)) e = fi._uint8ArrayToBuffer(e), r = "buffer";
    else throw new D_("chunk", ["string", "Buffer", "Uint8Array"], e);
    let o;
    return n.ending ? o = new H_() : n.destroyed && (o = new hi("write")), o ? (ir.nextTick(i, o), Lr(t, o, true), o) : (n.pendingcb++, V_(t, n, e, r, i));
  }
  se.prototype.write = function(t, e, r) {
    return Jf(this, t, e, r) === true;
  };
  se.prototype.cork = function() {
    this._writableState.corked++;
  };
  se.prototype.uncork = function() {
    let t = this._writableState;
    t.corked && (t.corked--, t.writing || to(this, t));
  };
  se.prototype.setDefaultEncoding = function(e) {
    if (typeof e == "string" && (e = x_(e)), !cn.isEncoding(e)) throw new Yf(e);
    return this._writableState.defaultEncoding = e, this;
  };
  function V_(t, e, r, i, n) {
    let o = e.objectMode ? 1 : r.length;
    e.length += o;
    let s = e.length < e.highWaterMark;
    return s || (e.needDrain = true), e.writing || e.corked || e.errored || !e.constructed ? (e.buffered.push({ chunk: r, encoding: i, callback: n }), e.allBuffers && i !== "buffer" && (e.allBuffers = false), e.allNoop && n !== Zs && (e.allNoop = false)) : (e.writelen = o, e.writecb = n, e.writing = true, e.sync = true, t._write(r, i, e.onwrite), e.sync = false), s && !e.errored && !e.destroyed;
  }
  function $f(t, e, r, i, n, o, s) {
    e.writelen = i, e.writecb = s, e.writing = true, e.sync = true, e.destroyed ? e.onwrite(new hi("write")) : r ? t._writev(n, e.onwrite) : t._write(n, o, e.onwrite), e.sync = false;
  }
  function Hf(t, e, r, i) {
    --e.pendingcb, i(r), eo(e), Lr(t, r);
  }
  function z_(t, e) {
    let r = t._writableState, i = r.sync, n = r.writecb;
    if (typeof n != "function") {
      Lr(t, new Gf());
      return;
    }
    r.writing = false, r.writecb = null, r.length -= r.writelen, r.writelen = 0, e ? (e.stack, r.errored || (r.errored = e), t._readableState && !t._readableState.errored && (t._readableState.errored = e), i ? ir.nextTick(Hf, t, r, e, n) : Hf(t, r, e, n)) : (r.buffered.length > r.bufferedIndex && to(t, r), i ? r.afterWriteTickInfo !== null && r.afterWriteTickInfo.cb === n ? r.afterWriteTickInfo.count++ : (r.afterWriteTickInfo = { count: 1, cb: n, stream: t, state: r }, ir.nextTick(K_, r.afterWriteTickInfo)) : Xf(t, r, 1, n));
  }
  function K_({ stream: t, state: e, count: r, cb: i }) {
    return e.afterWriteTickInfo = null, Xf(t, e, r, i);
  }
  function Xf(t, e, r, i) {
    for (!e.ending && !t.destroyed && e.length === 0 && e.needDrain && (e.needDrain = false, t.emit("drain")); r-- > 0; ) e.pendingcb--, i();
    e.destroyed && eo(e), ro(t, e);
  }
  function eo(t) {
    if (t.writing) return;
    for (let n = t.bufferedIndex; n < t.buffered.length; ++n) {
      var e;
      let { chunk: o, callback: s } = t.buffered[n], a = t.objectMode ? 1 : o.length;
      t.length -= a, s((e = t.errored) !== null && e !== void 0 ? e : new hi("write"));
    }
    let r = t[qr].splice(0);
    for (let n = 0; n < r.length; n++) {
      var i;
      r[n]((i = t.errored) !== null && i !== void 0 ? i : new hi("end"));
    }
    hn(t);
  }
  function to(t, e) {
    if (e.corked || e.bufferProcessing || e.destroyed || !e.constructed) return;
    let { buffered: r, bufferedIndex: i, objectMode: n } = e, o = r.length - i;
    if (!o) return;
    let s = i;
    if (e.bufferProcessing = true, o > 1 && t._writev) {
      e.pendingcb -= o - 1;
      let a = e.allNoop ? Zs : (f) => {
        for (let d = s; d < r.length; ++d) r[d].callback(f);
      }, u = e.allNoop && s === 0 ? r : Vf(r, s);
      u.allBuffers = e.allBuffers, $f(t, e, true, e.length, u, "", a), hn(e);
    } else {
      do {
        let { chunk: a, encoding: u, callback: f } = r[s];
        r[s++] = null;
        let d = n ? 1 : a.length;
        $f(t, e, false, d, a, u, f);
      } while (s < r.length && !e.writing);
      s === r.length ? hn(e) : s > 256 ? (r.splice(0, s), e.bufferedIndex = 0) : e.bufferedIndex = s;
    }
    e.bufferProcessing = false;
  }
  se.prototype._write = function(t, e, r) {
    if (this._writev) this._writev([{ chunk: t, encoding: e }], r);
    else throw new j_("_write()");
  };
  se.prototype._writev = null;
  se.prototype.end = function(t, e, r) {
    let i = this._writableState;
    typeof t == "function" ? (r = t, t = null, e = null) : typeof e == "function" && (r = e, e = null);
    let n;
    if (t != null) {
      let o = Jf(this, t, e);
      o instanceof P_ && (n = o);
    }
    return i.corked && (i.corked = 1, this.uncork()), n || (!i.errored && !i.ending ? (i.ending = true, ro(this, i, true), i.ended = true) : i.finished ? n = new W_("end") : i.destroyed && (n = new hi("end"))), typeof r == "function" && (n || i.finished ? ir.nextTick(r, n) : i[qr].push(r)), this;
  };
  function fn(t) {
    return t.ending && !t.destroyed && t.constructed && t.length === 0 && !t.errored && t.buffered.length === 0 && !t.finished && !t.writing && !t.errorEmitted && !t.closeEmitted;
  }
  function Q_(t, e) {
    let r = false;
    function i(n) {
      if (r) {
        Lr(t, n ?? Gf());
        return;
      }
      if (r = true, e.pendingcb--, n) {
        let o = e[qr].splice(0);
        for (let s = 0; s < o.length; s++) o[s](n);
        Lr(t, n, e.sync);
      } else fn(e) && (e.prefinished = true, t.emit("prefinish"), e.pendingcb++, ir.nextTick(Xs, t, e));
    }
    e.sync = true, e.pendingcb++;
    try {
      t._final(i);
    } catch (n) {
      i(n);
    }
    e.sync = false;
  }
  function G_(t, e) {
    !e.prefinished && !e.finalCalled && (typeof t._final == "function" && !e.destroyed ? (e.finalCalled = true, Q_(t, e)) : (e.prefinished = true, t.emit("prefinish")));
  }
  function ro(t, e, r) {
    fn(e) && (G_(t, e), e.pendingcb === 0 && (r ? (e.pendingcb++, ir.nextTick((i, n) => {
      fn(n) ? Xs(i, n) : n.pendingcb--;
    }, t, e)) : fn(e) && (e.pendingcb++, Xs(t, e))));
  }
  function Xs(t, e) {
    e.pendingcb--, e.finished = true;
    let r = e[qr].splice(0);
    for (let i = 0; i < r.length; i++) r[i]();
    if (t.emit("finish"), e.autoDestroy) {
      let i = t._readableState;
      (!i || i.autoDestroy && (i.endEmitted || i.readable === false)) && t.destroy();
    }
  }
  B_(se.prototype, { closed: { __proto__: null, get() {
    return this._writableState ? this._writableState.closed : false;
  } }, destroyed: { __proto__: null, get() {
    return this._writableState ? this._writableState.destroyed : false;
  }, set(t) {
    this._writableState && (this._writableState.destroyed = t);
  } }, writable: { __proto__: null, get() {
    let t = this._writableState;
    return !!t && t.writable !== false && !t.destroyed && !t.errored && !t.ending && !t.ended;
  }, set(t) {
    this._writableState && (this._writableState.writable = !!t);
  } }, writableFinished: { __proto__: null, get() {
    return this._writableState ? this._writableState.finished : false;
  } }, writableObjectMode: { __proto__: null, get() {
    return this._writableState ? this._writableState.objectMode : false;
  } }, writableBuffer: { __proto__: null, get() {
    return this._writableState && this._writableState.getBuffer();
  } }, writableEnded: { __proto__: null, get() {
    return this._writableState ? this._writableState.ending : false;
  } }, writableNeedDrain: { __proto__: null, get() {
    let t = this._writableState;
    return t ? !t.destroyed && !t.ending && t.needDrain : false;
  } }, writableHighWaterMark: { __proto__: null, get() {
    return this._writableState && this._writableState.highWaterMark;
  } }, writableCorked: { __proto__: null, get() {
    return this._writableState ? this._writableState.corked : 0;
  } }, writableLength: { __proto__: null, get() {
    return this._writableState && this._writableState.length;
  } }, errored: { __proto__: null, enumerable: false, get() {
    return this._writableState ? this._writableState.errored : null;
  } }, writableAborted: { __proto__: null, enumerable: false, get: function() {
    return !!(this._writableState.writable !== false && (this._writableState.destroyed || this._writableState.errored) && !this._writableState.finished);
  } } });
  var Y_ = dn.destroy;
  se.prototype.destroy = function(t, e) {
    let r = this._writableState;
    return !r.destroyed && (r.bufferedIndex < r.buffered.length || r[qr].length) && ir.nextTick(eo, r), Y_.call(this, t, e), this;
  };
  se.prototype._undestroy = dn.undestroy;
  se.prototype._destroy = function(t, e) {
    e(t);
  };
  se.prototype[L_.captureRejectionSymbol] = function(t) {
    this.destroy(t);
  };
  var Js;
  function Zf() {
    return Js === void 0 && (Js = {}), Js;
  }
  se.fromWeb = function(t, e) {
    return Zf().newStreamWritableFromWritableStream(t, e);
  };
  se.toWeb = function(t) {
    return Zf().newWritableStreamFromStreamWritable(t);
  };
});
var gh = O((PC, ph) => {
  _();
  v();
  m();
  var io = Mt(), J_ = (he(), G(be)), { isReadable: X_, isWritable: Z_, isIterable: th, isNodeStream: em, isReadableNodeStream: rh, isWritableNodeStream: ih, isDuplexNodeStream: tm, isReadableStream: nh, isWritableStream: sh } = Ze(), oh = _t(), { AbortError: hh, codes: { ERR_INVALID_ARG_TYPE: rm, ERR_INVALID_RETURN_VALUE: ah } } = me(), { destroyer: Nr } = er(), im = rt(), dh = ci(), nm = pn(), { createDeferredPromise: lh } = Ie(), uh = Ws(), ch = globalThis.Blob || J_.Blob, sm = typeof ch < "u" ? function(e) {
    return e instanceof ch;
  } : function(e) {
    return false;
  }, om = globalThis.AbortController || Tr().AbortController, { FunctionPrototypeCall: fh } = ie(), Ut = class extends im {
    constructor(e) {
      super(e), (e == null ? void 0 : e.readable) === false && (this._readableState.readable = false, this._readableState.ended = true, this._readableState.endEmitted = true), (e == null ? void 0 : e.writable) === false && (this._writableState.writable = false, this._writableState.ending = true, this._writableState.ended = true, this._writableState.finished = true);
    }
  };
  ph.exports = function t(e, r) {
    if (tm(e)) return e;
    if (rh(e)) return Ur({ readable: e });
    if (ih(e)) return Ur({ writable: e });
    if (em(e)) return Ur({ writable: false, readable: false });
    if (nh(e)) return Ur({ readable: dh.fromWeb(e) });
    if (sh(e)) return Ur({ writable: nm.fromWeb(e) });
    if (typeof e == "function") {
      let { value: n, write: o, final: s, destroy: a } = am(e);
      if (th(n)) return uh(Ut, n, { objectMode: true, write: o, final: s, destroy: a });
      let u = n == null ? void 0 : n.then;
      if (typeof u == "function") {
        let f, d = fh(u, n, (h) => {
          if (h != null) throw new ah("nully", "body", h);
        }, (h) => {
          Nr(f, h);
        });
        return f = new Ut({ objectMode: true, readable: false, write: o, final(h) {
          s(async () => {
            try {
              await d, io.nextTick(h, null);
            } catch (g) {
              io.nextTick(h, g);
            }
          });
        }, destroy: a });
      }
      throw new ah("Iterable, AsyncIterable or AsyncFunction", r, n);
    }
    if (sm(e)) return t(e.arrayBuffer());
    if (th(e)) return uh(Ut, e, { objectMode: true, writable: false });
    if (nh(e == null ? void 0 : e.readable) && sh(e == null ? void 0 : e.writable)) return Ut.fromWeb(e);
    if (typeof (e == null ? void 0 : e.writable) == "object" || typeof (e == null ? void 0 : e.readable) == "object") {
      let n = e != null && e.readable ? rh(e == null ? void 0 : e.readable) ? e == null ? void 0 : e.readable : t(e.readable) : void 0, o = e != null && e.writable ? ih(e == null ? void 0 : e.writable) ? e == null ? void 0 : e.writable : t(e.writable) : void 0;
      return Ur({ readable: n, writable: o });
    }
    let i = e == null ? void 0 : e.then;
    if (typeof i == "function") {
      let n;
      return fh(i, e, (o) => {
        o != null && n.push(o), n.push(null);
      }, (o) => {
        Nr(n, o);
      }), n = new Ut({ objectMode: true, writable: false, read() {
      } });
    }
    throw new rm(r, ["Blob", "ReadableStream", "WritableStream", "Stream", "Iterable", "AsyncIterable", "Function", "{ readable, writable } pair", "Promise"], e);
  };
  function am(t) {
    let { promise: e, resolve: r } = lh(), i = new om(), n = i.signal;
    return { value: t(async function* () {
      for (; ; ) {
        let s = e;
        e = null;
        let { chunk: a, done: u, cb: f } = await s;
        if (io.nextTick(f), u) return;
        if (n.aborted) throw new hh(void 0, { cause: n.reason });
        ({ promise: e, resolve: r } = lh()), yield a;
      }
    }(), { signal: n }), write(s, a, u) {
      let f = r;
      r = null, f({ chunk: s, done: false, cb: u });
    }, final(s) {
      let a = r;
      r = null, a({ done: true, cb: s });
    }, destroy(s, a) {
      i.abort(), a(s);
    } };
  }
  function Ur(t) {
    let e = t.readable && typeof t.readable.read != "function" ? dh.wrap(t.readable) : t.readable, r = t.writable, i = !!X_(e), n = !!Z_(r), o, s, a, u, f;
    function d(h) {
      let g = u;
      u = null, g ? g(h) : h && f.destroy(h);
    }
    return f = new Ut({ readableObjectMode: !!(e != null && e.readableObjectMode), writableObjectMode: !!(r != null && r.writableObjectMode), readable: i, writable: n }), n && (oh(r, (h) => {
      n = false, h && Nr(e, h), d(h);
    }), f._write = function(h, g, y) {
      r.write(h, g) ? y() : o = y;
    }, f._final = function(h) {
      r.end(), s = h;
    }, r.on("drain", function() {
      if (o) {
        let h = o;
        o = null, h();
      }
    }), r.on("finish", function() {
      if (s) {
        let h = s;
        s = null, h();
      }
    })), i && (oh(e, (h) => {
      i = false, h && Nr(e, h), d(h);
    }), e.on("readable", function() {
      if (a) {
        let h = a;
        a = null, h();
      }
    }), e.on("end", function() {
      f.push(null);
    }), f._read = function() {
      for (; ; ) {
        let h = e.read();
        if (h === null) {
          a = f._read;
          return;
        }
        if (!f.push(h)) return;
      }
    }), f._destroy = function(h, g) {
      !h && u !== null && (h = new hh()), a = null, o = null, s = null, u === null ? g(h) : (u = g, Nr(r, h), Nr(e, h));
    }, f;
  }
});
var rt = O((jC, wh) => {
  "use strict";
  _();
  v();
  m();
  var { ObjectDefineProperties: lm, ObjectGetOwnPropertyDescriptor: mt, ObjectKeys: um, ObjectSetPrototypeOf: yh } = ie();
  wh.exports = $e;
  var oo = ci(), Le = pn();
  yh($e.prototype, oo.prototype);
  yh($e, oo);
  {
    let t = um(Le.prototype);
    for (let e = 0; e < t.length; e++) {
      let r = t[e];
      $e.prototype[r] || ($e.prototype[r] = Le.prototype[r]);
    }
  }
  function $e(t) {
    if (!(this instanceof $e)) return new $e(t);
    oo.call(this, t), Le.call(this, t), t ? (this.allowHalfOpen = t.allowHalfOpen !== false, t.readable === false && (this._readableState.readable = false, this._readableState.ended = true, this._readableState.endEmitted = true), t.writable === false && (this._writableState.writable = false, this._writableState.ending = true, this._writableState.ended = true, this._writableState.finished = true)) : this.allowHalfOpen = true;
  }
  lm($e.prototype, { writable: { __proto__: null, ...mt(Le.prototype, "writable") }, writableHighWaterMark: { __proto__: null, ...mt(Le.prototype, "writableHighWaterMark") }, writableObjectMode: { __proto__: null, ...mt(Le.prototype, "writableObjectMode") }, writableBuffer: { __proto__: null, ...mt(Le.prototype, "writableBuffer") }, writableLength: { __proto__: null, ...mt(Le.prototype, "writableLength") }, writableFinished: { __proto__: null, ...mt(Le.prototype, "writableFinished") }, writableCorked: { __proto__: null, ...mt(Le.prototype, "writableCorked") }, writableEnded: { __proto__: null, ...mt(Le.prototype, "writableEnded") }, writableNeedDrain: { __proto__: null, ...mt(Le.prototype, "writableNeedDrain") }, destroyed: { __proto__: null, get() {
    return this._readableState === void 0 || this._writableState === void 0 ? false : this._readableState.destroyed && this._writableState.destroyed;
  }, set(t) {
    this._readableState && this._writableState && (this._readableState.destroyed = t, this._writableState.destroyed = t);
  } } });
  var no;
  function bh() {
    return no === void 0 && (no = {}), no;
  }
  $e.fromWeb = function(t, e) {
    return bh().newStreamDuplexFromReadableWritablePair(t, e);
  };
  $e.toWeb = function(t) {
    return bh().newReadableWritablePairFromDuplex(t);
  };
  var so;
  $e.from = function(t) {
    return so || (so = gh()), so(t, "body");
  };
});
var uo = O((YC, mh) => {
  "use strict";
  _();
  v();
  m();
  var { ObjectSetPrototypeOf: _h, Symbol: cm } = ie();
  mh.exports = vt;
  var { ERR_METHOD_NOT_IMPLEMENTED: fm } = me().codes, lo = rt(), { getHighWaterMark: hm } = ai();
  _h(vt.prototype, lo.prototype);
  _h(vt, lo);
  var pi = cm("kCallback");
  function vt(t) {
    if (!(this instanceof vt)) return new vt(t);
    let e = t ? hm(this, t, "readableHighWaterMark", true) : null;
    e === 0 && (t = { ...t, highWaterMark: null, readableHighWaterMark: e, writableHighWaterMark: t.writableHighWaterMark || 0 }), lo.call(this, t), this._readableState.sync = false, this[pi] = null, t && (typeof t.transform == "function" && (this._transform = t.transform), typeof t.flush == "function" && (this._flush = t.flush)), this.on("prefinish", dm);
  }
  function ao(t) {
    typeof this._flush == "function" && !this.destroyed ? this._flush((e, r) => {
      if (e) {
        t ? t(e) : this.destroy(e);
        return;
      }
      r != null && this.push(r), this.push(null), t && t();
    }) : (this.push(null), t && t());
  }
  function dm() {
    this._final !== ao && ao.call(this);
  }
  vt.prototype._final = ao;
  vt.prototype._transform = function(t, e, r) {
    throw new fm("_transform()");
  };
  vt.prototype._write = function(t, e, r) {
    let i = this._readableState, n = this._writableState, o = i.length;
    this._transform(t, e, (s, a) => {
      if (s) {
        r(s);
        return;
      }
      a != null && this.push(a), n.ended || o === i.length || i.length < i.highWaterMark ? r() : this[pi] = r;
    });
  };
  vt.prototype._read = function() {
    if (this[pi]) {
      let t = this[pi];
      this[pi] = null, t();
    }
  };
});
var fo = O((ok, Eh) => {
  "use strict";
  _();
  v();
  m();
  var { ObjectSetPrototypeOf: vh } = ie();
  Eh.exports = Dr;
  var co = uo();
  vh(Dr.prototype, co.prototype);
  vh(Dr, co);
  function Dr(t) {
    if (!(this instanceof Dr)) return new Dr(t);
    co.call(this, t);
  }
  Dr.prototype._transform = function(t, e, r) {
    r(null, t);
  };
});
var wn = O((yk, Rh) => {
  _();
  v();
  m();
  var gi = Mt(), { ArrayIsArray: pm, Promise: gm, SymbolAsyncIterator: ym, SymbolDispose: bm } = ie(), bn = _t(), { once: wm } = Ie(), _m = er(), Sh = rt(), { aggregateTwoErrors: mm, codes: { ERR_INVALID_ARG_TYPE: vo, ERR_INVALID_RETURN_VALUE: ho, ERR_MISSING_ARGS: vm, ERR_STREAM_DESTROYED: Em, ERR_STREAM_PREMATURE_CLOSE: Sm }, AbortError: Am } = me(), { validateFunction: Im, validateAbortSignal: Tm } = kr(), { isIterable: nr, isReadable: po, isReadableNodeStream: yn, isNodeStream: Ah, isTransformStream: jr, isWebStream: Rm, isReadableStream: go, isReadableFinished: Cm } = Ze(), km = globalThis.AbortController || Tr().AbortController, yo, bo, wo;
  function Ih(t, e, r) {
    let i = false;
    t.on("close", () => {
      i = true;
    });
    let n = bn(t, { readable: e, writable: r }, (o) => {
      i = !o;
    });
    return { destroy: (o) => {
      i || (i = true, _m.destroyer(t, o || new Em("pipe")));
    }, cleanup: n };
  }
  function Pm(t) {
    return Im(t[t.length - 1], "streams[stream.length - 1]"), t.pop();
  }
  function _o(t) {
    if (nr(t)) return t;
    if (yn(t)) return Bm(t);
    throw new vo("val", ["Readable", "Iterable", "AsyncIterable"], t);
  }
  async function* Bm(t) {
    bo || (bo = ci()), yield* bo.prototype[ym].call(t);
  }
  async function gn(t, e, r, { end: i }) {
    let n, o = null, s = (f) => {
      if (f && (n = f), o) {
        let d = o;
        o = null, d();
      }
    }, a = () => new gm((f, d) => {
      n ? d(n) : o = () => {
        n ? d(n) : f();
      };
    });
    e.on("drain", s);
    let u = bn(e, { readable: false }, s);
    try {
      e.writableNeedDrain && await a();
      for await (let f of t) e.write(f) || await a();
      i && (e.end(), await a()), r();
    } catch (f) {
      r(n !== f ? mm(n, f) : f);
    } finally {
      u(), e.off("drain", s);
    }
  }
  async function mo(t, e, r, { end: i }) {
    jr(e) && (e = e.writable);
    let n = e.getWriter();
    try {
      for await (let o of t) await n.ready, n.write(o).catch(() => {
      });
      await n.ready, i && await n.close(), r();
    } catch (o) {
      try {
        await n.abort(o), r(o);
      } catch (s) {
        r(s);
      }
    }
  }
  function xm(...t) {
    return Th(t, wm(Pm(t)));
  }
  function Th(t, e, r) {
    if (t.length === 1 && pm(t[0]) && (t = t[0]), t.length < 2) throw new vm("streams");
    let i = new km(), n = i.signal, o = r == null ? void 0 : r.signal, s = [];
    Tm(o, "options.signal");
    function a() {
      E(new Am());
    }
    wo = wo || Ie().addAbortListener;
    let u;
    o && (u = wo(o, a));
    let f, d, h = [], g = 0;
    function y(k) {
      E(k, --g === 0);
    }
    function E(k, M) {
      var q;
      if (k && (!f || f.code === "ERR_STREAM_PREMATURE_CLOSE") && (f = k), !(!f && !M)) {
        for (; h.length; ) h.shift()(f);
        (q = u) === null || q === void 0 || q[bm](), i.abort(), M && (f || s.forEach((z) => z()), gi.nextTick(e, f, d));
      }
    }
    let w;
    for (let k = 0; k < t.length; k++) {
      let M = t[k], q = k < t.length - 1, z = k > 0, j = q || (r == null ? void 0 : r.end) !== false, Q = k === t.length - 1;
      if (Ah(M)) {
        let $ = function(te) {
          te && te.name !== "AbortError" && te.code !== "ERR_STREAM_PREMATURE_CLOSE" && y(te);
        };
        var C = $;
        if (j) {
          let { destroy: te, cleanup: pt } = Ih(M, q, z);
          h.push(te), po(M) && Q && s.push(pt);
        }
        M.on("error", $), po(M) && Q && s.push(() => {
          M.removeListener("error", $);
        });
      }
      if (k === 0) if (typeof M == "function") {
        if (w = M({ signal: n }), !nr(w)) throw new ho("Iterable, AsyncIterable or Stream", "source", w);
      } else nr(M) || yn(M) || jr(M) ? w = M : w = Sh.from(M);
      else if (typeof M == "function") {
        if (jr(w)) {
          var S;
          w = _o((S = w) === null || S === void 0 ? void 0 : S.readable);
        } else w = _o(w);
        if (w = M(w, { signal: n }), q) {
          if (!nr(w, true)) throw new ho("AsyncIterable", `transform[${k - 1}]`, w);
        } else {
          var I;
          yo || (yo = fo());
          let $ = new yo({ objectMode: true }), te = (I = w) === null || I === void 0 ? void 0 : I.then;
          if (typeof te == "function") g++, te.call(w, (Se) => {
            d = Se, Se != null && $.write(Se), j && $.end(), gi.nextTick(y);
          }, (Se) => {
            $.destroy(Se), gi.nextTick(y, Se);
          });
          else if (nr(w, true)) g++, gn(w, $, y, { end: j });
          else if (go(w) || jr(w)) {
            let Se = w.readable || w;
            g++, gn(Se, $, y, { end: j });
          } else throw new ho("AsyncIterable or Promise", "destination", w);
          w = $;
          let { destroy: pt, cleanup: Fe } = Ih(w, false, true);
          h.push(pt), Q && s.push(Fe);
        }
      } else if (Ah(M)) {
        if (yn(w)) {
          g += 2;
          let $ = Om(w, M, y, { end: j });
          po(M) && Q && s.push($);
        } else if (jr(w) || go(w)) {
          let $ = w.readable || w;
          g++, gn($, M, y, { end: j });
        } else if (nr(w)) g++, gn(w, M, y, { end: j });
        else throw new vo("val", ["Readable", "Iterable", "AsyncIterable", "ReadableStream", "TransformStream"], w);
        w = M;
      } else if (Rm(M)) {
        if (yn(w)) g++, mo(_o(w), M, y, { end: j });
        else if (go(w) || nr(w)) g++, mo(w, M, y, { end: j });
        else if (jr(w)) g++, mo(w.readable, M, y, { end: j });
        else throw new vo("val", ["Readable", "Iterable", "AsyncIterable", "ReadableStream", "TransformStream"], w);
        w = M;
      } else w = Sh.from(M);
    }
    return (n != null && n.aborted || o != null && o.aborted) && gi.nextTick(a), w;
  }
  function Om(t, e, r, { end: i }) {
    let n = false;
    if (e.on("close", () => {
      n || r(new Sm());
    }), t.pipe(e, { end: false }), i) {
      let s = function() {
        n = true, e.end();
      };
      var o = s;
      Cm(t) ? gi.nextTick(s) : t.once("end", s);
    } else r();
    return bn(t, { readable: true, writable: false }, (s) => {
      let a = t._readableState;
      s && s.code === "ERR_STREAM_PREMATURE_CLOSE" && a && a.ended && !a.errored && !a.errorEmitted ? t.once("end", r).once("error", r) : r(s);
    }), bn(e, { readable: false, writable: true }, r);
  }
  Rh.exports = { pipelineImpl: Th, pipeline: xm };
});
var So = O((Tk, Oh) => {
  "use strict";
  _();
  v();
  m();
  var { pipeline: Mm } = wn(), _n = rt(), { destroyer: Lm } = er(), { isNodeStream: mn, isReadable: Ch, isWritable: kh, isWebStream: Eo, isTransformStream: sr, isWritableStream: Ph, isReadableStream: Bh } = Ze(), { AbortError: qm, codes: { ERR_INVALID_ARG_VALUE: xh, ERR_MISSING_ARGS: Um } } = me(), Nm = _t();
  Oh.exports = function(...e) {
    if (e.length === 0) throw new Um("streams");
    if (e.length === 1) return _n.from(e[0]);
    let r = [...e];
    if (typeof e[0] == "function" && (e[0] = _n.from(e[0])), typeof e[e.length - 1] == "function") {
      let y = e.length - 1;
      e[y] = _n.from(e[y]);
    }
    for (let y = 0; y < e.length; ++y) if (!(!mn(e[y]) && !Eo(e[y]))) {
      if (y < e.length - 1 && !(Ch(e[y]) || Bh(e[y]) || sr(e[y]))) throw new xh(`streams[${y}]`, r[y], "must be readable");
      if (y > 0 && !(kh(e[y]) || Ph(e[y]) || sr(e[y]))) throw new xh(`streams[${y}]`, r[y], "must be writable");
    }
    let i, n, o, s, a;
    function u(y) {
      let E = s;
      s = null, E ? E(y) : y ? a.destroy(y) : !g && !h && a.destroy();
    }
    let f = e[0], d = Mm(e, u), h = !!(kh(f) || Ph(f) || sr(f)), g = !!(Ch(d) || Bh(d) || sr(d));
    if (a = new _n({ writableObjectMode: !!(f != null && f.writableObjectMode), readableObjectMode: !!(d != null && d.readableObjectMode), writable: h, readable: g }), h) {
      if (mn(f)) a._write = function(E, w, S) {
        f.write(E, w) ? S() : i = S;
      }, a._final = function(E) {
        f.end(), n = E;
      }, f.on("drain", function() {
        if (i) {
          let E = i;
          i = null, E();
        }
      });
      else if (Eo(f)) {
        let w = (sr(f) ? f.writable : f).getWriter();
        a._write = async function(S, I, C) {
          try {
            await w.ready, w.write(S).catch(() => {
            }), C();
          } catch (k) {
            C(k);
          }
        }, a._final = async function(S) {
          try {
            await w.ready, w.close().catch(() => {
            }), n = S;
          } catch (I) {
            S(I);
          }
        };
      }
      let y = sr(d) ? d.readable : d;
      Nm(y, () => {
        if (n) {
          let E = n;
          n = null, E();
        }
      });
    }
    if (g) {
      if (mn(d)) d.on("readable", function() {
        if (o) {
          let y = o;
          o = null, y();
        }
      }), d.on("end", function() {
        a.push(null);
      }), a._read = function() {
        for (; ; ) {
          let y = d.read();
          if (y === null) {
            o = a._read;
            return;
          }
          if (!a.push(y)) return;
        }
      };
      else if (Eo(d)) {
        let E = (sr(d) ? d.readable : d).getReader();
        a._read = async function() {
          for (; ; ) try {
            let { value: w, done: S } = await E.read();
            if (!a.push(w)) return;
            if (S) {
              a.push(null);
              return;
            }
          } catch {
            return;
          }
        };
      }
    }
    return a._destroy = function(y, E) {
      !y && s !== null && (y = new qm()), o = null, i = null, n = null, s === null ? E(y) : (s = E, mn(d) && Lm(d, y));
    }, a;
  };
});
var $h = O((qk, To) => {
  "use strict";
  _();
  v();
  m();
  var Dm = globalThis.AbortController || Tr().AbortController, { codes: { ERR_INVALID_ARG_VALUE: jm, ERR_INVALID_ARG_TYPE: yi, ERR_MISSING_ARGS: Fm, ERR_OUT_OF_RANGE: Wm }, AbortError: it } = me(), { validateAbortSignal: or, validateInteger: Mh, validateObject: ar } = kr(), $m = ie().Symbol("kWeak"), Hm = ie().Symbol("kResistStopPropagation"), { finished: Vm } = _t(), zm = So(), { addAbortSignalNoValidate: Km } = oi(), { isWritable: Qm, isNodeStream: Gm } = Ze(), { deprecate: Ym } = Ie(), { ArrayPrototypePush: Jm, Boolean: Xm, MathFloor: Lh, Number: Zm, NumberIsNaN: e0, Promise: qh, PromiseReject: Uh, PromiseResolve: t0, PromisePrototypeThen: Nh, Symbol: jh } = ie(), vn = jh("kEmpty"), Dh = jh("kEof");
  function r0(t, e) {
    if (e != null && ar(e, "options"), (e == null ? void 0 : e.signal) != null && or(e.signal, "options.signal"), Gm(t) && !Qm(t)) throw new jm("stream", t, "must be writable");
    let r = zm(this, t);
    return e != null && e.signal && Km(e.signal, r), r;
  }
  function En(t, e) {
    if (typeof t != "function") throw new yi("fn", ["Function", "AsyncFunction"], t);
    e != null && ar(e, "options"), (e == null ? void 0 : e.signal) != null && or(e.signal, "options.signal");
    let r = 1;
    (e == null ? void 0 : e.concurrency) != null && (r = Lh(e.concurrency));
    let i = r - 1;
    return (e == null ? void 0 : e.highWaterMark) != null && (i = Lh(e.highWaterMark)), Mh(r, "options.concurrency", 1), Mh(i, "options.highWaterMark", 0), i += r, (async function* () {
      let o = Ie().AbortSignalAny([e == null ? void 0 : e.signal].filter(Xm)), s = this, a = [], u = { signal: o }, f, d, h = false, g = 0;
      function y() {
        h = true, E();
      }
      function E() {
        g -= 1, w();
      }
      function w() {
        d && !h && g < r && a.length < i && (d(), d = null);
      }
      async function S() {
        try {
          for await (let I of s) {
            if (h) return;
            if (o.aborted) throw new it();
            try {
              if (I = t(I, u), I === vn) continue;
              I = t0(I);
            } catch (C) {
              I = Uh(C);
            }
            g += 1, Nh(I, E, y), a.push(I), f && (f(), f = null), !h && (a.length >= i || g >= r) && await new qh((C) => {
              d = C;
            });
          }
          a.push(Dh);
        } catch (I) {
          let C = Uh(I);
          Nh(C, E, y), a.push(C);
        } finally {
          h = true, f && (f(), f = null);
        }
      }
      S();
      try {
        for (; ; ) {
          for (; a.length > 0; ) {
            let I = await a[0];
            if (I === Dh) return;
            if (o.aborted) throw new it();
            I !== vn && (yield I), a.shift(), w();
          }
          await new qh((I) => {
            f = I;
          });
        }
      } finally {
        h = true, d && (d(), d = null);
      }
    }).call(this);
  }
  function i0(t = void 0) {
    return t != null && ar(t, "options"), (t == null ? void 0 : t.signal) != null && or(t.signal, "options.signal"), (async function* () {
      let r = 0;
      for await (let n of this) {
        var i;
        if (t != null && (i = t.signal) !== null && i !== void 0 && i.aborted) throw new it({ cause: t.signal.reason });
        yield [r++, n];
      }
    }).call(this);
  }
  async function Fh(t, e = void 0) {
    for await (let r of Io.call(this, t, e)) return true;
    return false;
  }
  async function n0(t, e = void 0) {
    if (typeof t != "function") throw new yi("fn", ["Function", "AsyncFunction"], t);
    return !await Fh.call(this, async (...r) => !await t(...r), e);
  }
  async function s0(t, e) {
    for await (let r of Io.call(this, t, e)) return r;
  }
  async function o0(t, e) {
    if (typeof t != "function") throw new yi("fn", ["Function", "AsyncFunction"], t);
    async function r(i, n) {
      return await t(i, n), vn;
    }
    for await (let i of En.call(this, r, e)) ;
  }
  function Io(t, e) {
    if (typeof t != "function") throw new yi("fn", ["Function", "AsyncFunction"], t);
    async function r(i, n) {
      return await t(i, n) ? i : vn;
    }
    return En.call(this, r, e);
  }
  var Ao = class extends Fm {
    constructor() {
      super("reduce"), this.message = "Reduce of an empty stream requires an initial value";
    }
  };
  async function a0(t, e, r) {
    var i;
    if (typeof t != "function") throw new yi("reducer", ["Function", "AsyncFunction"], t);
    r != null && ar(r, "options"), (r == null ? void 0 : r.signal) != null && or(r.signal, "options.signal");
    let n = arguments.length > 1;
    if (r != null && (i = r.signal) !== null && i !== void 0 && i.aborted) {
      let f = new it(void 0, { cause: r.signal.reason });
      throw this.once("error", () => {
      }), await Vm(this.destroy(f)), f;
    }
    let o = new Dm(), s = o.signal;
    if (r != null && r.signal) {
      let f = { once: true, [$m]: this, [Hm]: true };
      r.signal.addEventListener("abort", () => o.abort(), f);
    }
    let a = false;
    try {
      for await (let f of this) {
        var u;
        if (a = true, r != null && (u = r.signal) !== null && u !== void 0 && u.aborted) throw new it();
        n ? e = await t(e, f, { signal: s }) : (e = f, n = true);
      }
      if (!a && !n) throw new Ao();
    } finally {
      o.abort();
    }
    return e;
  }
  async function l0(t) {
    t != null && ar(t, "options"), (t == null ? void 0 : t.signal) != null && or(t.signal, "options.signal");
    let e = [];
    for await (let i of this) {
      var r;
      if (t != null && (r = t.signal) !== null && r !== void 0 && r.aborted) throw new it(void 0, { cause: t.signal.reason });
      Jm(e, i);
    }
    return e;
  }
  function u0(t, e) {
    let r = En.call(this, t, e);
    return (async function* () {
      for await (let n of r) yield* n;
    }).call(this);
  }
  function Wh(t) {
    if (t = Zm(t), e0(t)) return 0;
    if (t < 0) throw new Wm("number", ">= 0", t);
    return t;
  }
  function c0(t, e = void 0) {
    return e != null && ar(e, "options"), (e == null ? void 0 : e.signal) != null && or(e.signal, "options.signal"), t = Wh(t), (async function* () {
      var i;
      if (e != null && (i = e.signal) !== null && i !== void 0 && i.aborted) throw new it();
      for await (let o of this) {
        var n;
        if (e != null && (n = e.signal) !== null && n !== void 0 && n.aborted) throw new it();
        t-- <= 0 && (yield o);
      }
    }).call(this);
  }
  function f0(t, e = void 0) {
    return e != null && ar(e, "options"), (e == null ? void 0 : e.signal) != null && or(e.signal, "options.signal"), t = Wh(t), (async function* () {
      var i;
      if (e != null && (i = e.signal) !== null && i !== void 0 && i.aborted) throw new it();
      for await (let o of this) {
        var n;
        if (e != null && (n = e.signal) !== null && n !== void 0 && n.aborted) throw new it();
        if (t-- > 0 && (yield o), t <= 0) return;
      }
    }).call(this);
  }
  To.exports.streamReturningOperators = { asIndexedPairs: Ym(i0, "readable.asIndexedPairs will be removed in a future version."), drop: c0, filter: Io, flatMap: u0, map: En, take: f0, compose: r0 };
  To.exports.promiseReturningOperators = { every: n0, forEach: o0, reduce: a0, toArray: l0, some: Fh, find: s0 };
});
var Ro = O((zk, Hh) => {
  "use strict";
  _();
  v();
  m();
  var { ArrayPrototypePop: h0, Promise: d0 } = ie(), { isIterable: p0, isNodeStream: g0, isWebStream: y0 } = Ze(), { pipelineImpl: b0 } = wn(), { finished: w0 } = _t();
  Co();
  function _0(...t) {
    return new d0((e, r) => {
      let i, n, o = t[t.length - 1];
      if (o && typeof o == "object" && !g0(o) && !p0(o) && !y0(o)) {
        let s = h0(t);
        i = s.signal, n = s.end;
      }
      b0(t, (s, a) => {
        s ? r(s) : e(a);
      }, { signal: i, end: n });
    });
  }
  Hh.exports = { finished: w0, pipeline: _0 };
});
var Co = O((rP, Zh) => {
  "use strict";
  _();
  v();
  m();
  var { Buffer: m0 } = (he(), G(be)), { ObjectDefineProperty: Et, ObjectKeys: Kh, ReflectApply: Qh } = ie(), { promisify: { custom: Gh } } = Ie(), { streamReturningOperators: Vh, promiseReturningOperators: zh } = $h(), { codes: { ERR_ILLEGAL_CONSTRUCTOR: Yh } } = me(), v0 = So(), { setDefaultHighWaterMark: E0, getDefaultHighWaterMark: S0 } = ai(), { pipeline: Jh } = wn(), { destroyer: A0 } = er(), Xh = _t(), ko = Ro(), bi = Ze(), ee = Zh.exports = en().Stream;
  ee.isDestroyed = bi.isDestroyed;
  ee.isDisturbed = bi.isDisturbed;
  ee.isErrored = bi.isErrored;
  ee.isReadable = bi.isReadable;
  ee.isWritable = bi.isWritable;
  ee.Readable = ci();
  for (let t of Kh(Vh)) {
    let r = function(...i) {
      if (new.target) throw Yh();
      return ee.Readable.from(Qh(e, this, i));
    }, e = Vh[t];
    Et(r, "name", { __proto__: null, value: e.name }), Et(r, "length", { __proto__: null, value: e.length }), Et(ee.Readable.prototype, t, { __proto__: null, value: r, enumerable: false, configurable: true, writable: true });
  }
  for (let t of Kh(zh)) {
    let r = function(...i) {
      if (new.target) throw Yh();
      return Qh(e, this, i);
    }, e = zh[t];
    Et(r, "name", { __proto__: null, value: e.name }), Et(r, "length", { __proto__: null, value: e.length }), Et(ee.Readable.prototype, t, { __proto__: null, value: r, enumerable: false, configurable: true, writable: true });
  }
  ee.Writable = pn();
  ee.Duplex = rt();
  ee.Transform = uo();
  ee.PassThrough = fo();
  ee.pipeline = Jh;
  var { addAbortSignal: I0 } = oi();
  ee.addAbortSignal = I0;
  ee.finished = Xh;
  ee.destroy = A0;
  ee.compose = v0;
  ee.setDefaultHighWaterMark = E0;
  ee.getDefaultHighWaterMark = S0;
  Et(ee, "promises", { __proto__: null, configurable: true, enumerable: true, get() {
    return ko;
  } });
  Et(Jh, Gh, { __proto__: null, enumerable: true, get() {
    return ko.pipeline;
  } });
  Et(Xh, Gh, { __proto__: null, enumerable: true, get() {
    return ko.finished;
  } });
  ee.Stream = ee;
  ee._isUint8Array = function(e) {
    return e instanceof Uint8Array;
  };
  ee._uint8ArrayToBuffer = function(e) {
    return m0.from(e.buffer, e.byteOffset, e.byteLength);
  };
});
var Nt = O((hP, ce) => {
  "use strict";
  _();
  v();
  m();
  var pe = Co(), T0 = Ro(), R0 = pe.Readable.destroy;
  ce.exports = pe.Readable;
  ce.exports._uint8ArrayToBuffer = pe._uint8ArrayToBuffer;
  ce.exports._isUint8Array = pe._isUint8Array;
  ce.exports.isDisturbed = pe.isDisturbed;
  ce.exports.isErrored = pe.isErrored;
  ce.exports.isReadable = pe.isReadable;
  ce.exports.Readable = pe.Readable;
  ce.exports.Writable = pe.Writable;
  ce.exports.Duplex = pe.Duplex;
  ce.exports.Transform = pe.Transform;
  ce.exports.PassThrough = pe.PassThrough;
  ce.exports.addAbortSignal = pe.addAbortSignal;
  ce.exports.finished = pe.finished;
  ce.exports.destroy = pe.destroy;
  ce.exports.destroy = R0;
  ce.exports.pipeline = pe.pipeline;
  ce.exports.compose = pe.compose;
  Object.defineProperty(pe, "promises", { configurable: true, enumerable: true, get() {
    return T0;
  } });
  ce.exports.Stream = pe.Stream;
  ce.exports.default = ce.exports;
});
var ed = O((EP, Po) => {
  _();
  v();
  m();
  typeof Object.create == "function" ? Po.exports = function(e, r) {
    r && (e.super_ = r, e.prototype = Object.create(r.prototype, { constructor: { value: e, enumerable: false, writable: true, configurable: true } }));
  } : Po.exports = function(e, r) {
    if (r) {
      e.super_ = r;
      var i = function() {
      };
      i.prototype = r.prototype, e.prototype = new i(), e.prototype.constructor = e;
    }
  };
});
var id = O((xP, rd) => {
  "use strict";
  _();
  v();
  m();
  var { Buffer: He } = (he(), G(be)), td = Symbol.for("BufferList");
  function Z(t) {
    if (!(this instanceof Z)) return new Z(t);
    Z._init.call(this, t);
  }
  Z._init = function(e) {
    Object.defineProperty(this, td, { value: true }), this._bufs = [], this.length = 0, e && this.append(e);
  };
  Z.prototype._new = function(e) {
    return new Z(e);
  };
  Z.prototype._offset = function(e) {
    if (e === 0) return [0, 0];
    let r = 0;
    for (let i = 0; i < this._bufs.length; i++) {
      let n = r + this._bufs[i].length;
      if (e < n || i === this._bufs.length - 1) return [i, e - r];
      r = n;
    }
  };
  Z.prototype._reverseOffset = function(t) {
    let e = t[0], r = t[1];
    for (let i = 0; i < e; i++) r += this._bufs[i].length;
    return r;
  };
  Z.prototype.get = function(e) {
    if (e > this.length || e < 0) return;
    let r = this._offset(e);
    return this._bufs[r[0]][r[1]];
  };
  Z.prototype.slice = function(e, r) {
    return typeof e == "number" && e < 0 && (e += this.length), typeof r == "number" && r < 0 && (r += this.length), this.copy(null, 0, e, r);
  };
  Z.prototype.copy = function(e, r, i, n) {
    if ((typeof i != "number" || i < 0) && (i = 0), (typeof n != "number" || n > this.length) && (n = this.length), i >= this.length || n <= 0) return e || He.alloc(0);
    let o = !!e, s = this._offset(i), a = n - i, u = a, f = o && r || 0, d = s[1];
    if (i === 0 && n === this.length) {
      if (!o) return this._bufs.length === 1 ? this._bufs[0] : He.concat(this._bufs, this.length);
      for (let h = 0; h < this._bufs.length; h++) this._bufs[h].copy(e, f), f += this._bufs[h].length;
      return e;
    }
    if (u <= this._bufs[s[0]].length - d) return o ? this._bufs[s[0]].copy(e, r, d, d + u) : this._bufs[s[0]].slice(d, d + u);
    o || (e = He.allocUnsafe(a));
    for (let h = s[0]; h < this._bufs.length; h++) {
      let g = this._bufs[h].length - d;
      if (u > g) this._bufs[h].copy(e, f, d), f += g;
      else {
        this._bufs[h].copy(e, f, d, d + u), f += g;
        break;
      }
      u -= g, d && (d = 0);
    }
    return e.length > f ? e.slice(0, f) : e;
  };
  Z.prototype.shallowSlice = function(e, r) {
    if (e = e || 0, r = typeof r != "number" ? this.length : r, e < 0 && (e += this.length), r < 0 && (r += this.length), e === r) return this._new();
    let i = this._offset(e), n = this._offset(r), o = this._bufs.slice(i[0], n[0] + 1);
    return n[1] === 0 ? o.pop() : o[o.length - 1] = o[o.length - 1].slice(0, n[1]), i[1] !== 0 && (o[0] = o[0].slice(i[1])), this._new(o);
  };
  Z.prototype.toString = function(e, r, i) {
    return this.slice(r, i).toString(e);
  };
  Z.prototype.consume = function(e) {
    if (e = Math.trunc(e), Number.isNaN(e) || e <= 0) return this;
    for (; this._bufs.length; ) if (e >= this._bufs[0].length) e -= this._bufs[0].length, this.length -= this._bufs[0].length, this._bufs.shift();
    else {
      this._bufs[0] = this._bufs[0].slice(e), this.length -= e;
      break;
    }
    return this;
  };
  Z.prototype.duplicate = function() {
    let e = this._new();
    for (let r = 0; r < this._bufs.length; r++) e.append(this._bufs[r]);
    return e;
  };
  Z.prototype.append = function(e) {
    if (e == null) return this;
    if (e.buffer) this._appendBuffer(He.from(e.buffer, e.byteOffset, e.byteLength));
    else if (Array.isArray(e)) for (let r = 0; r < e.length; r++) this.append(e[r]);
    else if (this._isBufferList(e)) for (let r = 0; r < e._bufs.length; r++) this.append(e._bufs[r]);
    else typeof e == "number" && (e = e.toString()), this._appendBuffer(He.from(e));
    return this;
  };
  Z.prototype._appendBuffer = function(e) {
    this._bufs.push(e), this.length += e.length;
  };
  Z.prototype.indexOf = function(t, e, r) {
    if (r === void 0 && typeof e == "string" && (r = e, e = void 0), typeof t == "function" || Array.isArray(t)) throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');
    if (typeof t == "number" ? t = He.from([t]) : typeof t == "string" ? t = He.from(t, r) : this._isBufferList(t) ? t = t.slice() : Array.isArray(t.buffer) ? t = He.from(t.buffer, t.byteOffset, t.byteLength) : He.isBuffer(t) || (t = He.from(t)), e = Number(e || 0), isNaN(e) && (e = 0), e < 0 && (e = this.length + e), e < 0 && (e = 0), t.length === 0) return e > this.length ? this.length : e;
    let i = this._offset(e), n = i[0], o = i[1];
    for (; n < this._bufs.length; n++) {
      let s = this._bufs[n];
      for (; o < s.length; ) if (s.length - o >= t.length) {
        let u = s.indexOf(t, o);
        if (u !== -1) return this._reverseOffset([n, u]);
        o = s.length - t.length + 1;
      } else {
        let u = this._reverseOffset([n, o]);
        if (this._match(u, t)) return u;
        o++;
      }
      o = 0;
    }
    return -1;
  };
  Z.prototype._match = function(t, e) {
    if (this.length - t < e.length) return false;
    for (let r = 0; r < e.length; r++) if (this.get(t + r) !== e[r]) return false;
    return true;
  };
  (function() {
    let t = { readDoubleBE: 8, readDoubleLE: 8, readFloatBE: 4, readFloatLE: 4, readBigInt64BE: 8, readBigInt64LE: 8, readBigUInt64BE: 8, readBigUInt64LE: 8, readInt32BE: 4, readInt32LE: 4, readUInt32BE: 4, readUInt32LE: 4, readInt16BE: 2, readInt16LE: 2, readUInt16BE: 2, readUInt16LE: 2, readInt8: 1, readUInt8: 1, readIntBE: null, readIntLE: null, readUIntBE: null, readUIntLE: null };
    for (let e in t) (function(r) {
      t[r] === null ? Z.prototype[r] = function(i, n) {
        return this.slice(i, i + n)[r](0, n);
      } : Z.prototype[r] = function(i = 0) {
        return this.slice(i, i + t[r])[r](0);
      };
    })(e);
  })();
  Z.prototype._isBufferList = function(e) {
    return e instanceof Z || Z.isBufferList(e);
  };
  Z.isBufferList = function(e) {
    return e != null && e[td];
  };
  rd.exports = Z;
});
var nd = O((WP, Sn) => {
  "use strict";
  _();
  v();
  m();
  var Bo = Nt().Duplex, C0 = ed(), wi = id();
  function Ee(t) {
    if (!(this instanceof Ee)) return new Ee(t);
    if (typeof t == "function") {
      this._callback = t;
      let e = (function(i) {
        this._callback && (this._callback(i), this._callback = null);
      }).bind(this);
      this.on("pipe", function(i) {
        i.on("error", e);
      }), this.on("unpipe", function(i) {
        i.removeListener("error", e);
      }), t = null;
    }
    wi._init.call(this, t), Bo.call(this);
  }
  C0(Ee, Bo);
  Object.assign(Ee.prototype, wi.prototype);
  Ee.prototype._new = function(e) {
    return new Ee(e);
  };
  Ee.prototype._write = function(e, r, i) {
    this._appendBuffer(e), typeof i == "function" && i();
  };
  Ee.prototype._read = function(e) {
    if (!this.length) return this.push(null);
    e = Math.min(e, this.length), this.push(this.slice(0, e)), this.consume(e);
  };
  Ee.prototype.end = function(e) {
    Bo.prototype.end.call(this, e), this._callback && (this._callback(null, this.slice()), this._callback = null);
  };
  Ee.prototype._destroy = function(e, r) {
    this._bufs.length = 0, this.length = 0, r(e);
  };
  Ee.prototype._isBufferList = function(e) {
    return e instanceof Ee || e instanceof wi || Ee.isBufferList(e);
  };
  Ee.isBufferList = wi.isBufferList;
  Sn.exports = Ee;
  Sn.exports.BufferListStream = Ee;
  Sn.exports.BufferList = wi;
});
var od = O((XP, sd) => {
  _();
  v();
  m();
  var xo = class {
    constructor() {
      this.cmd = null, this.retain = false, this.qos = 0, this.dup = false, this.length = -1, this.topic = null, this.payload = null;
    }
  };
  sd.exports = xo;
});
var Oo = O((lB, ad) => {
  _();
  v();
  m();
  var L = ad.exports, { Buffer: Pe } = (he(), G(be));
  L.types = { 0: "reserved", 1: "connect", 2: "connack", 3: "publish", 4: "puback", 5: "pubrec", 6: "pubrel", 7: "pubcomp", 8: "subscribe", 9: "suback", 10: "unsubscribe", 11: "unsuback", 12: "pingreq", 13: "pingresp", 14: "disconnect", 15: "auth" };
  L.requiredHeaderFlags = { 1: 0, 2: 0, 4: 0, 5: 0, 6: 2, 7: 0, 8: 2, 9: 0, 10: 2, 11: 0, 12: 0, 13: 0, 14: 0, 15: 0 };
  L.requiredHeaderFlagsErrors = {};
  for (let t in L.requiredHeaderFlags) {
    let e = L.requiredHeaderFlags[t];
    L.requiredHeaderFlagsErrors[t] = "Invalid header flag bits, must be 0x" + e.toString(16) + " for " + L.types[t] + " packet";
  }
  L.codes = {};
  for (let t in L.types) {
    let e = L.types[t];
    L.codes[e] = t;
  }
  L.CMD_SHIFT = 4;
  L.CMD_MASK = 240;
  L.DUP_MASK = 8;
  L.QOS_MASK = 3;
  L.QOS_SHIFT = 1;
  L.RETAIN_MASK = 1;
  L.VARBYTEINT_MASK = 127;
  L.VARBYTEINT_FIN_MASK = 128;
  L.VARBYTEINT_MAX = 268435455;
  L.SESSIONPRESENT_MASK = 1;
  L.SESSIONPRESENT_HEADER = Pe.from([L.SESSIONPRESENT_MASK]);
  L.CONNACK_HEADER = Pe.from([L.codes.connack << L.CMD_SHIFT]);
  L.USERNAME_MASK = 128;
  L.PASSWORD_MASK = 64;
  L.WILL_RETAIN_MASK = 32;
  L.WILL_QOS_MASK = 24;
  L.WILL_QOS_SHIFT = 3;
  L.WILL_FLAG_MASK = 4;
  L.CLEAN_SESSION_MASK = 2;
  L.CONNECT_HEADER = Pe.from([L.codes.connect << L.CMD_SHIFT]);
  L.properties = { sessionExpiryInterval: 17, willDelayInterval: 24, receiveMaximum: 33, maximumPacketSize: 39, topicAliasMaximum: 34, requestResponseInformation: 25, requestProblemInformation: 23, userProperties: 38, authenticationMethod: 21, authenticationData: 22, payloadFormatIndicator: 1, messageExpiryInterval: 2, contentType: 3, responseTopic: 8, correlationData: 9, maximumQoS: 36, retainAvailable: 37, assignedClientIdentifier: 18, reasonString: 31, wildcardSubscriptionAvailable: 40, subscriptionIdentifiersAvailable: 41, sharedSubscriptionAvailable: 42, serverKeepAlive: 19, responseInformation: 26, serverReference: 28, topicAlias: 35, subscriptionIdentifier: 11 };
  L.propertiesCodes = {};
  for (let t in L.properties) {
    let e = L.properties[t];
    L.propertiesCodes[e] = t;
  }
  L.propertiesTypes = { sessionExpiryInterval: "int32", willDelayInterval: "int32", receiveMaximum: "int16", maximumPacketSize: "int32", topicAliasMaximum: "int16", requestResponseInformation: "byte", requestProblemInformation: "byte", userProperties: "pair", authenticationMethod: "string", authenticationData: "binary", payloadFormatIndicator: "byte", messageExpiryInterval: "int32", contentType: "string", responseTopic: "string", correlationData: "binary", maximumQoS: "int8", retainAvailable: "byte", assignedClientIdentifier: "string", reasonString: "string", wildcardSubscriptionAvailable: "byte", subscriptionIdentifiersAvailable: "byte", sharedSubscriptionAvailable: "byte", serverKeepAlive: "int16", responseInformation: "string", serverReference: "string", topicAlias: "int16", subscriptionIdentifier: "var" };
  function Dt(t) {
    return [0, 1, 2].map((e) => [0, 1].map((r) => [0, 1].map((i) => {
      let n = Pe.alloc(1);
      return n.writeUInt8(L.codes[t] << L.CMD_SHIFT | (r ? L.DUP_MASK : 0) | e << L.QOS_SHIFT | i, 0, true), n;
    })));
  }
  L.PUBLISH_HEADER = Dt("publish");
  L.SUBSCRIBE_HEADER = Dt("subscribe");
  L.SUBSCRIBE_OPTIONS_QOS_MASK = 3;
  L.SUBSCRIBE_OPTIONS_NL_MASK = 1;
  L.SUBSCRIBE_OPTIONS_NL_SHIFT = 2;
  L.SUBSCRIBE_OPTIONS_RAP_MASK = 1;
  L.SUBSCRIBE_OPTIONS_RAP_SHIFT = 3;
  L.SUBSCRIBE_OPTIONS_RH_MASK = 3;
  L.SUBSCRIBE_OPTIONS_RH_SHIFT = 4;
  L.SUBSCRIBE_OPTIONS_RH = [0, 16, 32];
  L.SUBSCRIBE_OPTIONS_NL = 4;
  L.SUBSCRIBE_OPTIONS_RAP = 8;
  L.SUBSCRIBE_OPTIONS_QOS = [0, 1, 2];
  L.UNSUBSCRIBE_HEADER = Dt("unsubscribe");
  L.ACKS = { unsuback: Dt("unsuback"), puback: Dt("puback"), pubcomp: Dt("pubcomp"), pubrel: Dt("pubrel"), pubrec: Dt("pubrec") };
  L.SUBACK_HEADER = Pe.from([L.codes.suback << L.CMD_SHIFT]);
  L.VERSION3 = Pe.from([3]);
  L.VERSION4 = Pe.from([4]);
  L.VERSION5 = Pe.from([5]);
  L.VERSION131 = Pe.from([131]);
  L.VERSION132 = Pe.from([132]);
  L.QOS = [0, 1, 2].map((t) => Pe.from([t]));
  L.EMPTY = { pingreq: Pe.from([L.codes.pingreq << 4, 0]), pingresp: Pe.from([L.codes.pingresp << 4, 0]), disconnect: Pe.from([L.codes.disconnect << 4, 0]) };
  L.MQTT5_PUBACK_PUBREC_CODES = { 0: "Success", 16: "No matching subscribers", 128: "Unspecified error", 131: "Implementation specific error", 135: "Not authorized", 144: "Topic Name invalid", 145: "Packet identifier in use", 151: "Quota exceeded", 153: "Payload format invalid" };
  L.MQTT5_PUBREL_PUBCOMP_CODES = { 0: "Success", 146: "Packet Identifier not found" };
  L.MQTT5_SUBACK_CODES = { 0: "Granted QoS 0", 1: "Granted QoS 1", 2: "Granted QoS 2", 128: "Unspecified error", 131: "Implementation specific error", 135: "Not authorized", 143: "Topic Filter invalid", 145: "Packet Identifier in use", 151: "Quota exceeded", 158: "Shared Subscriptions not supported", 161: "Subscription Identifiers not supported", 162: "Wildcard Subscriptions not supported" };
  L.MQTT5_UNSUBACK_CODES = { 0: "Success", 17: "No subscription existed", 128: "Unspecified error", 131: "Implementation specific error", 135: "Not authorized", 143: "Topic Filter invalid", 145: "Packet Identifier in use" };
  L.MQTT5_DISCONNECT_CODES = { 0: "Normal disconnection", 4: "Disconnect with Will Message", 128: "Unspecified error", 129: "Malformed Packet", 130: "Protocol Error", 131: "Implementation specific error", 135: "Not authorized", 137: "Server busy", 139: "Server shutting down", 141: "Keep Alive timeout", 142: "Session taken over", 143: "Topic Filter invalid", 144: "Topic Name invalid", 147: "Receive Maximum exceeded", 148: "Topic Alias invalid", 149: "Packet too large", 150: "Message rate too high", 151: "Quota exceeded", 152: "Administrative action", 153: "Payload format invalid", 154: "Retain not supported", 155: "QoS not supported", 156: "Use another server", 157: "Server moved", 158: "Shared Subscriptions not supported", 159: "Connection rate exceeded", 160: "Maximum connect time", 161: "Subscription Identifiers not supported", 162: "Wildcard Subscriptions not supported" };
  L.MQTT5_AUTH_CODES = { 0: "Success", 24: "Continue authentication", 25: "Re-authenticate" };
});
var ud = O((wB, ld) => {
  _();
  v();
  m();
  var Fr = 1e3, Wr = Fr * 60, $r = Wr * 60, lr = $r * 24, k0 = lr * 7, P0 = lr * 365.25;
  ld.exports = function(t, e) {
    e = e || {};
    var r = typeof t;
    if (r === "string" && t.length > 0) return B0(t);
    if (r === "number" && isFinite(t)) return e.long ? O0(t) : x0(t);
    throw new Error("val is not a non-empty string or a valid number. val=" + JSON.stringify(t));
  };
  function B0(t) {
    if (t = String(t), !(t.length > 100)) {
      var e = /^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);
      if (e) {
        var r = parseFloat(e[1]), i = (e[2] || "ms").toLowerCase();
        switch (i) {
          case "years":
          case "year":
          case "yrs":
          case "yr":
          case "y":
            return r * P0;
          case "weeks":
          case "week":
          case "w":
            return r * k0;
          case "days":
          case "day":
          case "d":
            return r * lr;
          case "hours":
          case "hour":
          case "hrs":
          case "hr":
          case "h":
            return r * $r;
          case "minutes":
          case "minute":
          case "mins":
          case "min":
          case "m":
            return r * Wr;
          case "seconds":
          case "second":
          case "secs":
          case "sec":
          case "s":
            return r * Fr;
          case "milliseconds":
          case "millisecond":
          case "msecs":
          case "msec":
          case "ms":
            return r;
          default:
            return;
        }
      }
    }
  }
  function x0(t) {
    var e = Math.abs(t);
    return e >= lr ? Math.round(t / lr) + "d" : e >= $r ? Math.round(t / $r) + "h" : e >= Wr ? Math.round(t / Wr) + "m" : e >= Fr ? Math.round(t / Fr) + "s" : t + "ms";
  }
  function O0(t) {
    var e = Math.abs(t);
    return e >= lr ? An(t, e, lr, "day") : e >= $r ? An(t, e, $r, "hour") : e >= Wr ? An(t, e, Wr, "minute") : e >= Fr ? An(t, e, Fr, "second") : t + " ms";
  }
  function An(t, e, r, i) {
    var n = e >= r * 1.5;
    return Math.round(t / r) + " " + i + (n ? "s" : "");
  }
});
var fd = O((CB, cd) => {
  _();
  v();
  m();
  function M0(t) {
    r.debug = r, r.default = r, r.coerce = u, r.disable = s, r.enable = n, r.enabled = a, r.humanize = ud(), r.destroy = f, Object.keys(t).forEach((d) => {
      r[d] = t[d];
    }), r.names = [], r.skips = [], r.formatters = {};
    function e(d) {
      let h = 0;
      for (let g = 0; g < d.length; g++) h = (h << 5) - h + d.charCodeAt(g), h |= 0;
      return r.colors[Math.abs(h) % r.colors.length];
    }
    r.selectColor = e;
    function r(d) {
      let h, g = null, y, E;
      function w(...S) {
        if (!w.enabled) return;
        let I = w, C = Number(/* @__PURE__ */ new Date()), k = C - (h || C);
        I.diff = k, I.prev = h, I.curr = C, h = C, S[0] = r.coerce(S[0]), typeof S[0] != "string" && S.unshift("%O");
        let M = 0;
        S[0] = S[0].replace(/%([a-zA-Z%])/g, (z, j) => {
          if (z === "%%") return "%";
          M++;
          let Q = r.formatters[j];
          if (typeof Q == "function") {
            let $ = S[M];
            z = Q.call(I, $), S.splice(M, 1), M--;
          }
          return z;
        }), r.formatArgs.call(I, S), (I.log || r.log).apply(I, S);
      }
      return w.namespace = d, w.useColors = r.useColors(), w.color = r.selectColor(d), w.extend = i, w.destroy = r.destroy, Object.defineProperty(w, "enabled", { enumerable: true, configurable: false, get: () => g !== null ? g : (y !== r.namespaces && (y = r.namespaces, E = r.enabled(d)), E), set: (S) => {
        g = S;
      } }), typeof r.init == "function" && r.init(w), w;
    }
    function i(d, h) {
      let g = r(this.namespace + (typeof h > "u" ? ":" : h) + d);
      return g.log = this.log, g;
    }
    function n(d) {
      r.save(d), r.namespaces = d, r.names = [], r.skips = [];
      let h = (typeof d == "string" ? d : "").trim().replace(" ", ",").split(",").filter(Boolean);
      for (let g of h) g[0] === "-" ? r.skips.push(g.slice(1)) : r.names.push(g);
    }
    function o(d, h) {
      let g = 0, y = 0, E = -1, w = 0;
      for (; g < d.length; ) if (y < h.length && (h[y] === d[g] || h[y] === "*")) h[y] === "*" ? (E = y, w = g, y++) : (g++, y++);
      else if (E !== -1) y = E + 1, w++, g = w;
      else return false;
      for (; y < h.length && h[y] === "*"; ) y++;
      return y === h.length;
    }
    function s() {
      let d = [...r.names, ...r.skips.map((h) => "-" + h)].join(",");
      return r.enable(""), d;
    }
    function a(d) {
      for (let h of r.skips) if (o(d, h)) return false;
      for (let h of r.names) if (o(d, h)) return true;
      return false;
    }
    function u(d) {
      return d instanceof Error ? d.stack || d.message : d;
    }
    function f() {
      console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");
    }
    return r.enable(r.load()), r;
  }
  cd.exports = M0;
});
var nt = O((Be, In) => {
  _();
  v();
  m();
  Be.formatArgs = q0;
  Be.save = U0;
  Be.load = N0;
  Be.useColors = L0;
  Be.storage = D0();
  Be.destroy = /* @__PURE__ */ (() => {
    let t = false;
    return () => {
      t || (t = true, console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."));
    };
  })();
  Be.colors = ["#0000CC", "#0000FF", "#0033CC", "#0033FF", "#0066CC", "#0066FF", "#0099CC", "#0099FF", "#00CC00", "#00CC33", "#00CC66", "#00CC99", "#00CCCC", "#00CCFF", "#3300CC", "#3300FF", "#3333CC", "#3333FF", "#3366CC", "#3366FF", "#3399CC", "#3399FF", "#33CC00", "#33CC33", "#33CC66", "#33CC99", "#33CCCC", "#33CCFF", "#6600CC", "#6600FF", "#6633CC", "#6633FF", "#66CC00", "#66CC33", "#9900CC", "#9900FF", "#9933CC", "#9933FF", "#99CC00", "#99CC33", "#CC0000", "#CC0033", "#CC0066", "#CC0099", "#CC00CC", "#CC00FF", "#CC3300", "#CC3333", "#CC3366", "#CC3399", "#CC33CC", "#CC33FF", "#CC6600", "#CC6633", "#CC9900", "#CC9933", "#CCCC00", "#CCCC33", "#FF0000", "#FF0033", "#FF0066", "#FF0099", "#FF00CC", "#FF00FF", "#FF3300", "#FF3333", "#FF3366", "#FF3399", "#FF33CC", "#FF33FF", "#FF6600", "#FF6633", "#FF9900", "#FF9933", "#FFCC00", "#FFCC33"];
  function L0() {
    if (typeof window < "u" && window.process && (window.process.type === "renderer" || window.process.__nwjs)) return true;
    if (typeof navigator < "u" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)) return false;
    let t;
    return typeof document < "u" && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || typeof window < "u" && window.console && (window.console.firebug || window.console.exception && window.console.table) || typeof navigator < "u" && navigator.userAgent && (t = navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)) && parseInt(t[1], 10) >= 31 || typeof navigator < "u" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/);
  }
  function q0(t) {
    if (t[0] = (this.useColors ? "%c" : "") + this.namespace + (this.useColors ? " %c" : " ") + t[0] + (this.useColors ? "%c " : " ") + "+" + In.exports.humanize(this.diff), !this.useColors) return;
    let e = "color: " + this.color;
    t.splice(1, 0, e, "color: inherit");
    let r = 0, i = 0;
    t[0].replace(/%[a-zA-Z%]/g, (n) => {
      n !== "%%" && (r++, n === "%c" && (i = r));
    }), t.splice(i, 0, e);
  }
  Be.log = console.debug || console.log || (() => {
  });
  function U0(t) {
    try {
      t ? Be.storage.setItem("debug", t) : Be.storage.removeItem("debug");
    } catch {
    }
  }
  function N0() {
    let t;
    try {
      t = Be.storage.getItem("debug");
    } catch {
    }
    return !t && typeof R < "u" && "env" in R && (t = R.env.DEBUG), t;
  }
  function D0() {
    try {
      return localStorage;
    } catch {
    }
  }
  In.exports = fd()(Be);
  var { formatters: j0 } = In.exports;
  j0.j = function(t) {
    try {
      return JSON.stringify(t);
    } catch (e) {
      return "[UnexpectedJSONParseError]: " + e.message;
    }
  };
});
var pd = O((KB, dd) => {
  _();
  v();
  m();
  var F0 = nd(), { EventEmitter: W0 } = (Ot(), G(xt)), hd = od(), V = Oo(), N = nt()("mqtt-packet:parser"), Mo = class t extends W0 {
    constructor() {
      super(), this.parser = this.constructor.parser;
    }
    static parser(e) {
      return this instanceof t ? (this.settings = e || {}, this._states = ["_parseHeader", "_parseLength", "_parsePayload", "_newPacket"], this._resetState(), this) : new t().parser(e);
    }
    _resetState() {
      N("_resetState: resetting packet, error, _list, and _stateCounter"), this.packet = new hd(), this.error = null, this._list = F0(), this._stateCounter = 0;
    }
    parse(e) {
      for (this.error && this._resetState(), this._list.append(e), N("parse: current state: %s", this._states[this._stateCounter]); (this.packet.length !== -1 || this._list.length > 0) && this[this._states[this._stateCounter]]() && !this.error; ) this._stateCounter++, N("parse: state complete. _stateCounter is now: %d", this._stateCounter), N("parse: packet.length: %d, buffer list length: %d", this.packet.length, this._list.length), this._stateCounter >= this._states.length && (this._stateCounter = 0);
      return N("parse: exited while loop. packet: %d, buffer list length: %d", this.packet.length, this._list.length), this._list.length;
    }
    _parseHeader() {
      let e = this._list.readUInt8(0), r = e >> V.CMD_SHIFT;
      this.packet.cmd = V.types[r];
      let i = e & 15, n = V.requiredHeaderFlags[r];
      return n != null && i !== n ? this._emitError(new Error(V.requiredHeaderFlagsErrors[r])) : (this.packet.retain = (e & V.RETAIN_MASK) !== 0, this.packet.qos = e >> V.QOS_SHIFT & V.QOS_MASK, this.packet.qos > 2 ? this._emitError(new Error("Packet must not have both QoS bits set to 1")) : (this.packet.dup = (e & V.DUP_MASK) !== 0, N("_parseHeader: packet: %o", this.packet), this._list.consume(1), true));
    }
    _parseLength() {
      let e = this._parseVarByteNum(true);
      return e && (this.packet.length = e.value, this._list.consume(e.bytes)), N("_parseLength %d", e.value), !!e;
    }
    _parsePayload() {
      N("_parsePayload: payload %O", this._list);
      let e = false;
      if (this.packet.length === 0 || this._list.length >= this.packet.length) {
        switch (this._pos = 0, this.packet.cmd) {
          case "connect":
            this._parseConnect();
            break;
          case "connack":
            this._parseConnack();
            break;
          case "publish":
            this._parsePublish();
            break;
          case "puback":
          case "pubrec":
          case "pubrel":
          case "pubcomp":
            this._parseConfirmation();
            break;
          case "subscribe":
            this._parseSubscribe();
            break;
          case "suback":
            this._parseSuback();
            break;
          case "unsubscribe":
            this._parseUnsubscribe();
            break;
          case "unsuback":
            this._parseUnsuback();
            break;
          case "pingreq":
          case "pingresp":
            break;
          case "disconnect":
            this._parseDisconnect();
            break;
          case "auth":
            this._parseAuth();
            break;
          default:
            this._emitError(new Error("Not supported"));
        }
        e = true;
      }
      return N("_parsePayload complete result: %s", e), e;
    }
    _parseConnect() {
      N("_parseConnect");
      let e, r, i, n, o = {}, s = this.packet, a = this._parseString();
      if (a === null) return this._emitError(new Error("Cannot parse protocolId"));
      if (a !== "MQTT" && a !== "MQIsdp") return this._emitError(new Error("Invalid protocolId"));
      if (s.protocolId = a, this._pos >= this._list.length) return this._emitError(new Error("Packet too short"));
      if (s.protocolVersion = this._list.readUInt8(this._pos), s.protocolVersion >= 128 && (s.bridgeMode = true, s.protocolVersion = s.protocolVersion - 128), s.protocolVersion !== 3 && s.protocolVersion !== 4 && s.protocolVersion !== 5) return this._emitError(new Error("Invalid protocol version"));
      if (this._pos++, this._pos >= this._list.length) return this._emitError(new Error("Packet too short"));
      if (this._list.readUInt8(this._pos) & 1) return this._emitError(new Error("Connect flag bit 0 must be 0, but got 1"));
      o.username = this._list.readUInt8(this._pos) & V.USERNAME_MASK, o.password = this._list.readUInt8(this._pos) & V.PASSWORD_MASK, o.will = this._list.readUInt8(this._pos) & V.WILL_FLAG_MASK;
      let u = !!(this._list.readUInt8(this._pos) & V.WILL_RETAIN_MASK), f = (this._list.readUInt8(this._pos) & V.WILL_QOS_MASK) >> V.WILL_QOS_SHIFT;
      if (o.will) s.will = {}, s.will.retain = u, s.will.qos = f;
      else {
        if (u) return this._emitError(new Error("Will Retain Flag must be set to zero when Will Flag is set to 0"));
        if (f) return this._emitError(new Error("Will QoS must be set to zero when Will Flag is set to 0"));
      }
      if (s.clean = (this._list.readUInt8(this._pos) & V.CLEAN_SESSION_MASK) !== 0, this._pos++, s.keepalive = this._parseNum(), s.keepalive === -1) return this._emitError(new Error("Packet too short"));
      if (s.protocolVersion === 5) {
        let h = this._parseProperties();
        Object.getOwnPropertyNames(h).length && (s.properties = h);
      }
      let d = this._parseString();
      if (d === null) return this._emitError(new Error("Packet too short"));
      if (s.clientId = d, N("_parseConnect: packet.clientId: %s", s.clientId), o.will) {
        if (s.protocolVersion === 5) {
          let h = this._parseProperties();
          Object.getOwnPropertyNames(h).length && (s.will.properties = h);
        }
        if (e = this._parseString(), e === null) return this._emitError(new Error("Cannot parse will topic"));
        if (s.will.topic = e, N("_parseConnect: packet.will.topic: %s", s.will.topic), r = this._parseBuffer(), r === null) return this._emitError(new Error("Cannot parse will payload"));
        s.will.payload = r, N("_parseConnect: packet.will.paylaod: %s", s.will.payload);
      }
      if (o.username) {
        if (n = this._parseString(), n === null) return this._emitError(new Error("Cannot parse username"));
        s.username = n, N("_parseConnect: packet.username: %s", s.username);
      }
      if (o.password) {
        if (i = this._parseBuffer(), i === null) return this._emitError(new Error("Cannot parse password"));
        s.password = i;
      }
      return this.settings = s, N("_parseConnect: complete"), s;
    }
    _parseConnack() {
      N("_parseConnack");
      let e = this.packet;
      if (this._list.length < 1) return null;
      let r = this._list.readUInt8(this._pos++);
      if (r > 1) return this._emitError(new Error("Invalid connack flags, bits 7-1 must be set to 0"));
      if (e.sessionPresent = !!(r & V.SESSIONPRESENT_MASK), this.settings.protocolVersion === 5) this._list.length >= 2 ? e.reasonCode = this._list.readUInt8(this._pos++) : e.reasonCode = 0;
      else {
        if (this._list.length < 2) return null;
        e.returnCode = this._list.readUInt8(this._pos++);
      }
      if (e.returnCode === -1 || e.reasonCode === -1) return this._emitError(new Error("Cannot parse return code"));
      if (this.settings.protocolVersion === 5) {
        let i = this._parseProperties();
        Object.getOwnPropertyNames(i).length && (e.properties = i);
      }
      N("_parseConnack: complete");
    }
    _parsePublish() {
      N("_parsePublish");
      let e = this.packet;
      if (e.topic = this._parseString(), e.topic === null) return this._emitError(new Error("Cannot parse topic"));
      if (!(e.qos > 0 && !this._parseMessageId())) {
        if (this.settings.protocolVersion === 5) {
          let r = this._parseProperties();
          Object.getOwnPropertyNames(r).length && (e.properties = r);
        }
        e.payload = this._list.slice(this._pos, e.length), N("_parsePublish: payload from buffer list: %o", e.payload);
      }
    }
    _parseSubscribe() {
      N("_parseSubscribe");
      let e = this.packet, r, i, n, o, s, a, u;
      if (e.subscriptions = [], !!this._parseMessageId()) {
        if (this.settings.protocolVersion === 5) {
          let f = this._parseProperties();
          Object.getOwnPropertyNames(f).length && (e.properties = f);
        }
        if (e.length <= 0) return this._emitError(new Error("Malformed subscribe, no payload specified"));
        for (; this._pos < e.length; ) {
          if (r = this._parseString(), r === null) return this._emitError(new Error("Cannot parse topic"));
          if (this._pos >= e.length) return this._emitError(new Error("Malformed Subscribe Payload"));
          if (i = this._parseByte(), this.settings.protocolVersion === 5) {
            if (i & 192) return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-6 must be 0"));
          } else if (i & 252) return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-2 must be 0"));
          if (n = i & V.SUBSCRIBE_OPTIONS_QOS_MASK, n > 2) return this._emitError(new Error("Invalid subscribe QoS, must be <= 2"));
          if (a = (i >> V.SUBSCRIBE_OPTIONS_NL_SHIFT & V.SUBSCRIBE_OPTIONS_NL_MASK) !== 0, s = (i >> V.SUBSCRIBE_OPTIONS_RAP_SHIFT & V.SUBSCRIBE_OPTIONS_RAP_MASK) !== 0, o = i >> V.SUBSCRIBE_OPTIONS_RH_SHIFT & V.SUBSCRIBE_OPTIONS_RH_MASK, o > 2) return this._emitError(new Error("Invalid retain handling, must be <= 2"));
          u = { topic: r, qos: n }, this.settings.protocolVersion === 5 ? (u.nl = a, u.rap = s, u.rh = o) : this.settings.bridgeMode && (u.rh = 0, u.rap = true, u.nl = true), N("_parseSubscribe: push subscription `%s` to subscription", u), e.subscriptions.push(u);
        }
      }
    }
    _parseSuback() {
      N("_parseSuback");
      let e = this.packet;
      if (this.packet.granted = [], !!this._parseMessageId()) {
        if (this.settings.protocolVersion === 5) {
          let r = this._parseProperties();
          Object.getOwnPropertyNames(r).length && (e.properties = r);
        }
        if (e.length <= 0) return this._emitError(new Error("Malformed suback, no payload specified"));
        for (; this._pos < this.packet.length; ) {
          let r = this._list.readUInt8(this._pos++);
          if (this.settings.protocolVersion === 5) {
            if (!V.MQTT5_SUBACK_CODES[r]) return this._emitError(new Error("Invalid suback code"));
          } else if (r > 2 && r !== 128) return this._emitError(new Error("Invalid suback QoS, must be 0, 1, 2 or 128"));
          this.packet.granted.push(r);
        }
      }
    }
    _parseUnsubscribe() {
      N("_parseUnsubscribe");
      let e = this.packet;
      if (e.unsubscriptions = [], !!this._parseMessageId()) {
        if (this.settings.protocolVersion === 5) {
          let r = this._parseProperties();
          Object.getOwnPropertyNames(r).length && (e.properties = r);
        }
        if (e.length <= 0) return this._emitError(new Error("Malformed unsubscribe, no payload specified"));
        for (; this._pos < e.length; ) {
          let r = this._parseString();
          if (r === null) return this._emitError(new Error("Cannot parse topic"));
          N("_parseUnsubscribe: push topic `%s` to unsubscriptions", r), e.unsubscriptions.push(r);
        }
      }
    }
    _parseUnsuback() {
      N("_parseUnsuback");
      let e = this.packet;
      if (!this._parseMessageId()) return this._emitError(new Error("Cannot parse messageId"));
      if ((this.settings.protocolVersion === 3 || this.settings.protocolVersion === 4) && e.length !== 2) return this._emitError(new Error("Malformed unsuback, payload length must be 2"));
      if (e.length <= 0) return this._emitError(new Error("Malformed unsuback, no payload specified"));
      if (this.settings.protocolVersion === 5) {
        let r = this._parseProperties();
        for (Object.getOwnPropertyNames(r).length && (e.properties = r), e.granted = []; this._pos < this.packet.length; ) {
          let i = this._list.readUInt8(this._pos++);
          if (!V.MQTT5_UNSUBACK_CODES[i]) return this._emitError(new Error("Invalid unsuback code"));
          this.packet.granted.push(i);
        }
      }
    }
    _parseConfirmation() {
      N("_parseConfirmation: packet.cmd: `%s`", this.packet.cmd);
      let e = this.packet;
      if (this._parseMessageId(), this.settings.protocolVersion === 5) {
        if (e.length > 2) {
          switch (e.reasonCode = this._parseByte(), this.packet.cmd) {
            case "puback":
            case "pubrec":
              if (!V.MQTT5_PUBACK_PUBREC_CODES[e.reasonCode]) return this._emitError(new Error("Invalid " + this.packet.cmd + " reason code"));
              break;
            case "pubrel":
            case "pubcomp":
              if (!V.MQTT5_PUBREL_PUBCOMP_CODES[e.reasonCode]) return this._emitError(new Error("Invalid " + this.packet.cmd + " reason code"));
              break;
          }
          N("_parseConfirmation: packet.reasonCode `%d`", e.reasonCode);
        } else e.reasonCode = 0;
        if (e.length > 3) {
          let r = this._parseProperties();
          Object.getOwnPropertyNames(r).length && (e.properties = r);
        }
      }
      return true;
    }
    _parseDisconnect() {
      let e = this.packet;
      if (N("_parseDisconnect"), this.settings.protocolVersion === 5) {
        this._list.length > 0 ? (e.reasonCode = this._parseByte(), V.MQTT5_DISCONNECT_CODES[e.reasonCode] || this._emitError(new Error("Invalid disconnect reason code"))) : e.reasonCode = 0;
        let r = this._parseProperties();
        Object.getOwnPropertyNames(r).length && (e.properties = r);
      }
      return N("_parseDisconnect result: true"), true;
    }
    _parseAuth() {
      N("_parseAuth");
      let e = this.packet;
      if (this.settings.protocolVersion !== 5) return this._emitError(new Error("Not supported auth packet for this version MQTT"));
      if (e.reasonCode = this._parseByte(), !V.MQTT5_AUTH_CODES[e.reasonCode]) return this._emitError(new Error("Invalid auth reason code"));
      let r = this._parseProperties();
      return Object.getOwnPropertyNames(r).length && (e.properties = r), N("_parseAuth: result: true"), true;
    }
    _parseMessageId() {
      let e = this.packet;
      return e.messageId = this._parseNum(), e.messageId === null ? (this._emitError(new Error("Cannot parse messageId")), false) : (N("_parseMessageId: packet.messageId %d", e.messageId), true);
    }
    _parseString(e) {
      let r = this._parseNum(), i = r + this._pos;
      if (r === -1 || i > this._list.length || i > this.packet.length) return null;
      let n = this._list.toString("utf8", this._pos, i);
      return this._pos += r, N("_parseString: result: %s", n), n;
    }
    _parseStringPair() {
      return N("_parseStringPair"), { name: this._parseString(), value: this._parseString() };
    }
    _parseBuffer() {
      let e = this._parseNum(), r = e + this._pos;
      if (e === -1 || r > this._list.length || r > this.packet.length) return null;
      let i = this._list.slice(this._pos, r);
      return this._pos += e, N("_parseBuffer: result: %o", i), i;
    }
    _parseNum() {
      if (this._list.length - this._pos < 2) return -1;
      let e = this._list.readUInt16BE(this._pos);
      return this._pos += 2, N("_parseNum: result: %s", e), e;
    }
    _parse4ByteNum() {
      if (this._list.length - this._pos < 4) return -1;
      let e = this._list.readUInt32BE(this._pos);
      return this._pos += 4, N("_parse4ByteNum: result: %s", e), e;
    }
    _parseVarByteNum(e) {
      N("_parseVarByteNum");
      let r = 4, i = 0, n = 1, o = 0, s = false, a, u = this._pos ? this._pos : 0;
      for (; i < r && u + i < this._list.length; ) {
        if (a = this._list.readUInt8(u + i++), o += n * (a & V.VARBYTEINT_MASK), n *= 128, (a & V.VARBYTEINT_FIN_MASK) === 0) {
          s = true;
          break;
        }
        if (this._list.length <= i) break;
      }
      return !s && i === r && this._list.length >= i && this._emitError(new Error("Invalid variable byte integer")), u && (this._pos += i), s ? e ? s = { bytes: i, value: o } : s = o : s = false, N("_parseVarByteNum: result: %o", s), s;
    }
    _parseByte() {
      let e;
      return this._pos < this._list.length && (e = this._list.readUInt8(this._pos), this._pos++), N("_parseByte: result: %o", e), e;
    }
    _parseByType(e) {
      switch (N("_parseByType: type: %s", e), e) {
        case "byte":
          return this._parseByte() !== 0;
        case "int8":
          return this._parseByte();
        case "int16":
          return this._parseNum();
        case "int32":
          return this._parse4ByteNum();
        case "var":
          return this._parseVarByteNum();
        case "string":
          return this._parseString();
        case "pair":
          return this._parseStringPair();
        case "binary":
          return this._parseBuffer();
      }
    }
    _parseProperties() {
      N("_parseProperties");
      let e = this._parseVarByteNum(), i = this._pos + e, n = {};
      for (; this._pos < i; ) {
        let o = this._parseByte();
        if (!o) return this._emitError(new Error("Cannot parse property code type")), false;
        let s = V.propertiesCodes[o];
        if (!s) return this._emitError(new Error("Unknown property")), false;
        if (s === "userProperties") {
          n[s] || (n[s] = /* @__PURE__ */ Object.create(null));
          let a = this._parseByType(V.propertiesTypes[s]);
          if (n[s][a.name]) if (Array.isArray(n[s][a.name])) n[s][a.name].push(a.value);
          else {
            let u = n[s][a.name];
            n[s][a.name] = [u], n[s][a.name].push(a.value);
          }
          else n[s][a.name] = a.value;
          continue;
        }
        n[s] ? Array.isArray(n[s]) ? n[s].push(this._parseByType(V.propertiesTypes[s])) : (n[s] = [n[s]], n[s].push(this._parseByType(V.propertiesTypes[s]))) : n[s] = this._parseByType(V.propertiesTypes[s]);
      }
      return n;
    }
    _newPacket() {
      return N("_newPacket"), this.packet && (this._list.consume(this.packet.length), N("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d", this.packet.cmd, this.packet.payload, this.packet.length), this.emit("packet", this.packet)), N("_newPacket: new packet"), this.packet = new hd(), this._pos = 0, true;
    }
    _emitError(e) {
      N("_emitError", e), this.error = e, this.emit("error", e);
    }
  };
  dd.exports = Mo;
});
var wd = O((ix, bd) => {
  _();
  v();
  m();
  var { Buffer: _i } = (he(), G(be)), $0 = 65536, gd = {}, H0 = _i.isBuffer(_i.from([1, 2]).subarray(0, 1));
  function yd(t) {
    let e = _i.allocUnsafe(2);
    return e.writeUInt8(t >> 8, 0), e.writeUInt8(t & 255, 1), e;
  }
  function V0() {
    for (let t = 0; t < $0; t++) gd[t] = yd(t);
  }
  function z0(t) {
    let r = 0, i = 0, n = _i.allocUnsafe(4);
    do
      r = t % 128 | 0, t = t / 128 | 0, t > 0 && (r = r | 128), n.writeUInt8(r, i++);
    while (t > 0 && i < 4);
    return t > 0 && (i = 0), H0 ? n.subarray(0, i) : n.slice(0, i);
  }
  function K0(t) {
    let e = _i.allocUnsafe(4);
    return e.writeUInt32BE(t, 0), e;
  }
  bd.exports = { cache: gd, generateCache: V0, generateNumber: yd, genBufVariableByteInt: z0, generate4ByteBuffer: K0 };
});
var _d = O((dx, Lo) => {
  "use strict";
  _();
  v();
  m();
  typeof R > "u" || !R.version || R.version.indexOf("v0.") === 0 || R.version.indexOf("v1.") === 0 && R.version.indexOf("v1.8.") !== 0 ? Lo.exports = { nextTick: Q0 } : Lo.exports = R;
  function Q0(t, e, r, i) {
    if (typeof t != "function") throw new TypeError('"callback" argument must be a function');
    var n = arguments.length, o, s;
    switch (n) {
      case 0:
      case 1:
        return R.nextTick(t);
      case 2:
        return R.nextTick(function() {
          t.call(null, e);
        });
      case 3:
        return R.nextTick(function() {
          t.call(null, e, r);
        });
      case 4:
        return R.nextTick(function() {
          t.call(null, e, r, i);
        });
      default:
        for (o = new Array(n - 1), s = 0; s < o.length; ) o[s++] = arguments[s];
        return R.nextTick(function() {
          t.apply(null, o);
        });
    }
  }
});
var No = O((Ax, Rd) => {
  _();
  v();
  m();
  var D = Oo(), { Buffer: U } = (he(), G(be)), G0 = U.allocUnsafe(0), Y0 = U.from([0]), mi = wd(), J0 = _d().nextTick, qe = nt()("mqtt-packet:writeToStream"), Tn = mi.cache, X0 = mi.generateNumber, Z0 = mi.generateCache, qo = mi.genBufVariableByteInt, ev = mi.generate4ByteBuffer, Te = Uo, Rn = true;
  function Id(t, e, r) {
    switch (qe("generate called"), e.cork && (e.cork(), J0(tv, e)), Rn && (Rn = false, Z0()), qe("generate: packet.cmd: %s", t.cmd), t.cmd) {
      case "connect":
        return rv(t, e, r);
      case "connack":
        return iv(t, e, r);
      case "publish":
        return nv(t, e, r);
      case "puback":
      case "pubrec":
      case "pubrel":
      case "pubcomp":
        return sv(t, e, r);
      case "subscribe":
        return ov(t, e, r);
      case "suback":
        return av(t, e, r);
      case "unsubscribe":
        return lv(t, e, r);
      case "unsuback":
        return uv(t, e, r);
      case "pingreq":
      case "pingresp":
        return cv(t, e, r);
      case "disconnect":
        return fv(t, e, r);
      case "auth":
        return hv(t, e, r);
      default:
        return e.destroy(new Error("Unknown command")), false;
    }
  }
  Object.defineProperty(Id, "cacheNumbers", { get() {
    return Te === Uo;
  }, set(t) {
    t ? ((!Tn || Object.keys(Tn).length === 0) && (Rn = true), Te = Uo) : (Rn = false, Te = dv);
  } });
  function tv(t) {
    t.uncork();
  }
  function rv(t, e, r) {
    let i = t || {}, n = i.protocolId || "MQTT", o = i.protocolVersion || 4, s = i.will, a = i.clean, u = i.keepalive || 0, f = i.clientId || "", d = i.username, h = i.password, g = i.properties;
    a === void 0 && (a = true);
    let y = 0;
    if (!n || typeof n != "string" && !U.isBuffer(n)) return e.destroy(new Error("Invalid protocolId")), false;
    if (y += n.length + 2, o !== 3 && o !== 4 && o !== 5) return e.destroy(new Error("Invalid protocol version")), false;
    if (y += 1, (typeof f == "string" || U.isBuffer(f)) && (f || o >= 4) && (f || a)) y += U.byteLength(f) + 2;
    else {
      if (o < 4) return e.destroy(new Error("clientId must be supplied before 3.1.1")), false;
      if (a * 1 === 0) return e.destroy(new Error("clientId must be given if cleanSession set to 0")), false;
    }
    if (typeof u != "number" || u < 0 || u > 65535 || u % 1 !== 0) return e.destroy(new Error("Invalid keepalive")), false;
    y += 2, y += 1;
    let E, w;
    if (o === 5) {
      if (E = jt(e, g), !E) return false;
      y += E.length;
    }
    if (s) {
      if (typeof s != "object") return e.destroy(new Error("Invalid will")), false;
      if (!s.topic || typeof s.topic != "string") return e.destroy(new Error("Invalid will topic")), false;
      if (y += U.byteLength(s.topic) + 2, y += 2, s.payload) if (s.payload.length >= 0) typeof s.payload == "string" ? y += U.byteLength(s.payload) : y += s.payload.length;
      else return e.destroy(new Error("Invalid will payload")), false;
      if (w = {}, o === 5) {
        if (w = jt(e, s.properties), !w) return false;
        y += w.length;
      }
    }
    let S = false;
    if (d != null) if (Ad(d)) S = true, y += U.byteLength(d) + 2;
    else return e.destroy(new Error("Invalid username")), false;
    if (h != null) {
      if (!S) return e.destroy(new Error("Username is required to use password")), false;
      if (Ad(h)) y += Td(h) + 2;
      else return e.destroy(new Error("Invalid password")), false;
    }
    e.write(D.CONNECT_HEADER), Ue(e, y), Hr(e, n), i.bridgeMode && (o += 128), e.write(o === 131 ? D.VERSION131 : o === 132 ? D.VERSION132 : o === 4 ? D.VERSION4 : o === 5 ? D.VERSION5 : D.VERSION3);
    let I = 0;
    return I |= d != null ? D.USERNAME_MASK : 0, I |= h != null ? D.PASSWORD_MASK : 0, I |= s && s.retain ? D.WILL_RETAIN_MASK : 0, I |= s && s.qos ? s.qos << D.WILL_QOS_SHIFT : 0, I |= s ? D.WILL_FLAG_MASK : 0, I |= a ? D.CLEAN_SESSION_MASK : 0, e.write(U.from([I])), Te(e, u), o === 5 && E.write(), Hr(e, f), s && (o === 5 && w.write(), ur(e, s.topic), Hr(e, s.payload)), d != null && Hr(e, d), h != null && Hr(e, h), true;
  }
  function iv(t, e, r) {
    let i = r ? r.protocolVersion : 4, n = t || {}, o = i === 5 ? n.reasonCode : n.returnCode, s = n.properties, a = 2;
    if (typeof o != "number") return e.destroy(new Error("Invalid return code")), false;
    let u = null;
    if (i === 5) {
      if (u = jt(e, s), !u) return false;
      a += u.length;
    }
    return e.write(D.CONNACK_HEADER), Ue(e, a), e.write(n.sessionPresent ? D.SESSIONPRESENT_HEADER : Y0), e.write(U.from([o])), u == null ? void 0 : u.write(), true;
  }
  function nv(t, e, r) {
    qe("publish: packet: %o", t);
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.qos || 0, s = n.retain ? D.RETAIN_MASK : 0, a = n.topic, u = n.payload || G0, f = n.messageId, d = n.properties, h = 0;
    if (typeof a == "string") h += U.byteLength(a) + 2;
    else if (U.isBuffer(a)) h += a.length + 2;
    else return e.destroy(new Error("Invalid topic")), false;
    if (U.isBuffer(u) ? h += u.length : h += U.byteLength(u), o && typeof f != "number") return e.destroy(new Error("Invalid messageId")), false;
    o && (h += 2);
    let g = null;
    if (i === 5) {
      if (g = jt(e, d), !g) return false;
      h += g.length;
    }
    return e.write(D.PUBLISH_HEADER[o][n.dup ? 1 : 0][s ? 1 : 0]), Ue(e, h), Te(e, Td(a)), e.write(a), o > 0 && Te(e, f), g == null ? void 0 : g.write(), qe("publish: payload: %o", u), e.write(u);
  }
  function sv(t, e, r) {
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.cmd || "puback", s = n.messageId, a = n.dup && o === "pubrel" ? D.DUP_MASK : 0, u = 0, f = n.reasonCode, d = n.properties, h = i === 5 ? 3 : 2;
    if (o === "pubrel" && (u = 1), typeof s != "number") return e.destroy(new Error("Invalid messageId")), false;
    let g = null;
    if (i === 5 && typeof d == "object") {
      if (g = vi(e, d, r, h), !g) return false;
      h += g.length;
    }
    return e.write(D.ACKS[o][u][a][0]), h === 3 && (h += f !== 0 ? 1 : -1), Ue(e, h), Te(e, s), i === 5 && h !== 2 && e.write(U.from([f])), g !== null ? g.write() : h === 4 && e.write(U.from([0])), true;
  }
  function ov(t, e, r) {
    qe("subscribe: packet: ");
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.dup ? D.DUP_MASK : 0, s = n.messageId, a = n.subscriptions, u = n.properties, f = 0;
    if (typeof s != "number") return e.destroy(new Error("Invalid messageId")), false;
    f += 2;
    let d = null;
    if (i === 5) {
      if (d = jt(e, u), !d) return false;
      f += d.length;
    }
    if (typeof a == "object" && a.length) for (let g = 0; g < a.length; g += 1) {
      let y = a[g].topic, E = a[g].qos;
      if (typeof y != "string") return e.destroy(new Error("Invalid subscriptions - invalid topic")), false;
      if (typeof E != "number") return e.destroy(new Error("Invalid subscriptions - invalid qos")), false;
      if (i === 5) {
        if (typeof (a[g].nl || false) != "boolean") return e.destroy(new Error("Invalid subscriptions - invalid No Local")), false;
        if (typeof (a[g].rap || false) != "boolean") return e.destroy(new Error("Invalid subscriptions - invalid Retain as Published")), false;
        let I = a[g].rh || 0;
        if (typeof I != "number" || I > 2) return e.destroy(new Error("Invalid subscriptions - invalid Retain Handling")), false;
      }
      f += U.byteLength(y) + 2 + 1;
    }
    else return e.destroy(new Error("Invalid subscriptions")), false;
    qe("subscribe: writing to stream: %o", D.SUBSCRIBE_HEADER), e.write(D.SUBSCRIBE_HEADER[1][o ? 1 : 0][0]), Ue(e, f), Te(e, s), d !== null && d.write();
    let h = true;
    for (let g of a) {
      let y = g.topic, E = g.qos, w = +g.nl, S = +g.rap, I = g.rh, C;
      ur(e, y), C = D.SUBSCRIBE_OPTIONS_QOS[E], i === 5 && (C |= w ? D.SUBSCRIBE_OPTIONS_NL : 0, C |= S ? D.SUBSCRIBE_OPTIONS_RAP : 0, C |= I ? D.SUBSCRIBE_OPTIONS_RH[I] : 0), h = e.write(U.from([C]));
    }
    return h;
  }
  function av(t, e, r) {
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.messageId, s = n.granted, a = n.properties, u = 0;
    if (typeof o != "number") return e.destroy(new Error("Invalid messageId")), false;
    if (u += 2, typeof s == "object" && s.length) for (let d = 0; d < s.length; d += 1) {
      if (typeof s[d] != "number") return e.destroy(new Error("Invalid qos vector")), false;
      u += 1;
    }
    else return e.destroy(new Error("Invalid qos vector")), false;
    let f = null;
    if (i === 5) {
      if (f = vi(e, a, r, u), !f) return false;
      u += f.length;
    }
    return e.write(D.SUBACK_HEADER), Ue(e, u), Te(e, o), f !== null && f.write(), e.write(U.from(s));
  }
  function lv(t, e, r) {
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.messageId, s = n.dup ? D.DUP_MASK : 0, a = n.unsubscriptions, u = n.properties, f = 0;
    if (typeof o != "number") return e.destroy(new Error("Invalid messageId")), false;
    if (f += 2, typeof a == "object" && a.length) for (let g = 0; g < a.length; g += 1) {
      if (typeof a[g] != "string") return e.destroy(new Error("Invalid unsubscriptions")), false;
      f += U.byteLength(a[g]) + 2;
    }
    else return e.destroy(new Error("Invalid unsubscriptions")), false;
    let d = null;
    if (i === 5) {
      if (d = jt(e, u), !d) return false;
      f += d.length;
    }
    e.write(D.UNSUBSCRIBE_HEADER[1][s ? 1 : 0][0]), Ue(e, f), Te(e, o), d !== null && d.write();
    let h = true;
    for (let g = 0; g < a.length; g++) h = ur(e, a[g]);
    return h;
  }
  function uv(t, e, r) {
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.messageId, s = n.dup ? D.DUP_MASK : 0, a = n.granted, u = n.properties, f = n.cmd, d = 0, h = 2;
    if (typeof o != "number") return e.destroy(new Error("Invalid messageId")), false;
    if (i === 5) if (typeof a == "object" && a.length) for (let y = 0; y < a.length; y += 1) {
      if (typeof a[y] != "number") return e.destroy(new Error("Invalid qos vector")), false;
      h += 1;
    }
    else return e.destroy(new Error("Invalid qos vector")), false;
    let g = null;
    if (i === 5) {
      if (g = vi(e, u, r, h), !g) return false;
      h += g.length;
    }
    return e.write(D.ACKS[f][d][s][0]), Ue(e, h), Te(e, o), g !== null && g.write(), i === 5 && e.write(U.from(a)), true;
  }
  function cv(t, e, r) {
    return e.write(D.EMPTY[t.cmd]);
  }
  function fv(t, e, r) {
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.reasonCode, s = n.properties, a = i === 5 ? 1 : 0, u = null;
    if (i === 5) {
      if (u = vi(e, s, r, a), !u) return false;
      a += u.length;
    }
    return e.write(U.from([D.codes.disconnect << 4])), Ue(e, a), i === 5 && e.write(U.from([o])), u !== null && u.write(), true;
  }
  function hv(t, e, r) {
    let i = r ? r.protocolVersion : 4, n = t || {}, o = n.reasonCode, s = n.properties, a = i === 5 ? 1 : 0;
    i !== 5 && e.destroy(new Error("Invalid mqtt version for auth packet"));
    let u = vi(e, s, r, a);
    return u ? (a += u.length, e.write(U.from([D.codes.auth << 4])), Ue(e, a), e.write(U.from([o])), u !== null && u.write(), true) : false;
  }
  var md = {};
  function Ue(t, e) {
    if (e > D.VARBYTEINT_MAX) return t.destroy(new Error(`Invalid variable byte integer: ${e}`)), false;
    let r = md[e];
    return r || (r = qo(e), e < 16384 && (md[e] = r)), qe("writeVarByteInt: writing to stream: %o", r), t.write(r);
  }
  function ur(t, e) {
    let r = U.byteLength(e);
    return Te(t, r), qe("writeString: %s", e), t.write(e, "utf8");
  }
  function vd(t, e, r) {
    ur(t, e), ur(t, r);
  }
  function Uo(t, e) {
    return qe("writeNumberCached: number: %d", e), qe("writeNumberCached: %o", Tn[e]), t.write(Tn[e]);
  }
  function dv(t, e) {
    let r = X0(e);
    return qe("writeNumberGenerated: %o", r), t.write(r);
  }
  function pv(t, e) {
    let r = ev(e);
    return qe("write4ByteNumber: %o", r), t.write(r);
  }
  function Hr(t, e) {
    typeof e == "string" ? ur(t, e) : e ? (Te(t, e.length), t.write(e)) : Te(t, 0);
  }
  function jt(t, e) {
    if (typeof e != "object" || e.length != null) return { length: 1, write() {
      Sd(t, {}, 0);
    } };
    let r = 0;
    function i(o, s) {
      let a = D.propertiesTypes[o], u = 0;
      switch (a) {
        case "byte": {
          if (typeof s != "boolean") return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += 2;
          break;
        }
        case "int8": {
          if (typeof s != "number" || s < 0 || s > 255) return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += 2;
          break;
        }
        case "binary": {
          if (s && s === null) return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += 1 + U.byteLength(s) + 2;
          break;
        }
        case "int16": {
          if (typeof s != "number" || s < 0 || s > 65535) return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += 3;
          break;
        }
        case "int32": {
          if (typeof s != "number" || s < 0 || s > 4294967295) return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += 5;
          break;
        }
        case "var": {
          if (typeof s != "number" || s < 0 || s > 268435455) return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += 1 + U.byteLength(qo(s));
          break;
        }
        case "string": {
          if (typeof s != "string") return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += 3 + U.byteLength(s.toString());
          break;
        }
        case "pair": {
          if (typeof s != "object") return t.destroy(new Error(`Invalid ${o}: ${s}`)), false;
          u += Object.getOwnPropertyNames(s).reduce((f, d) => {
            let h = s[d];
            return Array.isArray(h) ? f += h.reduce((g, y) => (g += 3 + U.byteLength(d.toString()) + 2 + U.byteLength(y.toString()), g), 0) : f += 3 + U.byteLength(d.toString()) + 2 + U.byteLength(s[d].toString()), f;
          }, 0);
          break;
        }
        default:
          return t.destroy(new Error(`Invalid property ${o}: ${s}`)), false;
      }
      return u;
    }
    if (e) for (let o in e) {
      let s = 0, a = 0, u = e[o];
      if (u !== void 0) {
        if (Array.isArray(u)) for (let f = 0; f < u.length; f++) {
          if (a = i(o, u[f]), !a) return false;
          s += a;
        }
        else {
          if (a = i(o, u), !a) return false;
          s = a;
        }
        if (!s) return false;
        r += s;
      }
    }
    return { length: U.byteLength(qo(r)) + r, write() {
      Sd(t, e, r);
    } };
  }
  function vi(t, e, r, i) {
    let n = ["reasonString", "userProperties"], o = r && r.properties && r.properties.maximumPacketSize ? r.properties.maximumPacketSize : 0, s = jt(t, e);
    if (o) for (; i + s.length > o; ) {
      let a = n.shift();
      if (a && e[a]) delete e[a], s = jt(t, e);
      else return false;
    }
    return s;
  }
  function Ed(t, e, r) {
    switch (D.propertiesTypes[e]) {
      case "byte": {
        t.write(U.from([D.properties[e]])), t.write(U.from([+r]));
        break;
      }
      case "int8": {
        t.write(U.from([D.properties[e]])), t.write(U.from([r]));
        break;
      }
      case "binary": {
        t.write(U.from([D.properties[e]])), Hr(t, r);
        break;
      }
      case "int16": {
        t.write(U.from([D.properties[e]])), Te(t, r);
        break;
      }
      case "int32": {
        t.write(U.from([D.properties[e]])), pv(t, r);
        break;
      }
      case "var": {
        t.write(U.from([D.properties[e]])), Ue(t, r);
        break;
      }
      case "string": {
        t.write(U.from([D.properties[e]])), ur(t, r);
        break;
      }
      case "pair": {
        Object.getOwnPropertyNames(r).forEach((n) => {
          let o = r[n];
          Array.isArray(o) ? o.forEach((s) => {
            t.write(U.from([D.properties[e]])), vd(t, n.toString(), s.toString());
          }) : (t.write(U.from([D.properties[e]])), vd(t, n.toString(), o.toString()));
        });
        break;
      }
      default:
        return t.destroy(new Error(`Invalid property ${e} value: ${r}`)), false;
    }
  }
  function Sd(t, e, r) {
    Ue(t, r);
    for (let i in e) if (Object.prototype.hasOwnProperty.call(e, i) && e[i] != null) {
      let n = e[i];
      if (Array.isArray(n)) for (let o = 0; o < n.length; o++) Ed(t, i, n[o]);
      else Ed(t, i, n);
    }
  }
  function Td(t) {
    return t ? t instanceof U ? t.length : U.byteLength(t) : 0;
  }
  function Ad(t) {
    return typeof t == "string" || t instanceof U;
  }
  Rd.exports = Id;
});
var Pd = O((Mx, kd) => {
  _();
  v();
  m();
  var gv = No(), { EventEmitter: yv } = (Ot(), G(xt)), { Buffer: Cd } = (he(), G(be));
  function bv(t, e) {
    let r = new Do();
    return gv(t, r, e), r.concat();
  }
  var Do = class extends yv {
    constructor() {
      super(), this._array = new Array(20), this._i = 0;
    }
    write(e) {
      return this._array[this._i++] = e, true;
    }
    concat() {
      let e = 0, r = new Array(this._array.length), i = this._array, n = 0, o;
      for (o = 0; o < i.length && i[o] !== void 0; o++) typeof i[o] != "string" ? r[o] = i[o].length : r[o] = Cd.byteLength(i[o]), e += r[o];
      let s = Cd.allocUnsafe(e);
      for (o = 0; o < i.length && i[o] !== void 0; o++) typeof i[o] != "string" ? (i[o].copy(s, n), n += r[o]) : (s.write(i[o], n), n += r[o]);
      return s;
    }
    destroy(e) {
      e && this.emit("error", e);
    }
  };
  kd.exports = bv;
});
var Bd = O((Cn) => {
  _();
  v();
  m();
  Cn.parser = pd().parser;
  Cn.generate = Pd();
  Cn.writeToStream = No();
});
var Od = O((eO, xd) => {
  "use strict";
  _();
  v();
  m();
  xd.exports = wv;
  function Vr(t) {
    return t instanceof x ? x.from(t) : new t.constructor(t.buffer.slice(), t.byteOffset, t.length);
  }
  function wv(t) {
    if (t = t || {}, t.circles) return _v(t);
    let e = /* @__PURE__ */ new Map();
    if (e.set(Date, (s) => new Date(s)), e.set(Map, (s, a) => new Map(i(Array.from(s), a))), e.set(Set, (s, a) => new Set(i(Array.from(s), a))), t.constructorHandlers) for (let s of t.constructorHandlers) e.set(s[0], s[1]);
    let r = null;
    return t.proto ? o : n;
    function i(s, a) {
      let u = Object.keys(s), f = new Array(u.length);
      for (let d = 0; d < u.length; d++) {
        let h = u[d], g = s[h];
        typeof g != "object" || g === null ? f[h] = g : g.constructor !== Object && (r = e.get(g.constructor)) ? f[h] = r(g, a) : ArrayBuffer.isView(g) ? f[h] = Vr(g) : f[h] = a(g);
      }
      return f;
    }
    function n(s) {
      if (typeof s != "object" || s === null) return s;
      if (Array.isArray(s)) return i(s, n);
      if (s.constructor !== Object && (r = e.get(s.constructor))) return r(s, n);
      let a = {};
      for (let u in s) {
        if (Object.hasOwnProperty.call(s, u) === false) continue;
        let f = s[u];
        typeof f != "object" || f === null ? a[u] = f : f.constructor !== Object && (r = e.get(f.constructor)) ? a[u] = r(f, n) : ArrayBuffer.isView(f) ? a[u] = Vr(f) : a[u] = n(f);
      }
      return a;
    }
    function o(s) {
      if (typeof s != "object" || s === null) return s;
      if (Array.isArray(s)) return i(s, o);
      if (s.constructor !== Object && (r = e.get(s.constructor))) return r(s, o);
      let a = {};
      for (let u in s) {
        let f = s[u];
        typeof f != "object" || f === null ? a[u] = f : f.constructor !== Object && (r = e.get(f.constructor)) ? a[u] = r(f, o) : ArrayBuffer.isView(f) ? a[u] = Vr(f) : a[u] = o(f);
      }
      return a;
    }
  }
  function _v(t) {
    let e = [], r = [], i = /* @__PURE__ */ new Map();
    if (i.set(Date, (u) => new Date(u)), i.set(Map, (u, f) => new Map(o(Array.from(u), f))), i.set(Set, (u, f) => new Set(o(Array.from(u), f))), t.constructorHandlers) for (let u of t.constructorHandlers) i.set(u[0], u[1]);
    let n = null;
    return t.proto ? a : s;
    function o(u, f) {
      let d = Object.keys(u), h = new Array(d.length);
      for (let g = 0; g < d.length; g++) {
        let y = d[g], E = u[y];
        if (typeof E != "object" || E === null) h[y] = E;
        else if (E.constructor !== Object && (n = i.get(E.constructor))) h[y] = n(E, f);
        else if (ArrayBuffer.isView(E)) h[y] = Vr(E);
        else {
          let w = e.indexOf(E);
          w !== -1 ? h[y] = r[w] : h[y] = f(E);
        }
      }
      return h;
    }
    function s(u) {
      if (typeof u != "object" || u === null) return u;
      if (Array.isArray(u)) return o(u, s);
      if (u.constructor !== Object && (n = i.get(u.constructor))) return n(u, s);
      let f = {};
      e.push(u), r.push(f);
      for (let d in u) {
        if (Object.hasOwnProperty.call(u, d) === false) continue;
        let h = u[d];
        if (typeof h != "object" || h === null) f[d] = h;
        else if (h.constructor !== Object && (n = i.get(h.constructor))) f[d] = n(h, s);
        else if (ArrayBuffer.isView(h)) f[d] = Vr(h);
        else {
          let g = e.indexOf(h);
          g !== -1 ? f[d] = r[g] : f[d] = s(h);
        }
      }
      return e.pop(), r.pop(), f;
    }
    function a(u) {
      if (typeof u != "object" || u === null) return u;
      if (Array.isArray(u)) return o(u, a);
      if (u.constructor !== Object && (n = i.get(u.constructor))) return n(u, a);
      let f = {};
      e.push(u), r.push(f);
      for (let d in u) {
        let h = u[d];
        if (typeof h != "object" || h === null) f[d] = h;
        else if (h.constructor !== Object && (n = i.get(h.constructor))) f[d] = n(h, a);
        else if (ArrayBuffer.isView(h)) f[d] = Vr(h);
        else {
          let g = e.indexOf(h);
          g !== -1 ? f[d] = r[g] : f[d] = a(h);
        }
      }
      return e.pop(), r.pop(), f;
    }
  }
});
var Ld = O((cO, Md) => {
  "use strict";
  _();
  v();
  m();
  Md.exports = Od()();
});
var Ud = O((kn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(kn, "__esModule", { value: true });
  kn.validateTopic = qd;
  kn.validateTopics = mv;
  function qd(t) {
    let e = t.split("/");
    for (let r = 0; r < e.length; r++) if (e[r] !== "+") {
      if (e[r] === "#") return r === e.length - 1;
      if (e[r].indexOf("+") !== -1 || e[r].indexOf("#") !== -1) return false;
    }
    return true;
  }
  function mv(t) {
    if (t.length === 0) return "empty_topic_list";
    for (let e = 0; e < t.length; e++) if (!qd(t[e])) return t[e];
    return null;
  }
});
var Wo = O((Fo) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Fo, "__esModule", { value: true });
  var vv = Nt(), Ev = { objectMode: true }, Sv = { clean: true }, jo = class {
    constructor(e) {
      __publicField(this, "options");
      __publicField(this, "_inflights");
      this.options = e || {}, this.options = { ...Sv, ...e }, this._inflights = /* @__PURE__ */ new Map();
    }
    put(e, r) {
      return this._inflights.set(e.messageId, e), r && r(), this;
    }
    createStream() {
      let e = new vv.Readable(Ev), r = [], i = false, n = 0;
      return this._inflights.forEach((o, s) => {
        r.push(o);
      }), e._read = () => {
        !i && n < r.length ? e.push(r[n++]) : e.push(null);
      }, e.destroy = (o) => {
        if (!i) return i = true, setTimeout(() => {
          e.emit("close");
        }, 0), e;
      }, e;
    }
    del(e, r) {
      let i = this._inflights.get(e.messageId);
      return i ? (this._inflights.delete(e.messageId), r(null, i)) : r && r(new Error("missing packet")), this;
    }
    get(e, r) {
      let i = this._inflights.get(e.messageId);
      return i ? r(null, i) : r && r(new Error("missing packet")), this;
    }
    close(e) {
      this.options.clean && (this._inflights = null), e && e();
    }
  };
  Fo.default = jo;
});
var Dd = O(($o) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty($o, "__esModule", { value: true });
  var Nd = [0, 16, 128, 131, 135, 144, 145, 151, 153], Av = (t, e, r) => {
    t.log("handlePublish: packet %o", e), r = typeof r < "u" ? r : t.noop;
    let i = e.topic.toString(), n = e.payload, { qos: o } = e, { messageId: s } = e, { options: a } = t;
    if (t.options.protocolVersion === 5) {
      let u;
      if (e.properties && (u = e.properties.topicAlias), typeof u < "u") if (i.length === 0) if (u > 0 && u <= 65535) {
        let f = t.topicAliasRecv.getTopicByAlias(u);
        if (f) i = f, t.log("handlePublish :: topic complemented by alias. topic: %s - alias: %d", i, u);
        else {
          t.log("handlePublish :: unregistered topic alias. alias: %d", u), t.emit("error", new Error("Received unregistered Topic Alias"));
          return;
        }
      } else {
        t.log("handlePublish :: topic alias out of range. alias: %d", u), t.emit("error", new Error("Received Topic Alias is out of range"));
        return;
      }
      else if (t.topicAliasRecv.put(i, u)) t.log("handlePublish :: registered topic: %s - alias: %d", i, u);
      else {
        t.log("handlePublish :: topic alias out of range. alias: %d", u), t.emit("error", new Error("Received Topic Alias is out of range"));
        return;
      }
    }
    switch (t.log("handlePublish: qos %d", o), o) {
      case 2: {
        a.customHandleAcks(i, n, e, (u, f) => {
          if (typeof u == "number" && (f = u, u = null), u) return t.emit("error", u);
          if (Nd.indexOf(f) === -1) return t.emit("error", new Error("Wrong reason code for pubrec"));
          f ? t._sendPacket({ cmd: "pubrec", messageId: s, reasonCode: f }, r) : t.incomingStore.put(e, () => {
            t._sendPacket({ cmd: "pubrec", messageId: s }, r);
          });
        });
        break;
      }
      case 1: {
        a.customHandleAcks(i, n, e, (u, f) => {
          if (typeof u == "number" && (f = u, u = null), u) return t.emit("error", u);
          if (Nd.indexOf(f) === -1) return t.emit("error", new Error("Wrong reason code for puback"));
          f || t.emit("message", i, n, e), t.handleMessage(e, (d) => {
            if (d) return r && r(d);
            t._sendPacket({ cmd: "puback", messageId: s, reasonCode: f }, r);
          });
        });
        break;
      }
      case 0:
        t.emit("message", i, n, e), t.handleMessage(e, r);
        break;
      default:
        t.log("handlePublish: unknown QoS. Doing nothing.");
        break;
    }
  };
  $o.default = Av;
});
var jd = O((YO, Iv) => {
  Iv.exports = { version: "5.13.3" };
});
var cr = O((Ve) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ve, "__esModule", { value: true });
  Ve.MQTTJS_VERSION = Ve.nextTick = Ve.ErrorWithSubackPacket = Ve.ErrorWithReasonCode = void 0;
  Ve.applyMixin = Tv;
  var Ho = class t extends Error {
    constructor(e, r) {
      super(e);
      __publicField(this, "code");
      this.code = r, Object.setPrototypeOf(this, t.prototype), Object.getPrototypeOf(this).name = "ErrorWithReasonCode";
    }
  };
  Ve.ErrorWithReasonCode = Ho;
  var Vo = class t extends Error {
    constructor(e, r) {
      super(e);
      __publicField(this, "packet");
      this.packet = r, Object.setPrototypeOf(this, t.prototype), Object.getPrototypeOf(this).name = "ErrorWithSubackPacket";
    }
  };
  Ve.ErrorWithSubackPacket = Vo;
  function Tv(t, e, r = false) {
    let i = [e];
    for (; ; ) {
      let n = i[0], o = Object.getPrototypeOf(n);
      if (o == null ? void 0 : o.prototype) i.unshift(o);
      else break;
    }
    for (let n of i) for (let o of Object.getOwnPropertyNames(n.prototype)) (r || o !== "constructor") && Object.defineProperty(t.prototype, o, Object.getOwnPropertyDescriptor(n.prototype, o) ?? /* @__PURE__ */ Object.create(null));
  }
  Ve.nextTick = typeof (R == null ? void 0 : R.nextTick) == "function" ? R.nextTick : (t) => {
    setTimeout(t, 0);
  };
  Ve.MQTTJS_VERSION = jd().version;
});
var Ei = O((Ft) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ft, "__esModule", { value: true });
  Ft.ReasonCodes = void 0;
  var Fd = cr();
  Ft.ReasonCodes = { 0: "", 1: "Unacceptable protocol version", 2: "Identifier rejected", 3: "Server unavailable", 4: "Bad username or password", 5: "Not authorized", 16: "No matching subscribers", 17: "No subscription existed", 128: "Unspecified error", 129: "Malformed Packet", 130: "Protocol Error", 131: "Implementation specific error", 132: "Unsupported Protocol Version", 133: "Client Identifier not valid", 134: "Bad User Name or Password", 135: "Not authorized", 136: "Server unavailable", 137: "Server busy", 138: "Banned", 139: "Server shutting down", 140: "Bad authentication method", 141: "Keep Alive timeout", 142: "Session taken over", 143: "Topic Filter invalid", 144: "Topic Name invalid", 145: "Packet identifier in use", 146: "Packet Identifier not found", 147: "Receive Maximum exceeded", 148: "Topic Alias invalid", 149: "Packet too large", 150: "Message rate too high", 151: "Quota exceeded", 152: "Administrative action", 153: "Payload format invalid", 154: "Retain not supported", 155: "QoS not supported", 156: "Use another server", 157: "Server moved", 158: "Shared Subscriptions not supported", 159: "Connection rate exceeded", 160: "Maximum connect time", 161: "Subscription Identifiers not supported", 162: "Wildcard Subscriptions not supported" };
  var Rv = (t, e) => {
    let { messageId: r } = e, i = e.cmd, n = null, o = t.outgoing[r] ? t.outgoing[r].cb : null, s = null;
    if (!o) {
      t.log("_handleAck :: Server sent an ack in error. Ignoring.");
      return;
    }
    switch (t.log("_handleAck :: packet type", i), i) {
      case "pubcomp":
      case "puback": {
        let a = e.reasonCode;
        a && a > 0 && a !== 16 ? (s = new Fd.ErrorWithReasonCode(`Publish error: ${Ft.ReasonCodes[a]}`, a), t._removeOutgoingAndStoreMessage(r, () => {
          o(s, e);
        })) : t._removeOutgoingAndStoreMessage(r, o);
        break;
      }
      case "pubrec": {
        n = { cmd: "pubrel", qos: 2, messageId: r };
        let a = e.reasonCode;
        a && a > 0 && a !== 16 ? (s = new Fd.ErrorWithReasonCode(`Publish error: ${Ft.ReasonCodes[a]}`, a), t._removeOutgoingAndStoreMessage(r, () => {
          o(s, e);
        })) : t._sendPacket(n);
        break;
      }
      case "suback": {
        delete t.outgoing[r], t.messageIdProvider.deallocate(r);
        let a = e.granted;
        for (let u = 0; u < a.length; u++) {
          let f = a[u];
          if ((f & 128) !== 0) {
            s = new Error(`Subscribe error: ${Ft.ReasonCodes[f]}`), s.code = f;
            let d = t.messageIdToTopic[r];
            d && d.forEach((h) => {
              delete t._resubscribeTopics[h];
            });
          }
        }
        delete t.messageIdToTopic[r], t._invokeStoreProcessingQueue(), o(s, e);
        break;
      }
      case "unsuback": {
        delete t.outgoing[r], t.messageIdProvider.deallocate(r), t._invokeStoreProcessingQueue(), o(null, e);
        break;
      }
      default:
        t.emit("error", new Error("unrecognized packet type"));
    }
    t.disconnecting && Object.keys(t.outgoing).length === 0 && t.emit("outgoingEmpty");
  };
  Ft.default = Rv;
});
var $d = O((zo) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(zo, "__esModule", { value: true });
  var Wd = cr(), Cv = Ei(), kv = (t, e) => {
    let { options: r } = t, i = r.protocolVersion, n = i === 5 ? e.reasonCode : e.returnCode;
    if (i !== 5) {
      let o = new Wd.ErrorWithReasonCode(`Protocol error: Auth packets are only supported in MQTT 5. Your version:${i}`, n);
      t.emit("error", o);
      return;
    }
    t.handleAuth(e, (o, s) => {
      if (o) {
        t.emit("error", o);
        return;
      }
      if (n === 24) t.reconnecting = false, t._sendPacket(s);
      else {
        let a = new Wd.ErrorWithReasonCode(`Connection refused: ${Cv.ReasonCodes[n]}`, n);
        t.emit("error", a);
      }
    });
  };
  zo.default = kv;
});
var Qd = O((Bn) => {
  "use strict";
  var _a, _b, _l, _c, _d2, _l2, _f, _g, _y, _P, _B, _n, _b2, _i, _r2, _e2, _u2, _h, _a2, _s, _w, _o, __, _m, _d3, _v, _T, _c2, _t_instances, L_fn, _R, _I, _q, _p2, F_fn, _C, _x, _U, E_fn, S_fn, N_fn, O_fn, M_fn, t_fn, D_fn, k_fn, A_fn, j_fn, _e3;
  _();
  v();
  m();
  Object.defineProperty(Bn, "__esModule", { value: true });
  Bn.LRUCache = void 0;
  var zr = typeof performance == "object" && performance && typeof performance.now == "function" ? performance : Date, Vd = /* @__PURE__ */ new Set(), Ko = typeof R == "object" && R ? R : {}, zd = (t, e, r, i) => {
    typeof Ko.emitWarning == "function" ? Ko.emitWarning(t, e, r, i) : console.error(`[${r}] ${e}: ${t}`);
  }, Pn = globalThis.AbortController, Hd = globalThis.AbortSignal;
  if (typeof Pn > "u") {
    Hd = class {
      constructor() {
        __publicField(this, "onabort");
        __publicField(this, "_onabort", []);
        __publicField(this, "reason");
        __publicField(this, "aborted", false);
      }
      addEventListener(i, n) {
        this._onabort.push(n);
      }
    }, Pn = class {
      constructor() {
        __publicField(this, "signal", new Hd());
        e();
      }
      abort(i) {
        var _a3, _b3;
        if (!this.signal.aborted) {
          this.signal.reason = i, this.signal.aborted = true;
          for (let n of this.signal._onabort) n(i);
          (_b3 = (_a3 = this.signal).onabort) == null ? void 0 : _b3.call(_a3, i);
        }
      }
    };
    let t = ((_a = Ko.env) == null ? void 0 : _a.LRU_CACHE_IGNORE_AC_WARNING) !== "1", e = () => {
      t && (t = false, zd("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.", "NO_ABORT_CONTROLLER", "ENOTSUP", e));
    };
  }
  var Pv = (t) => !Vd.has(t), kM = Symbol("type"), Wt = (t) => t && t === Math.floor(t) && t > 0 && isFinite(t), Kd = (t) => Wt(t) ? t <= Math.pow(2, 8) ? Uint8Array : t <= Math.pow(2, 16) ? Uint16Array : t <= Math.pow(2, 32) ? Uint32Array : t <= Number.MAX_SAFE_INTEGER ? Kr : null : null, Kr = class extends Array {
    constructor(e) {
      super(e), this.fill(0);
    }
  }, Qo = (_b = class {
    constructor(e, r) {
      __publicField(this, "heap");
      __publicField(this, "length");
      if (!__privateGet(_b, _l)) throw new TypeError("instantiate Stack using Stack.create(n)");
      this.heap = new r(e), this.length = 0;
    }
    static create(e) {
      let r = Kd(e);
      if (!r) return [];
      __privateSet(_b, _l, true);
      let i = new _b(e, r);
      return __privateSet(_b, _l, false), i;
    }
    push(e) {
      this.heap[this.length++] = e;
    }
    pop() {
      return this.heap[--this.length];
    }
  }, _l = new WeakMap(), __privateAdd(_b, _l, false), _b), Go = (_e3 = class {
    constructor(e) {
      __privateAdd(this, _t_instances);
      __privateAdd(this, _l2);
      __privateAdd(this, _f);
      __privateAdd(this, _g);
      __privateAdd(this, _y);
      __privateAdd(this, _P);
      __privateAdd(this, _B);
      __publicField(this, "ttl");
      __publicField(this, "ttlResolution");
      __publicField(this, "ttlAutopurge");
      __publicField(this, "updateAgeOnGet");
      __publicField(this, "updateAgeOnHas");
      __publicField(this, "allowStale");
      __publicField(this, "noDisposeOnSet");
      __publicField(this, "noUpdateTTL");
      __publicField(this, "maxEntrySize");
      __publicField(this, "sizeCalculation");
      __publicField(this, "noDeleteOnFetchRejection");
      __publicField(this, "noDeleteOnStaleGet");
      __publicField(this, "allowStaleOnFetchAbort");
      __publicField(this, "allowStaleOnFetchRejection");
      __publicField(this, "ignoreFetchAbort");
      __privateAdd(this, _n);
      __privateAdd(this, _b2);
      __privateAdd(this, _i);
      __privateAdd(this, _r2);
      __privateAdd(this, _e2);
      __privateAdd(this, _u2);
      __privateAdd(this, _h);
      __privateAdd(this, _a2);
      __privateAdd(this, _s);
      __privateAdd(this, _w);
      __privateAdd(this, _o);
      __privateAdd(this, __);
      __privateAdd(this, _m);
      __privateAdd(this, _d3);
      __privateAdd(this, _v);
      __privateAdd(this, _T);
      __privateAdd(this, _c2);
      __privateAdd(this, _R, () => {
      });
      __privateAdd(this, _I, () => {
      });
      __privateAdd(this, _q, () => {
      });
      __privateAdd(this, _p2, () => false);
      __privateAdd(this, _C, (e) => {
      });
      __privateAdd(this, _x, (e, r, i) => {
      });
      __privateAdd(this, _U, (e, r, i, n) => {
        if (i || n) throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");
        return 0;
      });
      __publicField(this, _c, "LRUCache");
      let { max: r = 0, ttl: i, ttlResolution: n = 1, ttlAutopurge: o, updateAgeOnGet: s, updateAgeOnHas: a, allowStale: u, dispose: f, disposeAfter: d, noDisposeOnSet: h, noUpdateTTL: g, maxSize: y = 0, maxEntrySize: E = 0, sizeCalculation: w, fetchMethod: S, memoMethod: I, noDeleteOnFetchRejection: C, noDeleteOnStaleGet: k, allowStaleOnFetchRejection: M, allowStaleOnFetchAbort: q, ignoreFetchAbort: z } = e;
      if (r !== 0 && !Wt(r)) throw new TypeError("max option must be a nonnegative integer");
      let j = r ? Kd(r) : Array;
      if (!j) throw new Error("invalid max value: " + r);
      if (__privateSet(this, _l2, r), __privateSet(this, _f, y), this.maxEntrySize = E || __privateGet(this, _f), this.sizeCalculation = w, this.sizeCalculation) {
        if (!__privateGet(this, _f) && !this.maxEntrySize) throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");
        if (typeof this.sizeCalculation != "function") throw new TypeError("sizeCalculation set to non-function");
      }
      if (I !== void 0 && typeof I != "function") throw new TypeError("memoMethod must be a function if defined");
      if (__privateSet(this, _B, I), S !== void 0 && typeof S != "function") throw new TypeError("fetchMethod must be a function if specified");
      if (__privateSet(this, _P, S), __privateSet(this, _T, !!S), __privateSet(this, _i, /* @__PURE__ */ new Map()), __privateSet(this, _r2, new Array(r).fill(void 0)), __privateSet(this, _e2, new Array(r).fill(void 0)), __privateSet(this, _u2, new j(r)), __privateSet(this, _h, new j(r)), __privateSet(this, _a2, 0), __privateSet(this, _s, 0), __privateSet(this, _w, Qo.create(r)), __privateSet(this, _n, 0), __privateSet(this, _b2, 0), typeof f == "function" && __privateSet(this, _g, f), typeof d == "function" ? (__privateSet(this, _y, d), __privateSet(this, _o, [])) : (__privateSet(this, _y, void 0), __privateSet(this, _o, void 0)), __privateSet(this, _v, !!__privateGet(this, _g)), __privateSet(this, _c2, !!__privateGet(this, _y)), this.noDisposeOnSet = !!h, this.noUpdateTTL = !!g, this.noDeleteOnFetchRejection = !!C, this.allowStaleOnFetchRejection = !!M, this.allowStaleOnFetchAbort = !!q, this.ignoreFetchAbort = !!z, this.maxEntrySize !== 0) {
        if (__privateGet(this, _f) !== 0 && !Wt(__privateGet(this, _f))) throw new TypeError("maxSize must be a positive integer if specified");
        if (!Wt(this.maxEntrySize)) throw new TypeError("maxEntrySize must be a positive integer if specified");
        __privateMethod(this, _t_instances, F_fn).call(this);
      }
      if (this.allowStale = !!u, this.noDeleteOnStaleGet = !!k, this.updateAgeOnGet = !!s, this.updateAgeOnHas = !!a, this.ttlResolution = Wt(n) || n === 0 ? n : 1, this.ttlAutopurge = !!o, this.ttl = i || 0, this.ttl) {
        if (!Wt(this.ttl)) throw new TypeError("ttl must be a positive integer if specified");
        __privateMethod(this, _t_instances, L_fn).call(this);
      }
      if (__privateGet(this, _l2) === 0 && this.ttl === 0 && __privateGet(this, _f) === 0) throw new TypeError("At least one of max, maxSize, or ttl is required");
      if (!this.ttlAutopurge && !__privateGet(this, _l2) && !__privateGet(this, _f)) {
        let Q = "LRU_CACHE_UNBOUNDED";
        Pv(Q) && (Vd.add(Q), zd("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.", "UnboundedCacheWarning", Q, _e3));
      }
    }
    static unsafeExposeInternals(e) {
      return { starts: __privateGet(e, _m), ttls: __privateGet(e, _d3), sizes: __privateGet(e, __), keyMap: __privateGet(e, _i), keyList: __privateGet(e, _r2), valList: __privateGet(e, _e2), next: __privateGet(e, _u2), prev: __privateGet(e, _h), get head() {
        return __privateGet(e, _a2);
      }, get tail() {
        return __privateGet(e, _s);
      }, free: __privateGet(e, _w), isBackgroundFetch: (r) => {
        var _a3;
        return __privateMethod(_a3 = e, _t_instances, t_fn).call(_a3, r);
      }, backgroundFetch: (r, i, n, o) => {
        var _a3;
        return __privateMethod(_a3 = e, _t_instances, M_fn).call(_a3, r, i, n, o);
      }, moveToTail: (r) => {
        var _a3;
        return __privateMethod(_a3 = e, _t_instances, k_fn).call(_a3, r);
      }, indexes: (r) => {
        var _a3;
        return __privateMethod(_a3 = e, _t_instances, E_fn).call(_a3, r);
      }, rindexes: (r) => {
        var _a3;
        return __privateMethod(_a3 = e, _t_instances, S_fn).call(_a3, r);
      }, isStale: (r) => {
        var _a3;
        return __privateGet(_a3 = e, _p2).call(_a3, r);
      } };
    }
    get max() {
      return __privateGet(this, _l2);
    }
    get maxSize() {
      return __privateGet(this, _f);
    }
    get calculatedSize() {
      return __privateGet(this, _b2);
    }
    get size() {
      return __privateGet(this, _n);
    }
    get fetchMethod() {
      return __privateGet(this, _P);
    }
    get memoMethod() {
      return __privateGet(this, _B);
    }
    get dispose() {
      return __privateGet(this, _g);
    }
    get disposeAfter() {
      return __privateGet(this, _y);
    }
    getRemainingTTL(e) {
      return __privateGet(this, _i).has(e) ? 1 / 0 : 0;
    }
    *entries() {
      for (let e of __privateMethod(this, _t_instances, E_fn).call(this)) __privateGet(this, _e2)[e] !== void 0 && __privateGet(this, _r2)[e] !== void 0 && !__privateMethod(this, _t_instances, t_fn).call(this, __privateGet(this, _e2)[e]) && (yield [__privateGet(this, _r2)[e], __privateGet(this, _e2)[e]]);
    }
    *rentries() {
      for (let e of __privateMethod(this, _t_instances, S_fn).call(this)) __privateGet(this, _e2)[e] !== void 0 && __privateGet(this, _r2)[e] !== void 0 && !__privateMethod(this, _t_instances, t_fn).call(this, __privateGet(this, _e2)[e]) && (yield [__privateGet(this, _r2)[e], __privateGet(this, _e2)[e]]);
    }
    *keys() {
      for (let e of __privateMethod(this, _t_instances, E_fn).call(this)) {
        let r = __privateGet(this, _r2)[e];
        r !== void 0 && !__privateMethod(this, _t_instances, t_fn).call(this, __privateGet(this, _e2)[e]) && (yield r);
      }
    }
    *rkeys() {
      for (let e of __privateMethod(this, _t_instances, S_fn).call(this)) {
        let r = __privateGet(this, _r2)[e];
        r !== void 0 && !__privateMethod(this, _t_instances, t_fn).call(this, __privateGet(this, _e2)[e]) && (yield r);
      }
    }
    *values() {
      for (let e of __privateMethod(this, _t_instances, E_fn).call(this)) __privateGet(this, _e2)[e] !== void 0 && !__privateMethod(this, _t_instances, t_fn).call(this, __privateGet(this, _e2)[e]) && (yield __privateGet(this, _e2)[e]);
    }
    *rvalues() {
      for (let e of __privateMethod(this, _t_instances, S_fn).call(this)) __privateGet(this, _e2)[e] !== void 0 && !__privateMethod(this, _t_instances, t_fn).call(this, __privateGet(this, _e2)[e]) && (yield __privateGet(this, _e2)[e]);
    }
    [(_d2 = Symbol.iterator, _c = Symbol.toStringTag, _d2)]() {
      return this.entries();
    }
    find(e, r = {}) {
      for (let i of __privateMethod(this, _t_instances, E_fn).call(this)) {
        let n = __privateGet(this, _e2)[i], o = __privateMethod(this, _t_instances, t_fn).call(this, n) ? n.__staleWhileFetching : n;
        if (o !== void 0 && e(o, __privateGet(this, _r2)[i], this)) return this.get(__privateGet(this, _r2)[i], r);
      }
    }
    forEach(e, r = this) {
      for (let i of __privateMethod(this, _t_instances, E_fn).call(this)) {
        let n = __privateGet(this, _e2)[i], o = __privateMethod(this, _t_instances, t_fn).call(this, n) ? n.__staleWhileFetching : n;
        o !== void 0 && e.call(r, o, __privateGet(this, _r2)[i], this);
      }
    }
    rforEach(e, r = this) {
      for (let i of __privateMethod(this, _t_instances, S_fn).call(this)) {
        let n = __privateGet(this, _e2)[i], o = __privateMethod(this, _t_instances, t_fn).call(this, n) ? n.__staleWhileFetching : n;
        o !== void 0 && e.call(r, o, __privateGet(this, _r2)[i], this);
      }
    }
    purgeStale() {
      let e = false;
      for (let r of __privateMethod(this, _t_instances, S_fn).call(this, { allowStale: true })) __privateGet(this, _p2).call(this, r) && (__privateMethod(this, _t_instances, A_fn).call(this, __privateGet(this, _r2)[r], "expire"), e = true);
      return e;
    }
    info(e) {
      let r = __privateGet(this, _i).get(e);
      if (r === void 0) return;
      let i = __privateGet(this, _e2)[r], n = __privateMethod(this, _t_instances, t_fn).call(this, i) ? i.__staleWhileFetching : i;
      if (n === void 0) return;
      let o = { value: n };
      if (__privateGet(this, _d3) && __privateGet(this, _m)) {
        let s = __privateGet(this, _d3)[r], a = __privateGet(this, _m)[r];
        if (s && a) {
          let u = s - (zr.now() - a);
          o.ttl = u, o.start = Date.now();
        }
      }
      return __privateGet(this, __) && (o.size = __privateGet(this, __)[r]), o;
    }
    dump() {
      let e = [];
      for (let r of __privateMethod(this, _t_instances, E_fn).call(this, { allowStale: true })) {
        let i = __privateGet(this, _r2)[r], n = __privateGet(this, _e2)[r], o = __privateMethod(this, _t_instances, t_fn).call(this, n) ? n.__staleWhileFetching : n;
        if (o === void 0 || i === void 0) continue;
        let s = { value: o };
        if (__privateGet(this, _d3) && __privateGet(this, _m)) {
          s.ttl = __privateGet(this, _d3)[r];
          let a = zr.now() - __privateGet(this, _m)[r];
          s.start = Math.floor(Date.now() - a);
        }
        __privateGet(this, __) && (s.size = __privateGet(this, __)[r]), e.unshift([i, s]);
      }
      return e;
    }
    load(e) {
      this.clear();
      for (let [r, i] of e) {
        if (i.start) {
          let n = Date.now() - i.start;
          i.start = zr.now() - n;
        }
        this.set(r, i.value, i);
      }
    }
    set(e, r, i = {}) {
      var _a3, _b3, _c3, _d4, _e4;
      if (r === void 0) return this.delete(e), this;
      let { ttl: n = this.ttl, start: o, noDisposeOnSet: s = this.noDisposeOnSet, sizeCalculation: a = this.sizeCalculation, status: u } = i, { noUpdateTTL: f = this.noUpdateTTL } = i, d = __privateGet(this, _U).call(this, e, r, i.size || 0, a);
      if (this.maxEntrySize && d > this.maxEntrySize) return u && (u.set = "miss", u.maxEntrySizeExceeded = true), __privateMethod(this, _t_instances, A_fn).call(this, e, "set"), this;
      let h = __privateGet(this, _n) === 0 ? void 0 : __privateGet(this, _i).get(e);
      if (h === void 0) h = __privateGet(this, _n) === 0 ? __privateGet(this, _s) : __privateGet(this, _w).length !== 0 ? __privateGet(this, _w).pop() : __privateGet(this, _n) === __privateGet(this, _l2) ? __privateMethod(this, _t_instances, O_fn).call(this, false) : __privateGet(this, _n), __privateGet(this, _r2)[h] = e, __privateGet(this, _e2)[h] = r, __privateGet(this, _i).set(e, h), __privateGet(this, _u2)[__privateGet(this, _s)] = h, __privateGet(this, _h)[h] = __privateGet(this, _s), __privateSet(this, _s, h), __privateWrapper(this, _n)._++, __privateGet(this, _x).call(this, h, d, u), u && (u.set = "add"), f = false;
      else {
        __privateMethod(this, _t_instances, k_fn).call(this, h);
        let g = __privateGet(this, _e2)[h];
        if (r !== g) {
          if (__privateGet(this, _T) && __privateMethod(this, _t_instances, t_fn).call(this, g)) {
            g.__abortController.abort(new Error("replaced"));
            let { __staleWhileFetching: y } = g;
            y !== void 0 && !s && (__privateGet(this, _v) && ((_a3 = __privateGet(this, _g)) == null ? void 0 : _a3.call(this, y, e, "set")), __privateGet(this, _c2) && ((_b3 = __privateGet(this, _o)) == null ? void 0 : _b3.push([y, e, "set"])));
          } else s || (__privateGet(this, _v) && ((_c3 = __privateGet(this, _g)) == null ? void 0 : _c3.call(this, g, e, "set")), __privateGet(this, _c2) && ((_d4 = __privateGet(this, _o)) == null ? void 0 : _d4.push([g, e, "set"])));
          if (__privateGet(this, _C).call(this, h), __privateGet(this, _x).call(this, h, d, u), __privateGet(this, _e2)[h] = r, u) {
            u.set = "replace";
            let y = g && __privateMethod(this, _t_instances, t_fn).call(this, g) ? g.__staleWhileFetching : g;
            y !== void 0 && (u.oldValue = y);
          }
        } else u && (u.set = "update");
      }
      if (n !== 0 && !__privateGet(this, _d3) && __privateMethod(this, _t_instances, L_fn).call(this), __privateGet(this, _d3) && (f || __privateGet(this, _q).call(this, h, n, o), u && __privateGet(this, _I).call(this, u, h)), !s && __privateGet(this, _c2) && __privateGet(this, _o)) {
        let g = __privateGet(this, _o), y;
        for (; y = g == null ? void 0 : g.shift(); ) (_e4 = __privateGet(this, _y)) == null ? void 0 : _e4.call(this, ...y);
      }
      return this;
    }
    pop() {
      var _a3;
      try {
        for (; __privateGet(this, _n); ) {
          let e = __privateGet(this, _e2)[__privateGet(this, _a2)];
          if (__privateMethod(this, _t_instances, O_fn).call(this, true), __privateMethod(this, _t_instances, t_fn).call(this, e)) {
            if (e.__staleWhileFetching) return e.__staleWhileFetching;
          } else if (e !== void 0) return e;
        }
      } finally {
        if (__privateGet(this, _c2) && __privateGet(this, _o)) {
          let e = __privateGet(this, _o), r;
          for (; r = e == null ? void 0 : e.shift(); ) (_a3 = __privateGet(this, _y)) == null ? void 0 : _a3.call(this, ...r);
        }
      }
    }
    has(e, r = {}) {
      let { updateAgeOnHas: i = this.updateAgeOnHas, status: n } = r, o = __privateGet(this, _i).get(e);
      if (o !== void 0) {
        let s = __privateGet(this, _e2)[o];
        if (__privateMethod(this, _t_instances, t_fn).call(this, s) && s.__staleWhileFetching === void 0) return false;
        if (__privateGet(this, _p2).call(this, o)) n && (n.has = "stale", __privateGet(this, _I).call(this, n, o));
        else return i && __privateGet(this, _R).call(this, o), n && (n.has = "hit", __privateGet(this, _I).call(this, n, o)), true;
      } else n && (n.has = "miss");
      return false;
    }
    peek(e, r = {}) {
      let { allowStale: i = this.allowStale } = r, n = __privateGet(this, _i).get(e);
      if (n === void 0 || !i && __privateGet(this, _p2).call(this, n)) return;
      let o = __privateGet(this, _e2)[n];
      return __privateMethod(this, _t_instances, t_fn).call(this, o) ? o.__staleWhileFetching : o;
    }
    async fetch(e, r = {}) {
      let { allowStale: i = this.allowStale, updateAgeOnGet: n = this.updateAgeOnGet, noDeleteOnStaleGet: o = this.noDeleteOnStaleGet, ttl: s = this.ttl, noDisposeOnSet: a = this.noDisposeOnSet, size: u = 0, sizeCalculation: f = this.sizeCalculation, noUpdateTTL: d = this.noUpdateTTL, noDeleteOnFetchRejection: h = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection: g = this.allowStaleOnFetchRejection, ignoreFetchAbort: y = this.ignoreFetchAbort, allowStaleOnFetchAbort: E = this.allowStaleOnFetchAbort, context: w, forceRefresh: S = false, status: I, signal: C } = r;
      if (!__privateGet(this, _T)) return I && (I.fetch = "get"), this.get(e, { allowStale: i, updateAgeOnGet: n, noDeleteOnStaleGet: o, status: I });
      let k = { allowStale: i, updateAgeOnGet: n, noDeleteOnStaleGet: o, ttl: s, noDisposeOnSet: a, size: u, sizeCalculation: f, noUpdateTTL: d, noDeleteOnFetchRejection: h, allowStaleOnFetchRejection: g, allowStaleOnFetchAbort: E, ignoreFetchAbort: y, status: I, signal: C }, M = __privateGet(this, _i).get(e);
      if (M === void 0) {
        I && (I.fetch = "miss");
        let q = __privateMethod(this, _t_instances, M_fn).call(this, e, M, k, w);
        return q.__returned = q;
      } else {
        let q = __privateGet(this, _e2)[M];
        if (__privateMethod(this, _t_instances, t_fn).call(this, q)) {
          let te = i && q.__staleWhileFetching !== void 0;
          return I && (I.fetch = "inflight", te && (I.returnedStale = true)), te ? q.__staleWhileFetching : q.__returned = q;
        }
        let z = __privateGet(this, _p2).call(this, M);
        if (!S && !z) return I && (I.fetch = "hit"), __privateMethod(this, _t_instances, k_fn).call(this, M), n && __privateGet(this, _R).call(this, M), I && __privateGet(this, _I).call(this, I, M), q;
        let j = __privateMethod(this, _t_instances, M_fn).call(this, e, M, k, w), $ = j.__staleWhileFetching !== void 0 && i;
        return I && (I.fetch = z ? "stale" : "refresh", $ && z && (I.returnedStale = true)), $ ? j.__staleWhileFetching : j.__returned = j;
      }
    }
    async forceFetch(e, r = {}) {
      let i = await this.fetch(e, r);
      if (i === void 0) throw new Error("fetch() returned undefined");
      return i;
    }
    memo(e, r = {}) {
      let i = __privateGet(this, _B);
      if (!i) throw new Error("no memoMethod provided to constructor");
      let { context: n, forceRefresh: o, ...s } = r, a = this.get(e, s);
      if (!o && a !== void 0) return a;
      let u = i(e, a, { options: s, context: n });
      return this.set(e, u, s), u;
    }
    get(e, r = {}) {
      let { allowStale: i = this.allowStale, updateAgeOnGet: n = this.updateAgeOnGet, noDeleteOnStaleGet: o = this.noDeleteOnStaleGet, status: s } = r, a = __privateGet(this, _i).get(e);
      if (a !== void 0) {
        let u = __privateGet(this, _e2)[a], f = __privateMethod(this, _t_instances, t_fn).call(this, u);
        return s && __privateGet(this, _I).call(this, s, a), __privateGet(this, _p2).call(this, a) ? (s && (s.get = "stale"), f ? (s && i && u.__staleWhileFetching !== void 0 && (s.returnedStale = true), i ? u.__staleWhileFetching : void 0) : (o || __privateMethod(this, _t_instances, A_fn).call(this, e, "expire"), s && i && (s.returnedStale = true), i ? u : void 0)) : (s && (s.get = "hit"), f ? u.__staleWhileFetching : (__privateMethod(this, _t_instances, k_fn).call(this, a), n && __privateGet(this, _R).call(this, a), u));
      } else s && (s.get = "miss");
    }
    delete(e) {
      return __privateMethod(this, _t_instances, A_fn).call(this, e, "delete");
    }
    clear() {
      return __privateMethod(this, _t_instances, j_fn).call(this, "delete");
    }
  }, _l2 = new WeakMap(), _f = new WeakMap(), _g = new WeakMap(), _y = new WeakMap(), _P = new WeakMap(), _B = new WeakMap(), _n = new WeakMap(), _b2 = new WeakMap(), _i = new WeakMap(), _r2 = new WeakMap(), _e2 = new WeakMap(), _u2 = new WeakMap(), _h = new WeakMap(), _a2 = new WeakMap(), _s = new WeakMap(), _w = new WeakMap(), _o = new WeakMap(), __ = new WeakMap(), _m = new WeakMap(), _d3 = new WeakMap(), _v = new WeakMap(), _T = new WeakMap(), _c2 = new WeakMap(), _t_instances = new WeakSet(), L_fn = function() {
    let e = new Kr(__privateGet(this, _l2)), r = new Kr(__privateGet(this, _l2));
    __privateSet(this, _d3, e), __privateSet(this, _m, r), __privateSet(this, _q, (o, s, a = zr.now()) => {
      if (r[o] = s !== 0 ? a : 0, e[o] = s, s !== 0 && this.ttlAutopurge) {
        let u = setTimeout(() => {
          __privateGet(this, _p2).call(this, o) && __privateMethod(this, _t_instances, A_fn).call(this, __privateGet(this, _r2)[o], "expire");
        }, s + 1);
        u.unref && u.unref();
      }
    }), __privateSet(this, _R, (o) => {
      r[o] = e[o] !== 0 ? zr.now() : 0;
    }), __privateSet(this, _I, (o, s) => {
      if (e[s]) {
        let a = e[s], u = r[s];
        if (!a || !u) return;
        o.ttl = a, o.start = u, o.now = i || n();
        let f = o.now - u;
        o.remainingTTL = a - f;
      }
    });
    let i = 0, n = () => {
      let o = zr.now();
      if (this.ttlResolution > 0) {
        i = o;
        let s = setTimeout(() => i = 0, this.ttlResolution);
        s.unref && s.unref();
      }
      return o;
    };
    this.getRemainingTTL = (o) => {
      let s = __privateGet(this, _i).get(o);
      if (s === void 0) return 0;
      let a = e[s], u = r[s];
      if (!a || !u) return 1 / 0;
      let f = (i || n()) - u;
      return a - f;
    }, __privateSet(this, _p2, (o) => {
      let s = r[o], a = e[o];
      return !!a && !!s && (i || n()) - s > a;
    });
  }, _R = new WeakMap(), _I = new WeakMap(), _q = new WeakMap(), _p2 = new WeakMap(), F_fn = function() {
    let e = new Kr(__privateGet(this, _l2));
    __privateSet(this, _b2, 0), __privateSet(this, __, e), __privateSet(this, _C, (r) => {
      __privateSet(this, _b2, __privateGet(this, _b2) - e[r]), e[r] = 0;
    }), __privateSet(this, _U, (r, i, n, o) => {
      if (__privateMethod(this, _t_instances, t_fn).call(this, i)) return 0;
      if (!Wt(n)) if (o) {
        if (typeof o != "function") throw new TypeError("sizeCalculation must be a function");
        if (n = o(i, r), !Wt(n)) throw new TypeError("sizeCalculation return invalid (expect positive integer)");
      } else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");
      return n;
    }), __privateSet(this, _x, (r, i, n) => {
      if (e[r] = i, __privateGet(this, _f)) {
        let o = __privateGet(this, _f) - e[r];
        for (; __privateGet(this, _b2) > o; ) __privateMethod(this, _t_instances, O_fn).call(this, true);
      }
      __privateSet(this, _b2, __privateGet(this, _b2) + e[r]), n && (n.entrySize = i, n.totalCalculatedSize = __privateGet(this, _b2));
    });
  }, _C = new WeakMap(), _x = new WeakMap(), _U = new WeakMap(), E_fn = function* ({ allowStale: e = this.allowStale } = {}) {
    if (__privateGet(this, _n)) for (let r = __privateGet(this, _s); !(!__privateMethod(this, _t_instances, N_fn).call(this, r) || ((e || !__privateGet(this, _p2).call(this, r)) && (yield r), r === __privateGet(this, _a2))); ) r = __privateGet(this, _h)[r];
  }, S_fn = function* ({ allowStale: e = this.allowStale } = {}) {
    if (__privateGet(this, _n)) for (let r = __privateGet(this, _a2); !(!__privateMethod(this, _t_instances, N_fn).call(this, r) || ((e || !__privateGet(this, _p2).call(this, r)) && (yield r), r === __privateGet(this, _s))); ) r = __privateGet(this, _u2)[r];
  }, N_fn = function(e) {
    return e !== void 0 && __privateGet(this, _i).get(__privateGet(this, _r2)[e]) === e;
  }, O_fn = function(e) {
    var _a3, _b3;
    let r = __privateGet(this, _a2), i = __privateGet(this, _r2)[r], n = __privateGet(this, _e2)[r];
    return __privateGet(this, _T) && __privateMethod(this, _t_instances, t_fn).call(this, n) ? n.__abortController.abort(new Error("evicted")) : (__privateGet(this, _v) || __privateGet(this, _c2)) && (__privateGet(this, _v) && ((_a3 = __privateGet(this, _g)) == null ? void 0 : _a3.call(this, n, i, "evict")), __privateGet(this, _c2) && ((_b3 = __privateGet(this, _o)) == null ? void 0 : _b3.push([n, i, "evict"]))), __privateGet(this, _C).call(this, r), e && (__privateGet(this, _r2)[r] = void 0, __privateGet(this, _e2)[r] = void 0, __privateGet(this, _w).push(r)), __privateGet(this, _n) === 1 ? (__privateSet(this, _a2, __privateSet(this, _s, 0)), __privateGet(this, _w).length = 0) : __privateSet(this, _a2, __privateGet(this, _u2)[r]), __privateGet(this, _i).delete(i), __privateWrapper(this, _n)._--, r;
  }, M_fn = function(e, r, i, n) {
    let o = r === void 0 ? void 0 : __privateGet(this, _e2)[r];
    if (__privateMethod(this, _t_instances, t_fn).call(this, o)) return o;
    let s = new Pn(), { signal: a } = i;
    a == null ? void 0 : a.addEventListener("abort", () => s.abort(a.reason), { signal: s.signal });
    let u = { signal: s.signal, options: i, context: n }, f = (w, S = false) => {
      let { aborted: I } = s.signal, C = i.ignoreFetchAbort && w !== void 0;
      if (i.status && (I && !S ? (i.status.fetchAborted = true, i.status.fetchError = s.signal.reason, C && (i.status.fetchAbortIgnored = true)) : i.status.fetchResolved = true), I && !C && !S) return h(s.signal.reason);
      let k = y;
      return __privateGet(this, _e2)[r] === y && (w === void 0 ? k.__staleWhileFetching ? __privateGet(this, _e2)[r] = k.__staleWhileFetching : __privateMethod(this, _t_instances, A_fn).call(this, e, "fetch") : (i.status && (i.status.fetchUpdated = true), this.set(e, w, u.options))), w;
    }, d = (w) => (i.status && (i.status.fetchRejected = true, i.status.fetchError = w), h(w)), h = (w) => {
      let { aborted: S } = s.signal, I = S && i.allowStaleOnFetchAbort, C = I || i.allowStaleOnFetchRejection, k = C || i.noDeleteOnFetchRejection, M = y;
      if (__privateGet(this, _e2)[r] === y && (!k || M.__staleWhileFetching === void 0 ? __privateMethod(this, _t_instances, A_fn).call(this, e, "fetch") : I || (__privateGet(this, _e2)[r] = M.__staleWhileFetching)), C) return i.status && M.__staleWhileFetching !== void 0 && (i.status.returnedStale = true), M.__staleWhileFetching;
      if (M.__returned === M) throw w;
    }, g = (w, S) => {
      var _a3;
      let I = (_a3 = __privateGet(this, _P)) == null ? void 0 : _a3.call(this, e, o, u);
      I && I instanceof Promise && I.then((C) => w(C === void 0 ? void 0 : C), S), s.signal.addEventListener("abort", () => {
        (!i.ignoreFetchAbort || i.allowStaleOnFetchAbort) && (w(void 0), i.allowStaleOnFetchAbort && (w = (C) => f(C, true)));
      });
    };
    i.status && (i.status.fetchDispatched = true);
    let y = new Promise(g).then(f, d), E = Object.assign(y, { __abortController: s, __staleWhileFetching: o, __returned: void 0 });
    return r === void 0 ? (this.set(e, E, { ...u.options, status: void 0 }), r = __privateGet(this, _i).get(e)) : __privateGet(this, _e2)[r] = E, E;
  }, t_fn = function(e) {
    if (!__privateGet(this, _T)) return false;
    let r = e;
    return !!r && r instanceof Promise && r.hasOwnProperty("__staleWhileFetching") && r.__abortController instanceof Pn;
  }, D_fn = function(e, r) {
    __privateGet(this, _h)[r] = e, __privateGet(this, _u2)[e] = r;
  }, k_fn = function(e) {
    e !== __privateGet(this, _s) && (e === __privateGet(this, _a2) ? __privateSet(this, _a2, __privateGet(this, _u2)[e]) : __privateMethod(this, _t_instances, D_fn).call(this, __privateGet(this, _h)[e], __privateGet(this, _u2)[e]), __privateMethod(this, _t_instances, D_fn).call(this, __privateGet(this, _s), e), __privateSet(this, _s, e));
  }, A_fn = function(e, r) {
    var _a3, _b3, _c3, _d4;
    let i = false;
    if (__privateGet(this, _n) !== 0) {
      let n = __privateGet(this, _i).get(e);
      if (n !== void 0) if (i = true, __privateGet(this, _n) === 1) __privateMethod(this, _t_instances, j_fn).call(this, r);
      else {
        __privateGet(this, _C).call(this, n);
        let o = __privateGet(this, _e2)[n];
        if (__privateMethod(this, _t_instances, t_fn).call(this, o) ? o.__abortController.abort(new Error("deleted")) : (__privateGet(this, _v) || __privateGet(this, _c2)) && (__privateGet(this, _v) && ((_a3 = __privateGet(this, _g)) == null ? void 0 : _a3.call(this, o, e, r)), __privateGet(this, _c2) && ((_b3 = __privateGet(this, _o)) == null ? void 0 : _b3.push([o, e, r]))), __privateGet(this, _i).delete(e), __privateGet(this, _r2)[n] = void 0, __privateGet(this, _e2)[n] = void 0, n === __privateGet(this, _s)) __privateSet(this, _s, __privateGet(this, _h)[n]);
        else if (n === __privateGet(this, _a2)) __privateSet(this, _a2, __privateGet(this, _u2)[n]);
        else {
          let s = __privateGet(this, _h)[n];
          __privateGet(this, _u2)[s] = __privateGet(this, _u2)[n];
          let a = __privateGet(this, _u2)[n];
          __privateGet(this, _h)[a] = __privateGet(this, _h)[n];
        }
        __privateWrapper(this, _n)._--, __privateGet(this, _w).push(n);
      }
    }
    if (__privateGet(this, _c2) && ((_c3 = __privateGet(this, _o)) == null ? void 0 : _c3.length)) {
      let n = __privateGet(this, _o), o;
      for (; o = n == null ? void 0 : n.shift(); ) (_d4 = __privateGet(this, _y)) == null ? void 0 : _d4.call(this, ...o);
    }
    return i;
  }, j_fn = function(e) {
    var _a3, _b3, _c3;
    for (let r of __privateMethod(this, _t_instances, S_fn).call(this, { allowStale: true })) {
      let i = __privateGet(this, _e2)[r];
      if (__privateMethod(this, _t_instances, t_fn).call(this, i)) i.__abortController.abort(new Error("deleted"));
      else {
        let n = __privateGet(this, _r2)[r];
        __privateGet(this, _v) && ((_a3 = __privateGet(this, _g)) == null ? void 0 : _a3.call(this, i, n, e)), __privateGet(this, _c2) && ((_b3 = __privateGet(this, _o)) == null ? void 0 : _b3.push([i, n, e]));
      }
    }
    if (__privateGet(this, _i).clear(), __privateGet(this, _e2).fill(void 0), __privateGet(this, _r2).fill(void 0), __privateGet(this, _d3) && __privateGet(this, _m) && (__privateGet(this, _d3).fill(0), __privateGet(this, _m).fill(0)), __privateGet(this, __) && __privateGet(this, __).fill(0), __privateSet(this, _a2, 0), __privateSet(this, _s, 0), __privateGet(this, _w).length = 0, __privateSet(this, _b2, 0), __privateSet(this, _n, 0), __privateGet(this, _c2) && __privateGet(this, _o)) {
      let r = __privateGet(this, _o), i;
      for (; i = r == null ? void 0 : r.shift(); ) (_c3 = __privateGet(this, _y)) == null ? void 0 : _c3.call(this, ...i);
    }
  }, _e3);
  Bn.LRUCache = Go;
});
var st = O(($t) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty($t, "t", { value: true });
  $t.ContainerIterator = $t.Container = $t.Base = void 0;
  var Yo = class {
    constructor(e = 0) {
      this.iteratorType = e;
    }
    equals(e) {
      return this.o === e.o;
    }
  };
  $t.ContainerIterator = Yo;
  var xn = class {
    constructor() {
      this.i = 0;
    }
    get length() {
      return this.i;
    }
    size() {
      return this.i;
    }
    empty() {
      return this.i === 0;
    }
  };
  $t.Base = xn;
  var Jo = class extends xn {
  };
  $t.Container = Jo;
});
var Gd = O((On) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(On, "t", { value: true });
  On.default = void 0;
  var Bv = st(), Xo = class extends Bv.Base {
    constructor(e = []) {
      super(), this.S = [];
      let r = this;
      e.forEach(function(i) {
        r.push(i);
      });
    }
    clear() {
      this.i = 0, this.S = [];
    }
    push(e) {
      return this.S.push(e), this.i += 1, this.i;
    }
    pop() {
      if (this.i !== 0) return this.i -= 1, this.S.pop();
    }
    top() {
      return this.S[this.i - 1];
    }
  }, xv = Xo;
  On.default = xv;
});
var Yd = O((Mn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Mn, "t", { value: true });
  Mn.default = void 0;
  var Ov = st(), Zo = class extends Ov.Base {
    constructor(e = []) {
      super(), this.j = 0, this.q = [];
      let r = this;
      e.forEach(function(i) {
        r.push(i);
      });
    }
    clear() {
      this.q = [], this.i = this.j = 0;
    }
    push(e) {
      let r = this.q.length;
      if (this.j / r > 0.5 && this.j + this.i >= r && r > 4096) {
        let i = this.i;
        for (let n = 0; n < i; ++n) this.q[n] = this.q[this.j + n];
        this.j = 0, this.q[this.i] = e;
      } else this.q[this.j + this.i] = e;
      return ++this.i;
    }
    pop() {
      if (this.i === 0) return;
      let e = this.q[this.j++];
      return this.i -= 1, e;
    }
    front() {
      if (this.i !== 0) return this.q[this.j];
    }
  }, Mv = Zo;
  Mn.default = Mv;
});
var Jd = O((Ln) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ln, "t", { value: true });
  Ln.default = void 0;
  var Lv = st(), ea = class extends Lv.Base {
    constructor(e = [], r = function(n, o) {
      return n > o ? -1 : n < o ? 1 : 0;
    }, i = true) {
      if (super(), this.v = r, Array.isArray(e)) this.C = i ? [...e] : e;
      else {
        this.C = [];
        let o = this;
        e.forEach(function(s) {
          o.C.push(s);
        });
      }
      this.i = this.C.length;
      let n = this.i >> 1;
      for (let o = this.i - 1 >> 1; o >= 0; --o) this.k(o, n);
    }
    m(e) {
      let r = this.C[e];
      for (; e > 0; ) {
        let i = e - 1 >> 1, n = this.C[i];
        if (this.v(n, r) <= 0) break;
        this.C[e] = n, e = i;
      }
      this.C[e] = r;
    }
    k(e, r) {
      let i = this.C[e];
      for (; e < r; ) {
        let n = e << 1 | 1, o = n + 1, s = this.C[n];
        if (o < this.i && this.v(s, this.C[o]) > 0 && (n = o, s = this.C[o]), this.v(s, i) >= 0) break;
        this.C[e] = s, e = n;
      }
      this.C[e] = i;
    }
    clear() {
      this.i = 0, this.C.length = 0;
    }
    push(e) {
      this.C.push(e), this.m(this.i), this.i += 1;
    }
    pop() {
      if (this.i === 0) return;
      let e = this.C[0], r = this.C.pop();
      return this.i -= 1, this.i && (this.C[0] = r, this.k(0, this.i >> 1)), e;
    }
    top() {
      return this.C[0];
    }
    find(e) {
      return this.C.indexOf(e) >= 0;
    }
    remove(e) {
      let r = this.C.indexOf(e);
      return r < 0 ? false : (r === 0 ? this.pop() : r === this.i - 1 ? (this.C.pop(), this.i -= 1) : (this.C.splice(r, 1, this.C.pop()), this.i -= 1, this.m(r), this.k(r, this.i >> 1)), true);
    }
    updateItem(e) {
      let r = this.C.indexOf(e);
      return r < 0 ? false : (this.m(r), this.k(r, this.i >> 1), true);
    }
    toArray() {
      return [...this.C];
    }
  }, qv = ea;
  Ln.default = qv;
});
var Un = O((qn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(qn, "t", { value: true });
  qn.default = void 0;
  var Uv = st(), ta = class extends Uv.Container {
  }, Nv = ta;
  qn.default = Nv;
});
var ot = O((ra) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(ra, "t", { value: true });
  ra.throwIteratorAccessError = Dv;
  function Dv() {
    throw new RangeError("Iterator access denied!");
  }
});
var na = O((Dn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Dn, "t", { value: true });
  Dn.RandomIterator = void 0;
  var jv = st(), Nn = ot(), ia = class extends jv.ContainerIterator {
    constructor(e, r) {
      super(r), this.o = e, this.iteratorType === 0 ? (this.pre = function() {
        return this.o === 0 && (0, Nn.throwIteratorAccessError)(), this.o -= 1, this;
      }, this.next = function() {
        return this.o === this.container.size() && (0, Nn.throwIteratorAccessError)(), this.o += 1, this;
      }) : (this.pre = function() {
        return this.o === this.container.size() - 1 && (0, Nn.throwIteratorAccessError)(), this.o += 1, this;
      }, this.next = function() {
        return this.o === -1 && (0, Nn.throwIteratorAccessError)(), this.o -= 1, this;
      });
    }
    get pointer() {
      return this.container.getElementByPos(this.o);
    }
    set pointer(e) {
      this.container.setElementByPos(this.o, e);
    }
  };
  Dn.RandomIterator = ia;
});
var Xd = O((jn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(jn, "t", { value: true });
  jn.default = void 0;
  var Fv = $v(Un()), Wv = na();
  function $v(t) {
    return t && t.t ? t : { default: t };
  }
  var fr = class t extends Wv.RandomIterator {
    constructor(e, r, i) {
      super(e, i), this.container = r;
    }
    copy() {
      return new t(this.o, this.container, this.iteratorType);
    }
  }, sa = class extends Fv.default {
    constructor(e = [], r = true) {
      if (super(), Array.isArray(e)) this.J = r ? [...e] : e, this.i = e.length;
      else {
        this.J = [];
        let i = this;
        e.forEach(function(n) {
          i.pushBack(n);
        });
      }
    }
    clear() {
      this.i = 0, this.J.length = 0;
    }
    begin() {
      return new fr(0, this);
    }
    end() {
      return new fr(this.i, this);
    }
    rBegin() {
      return new fr(this.i - 1, this, 1);
    }
    rEnd() {
      return new fr(-1, this, 1);
    }
    front() {
      return this.J[0];
    }
    back() {
      return this.J[this.i - 1];
    }
    getElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      return this.J[e];
    }
    eraseElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      return this.J.splice(e, 1), this.i -= 1, this.i;
    }
    eraseElementByValue(e) {
      let r = 0;
      for (let i = 0; i < this.i; ++i) this.J[i] !== e && (this.J[r++] = this.J[i]);
      return this.i = this.J.length = r, this.i;
    }
    eraseElementByIterator(e) {
      let r = e.o;
      return e = e.next(), this.eraseElementByPos(r), e;
    }
    pushBack(e) {
      return this.J.push(e), this.i += 1, this.i;
    }
    popBack() {
      if (this.i !== 0) return this.i -= 1, this.J.pop();
    }
    setElementByPos(e, r) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      this.J[e] = r;
    }
    insert(e, r, i = 1) {
      if (e < 0 || e > this.i) throw new RangeError();
      return this.J.splice(e, 0, ...new Array(i).fill(r)), this.i += i, this.i;
    }
    find(e) {
      for (let r = 0; r < this.i; ++r) if (this.J[r] === e) return new fr(r, this);
      return this.end();
    }
    reverse() {
      this.J.reverse();
    }
    unique() {
      let e = 1;
      for (let r = 1; r < this.i; ++r) this.J[r] !== this.J[r - 1] && (this.J[e++] = this.J[r]);
      return this.i = this.J.length = e, this.i;
    }
    sort(e) {
      this.J.sort(e);
    }
    forEach(e) {
      for (let r = 0; r < this.i; ++r) e(this.J[r], r, this);
    }
    [Symbol.iterator]() {
      return (function* () {
        yield* this.J;
      }).bind(this)();
    }
  }, Hv = sa;
  jn.default = Hv;
});
var Zd = O((Fn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Fn, "t", { value: true });
  Fn.default = void 0;
  var Vv = Kv(Un()), zv = st(), hr = ot();
  function Kv(t) {
    return t && t.t ? t : { default: t };
  }
  var dr = class t extends zv.ContainerIterator {
    constructor(e, r, i, n) {
      super(n), this.o = e, this.h = r, this.container = i, this.iteratorType === 0 ? (this.pre = function() {
        return this.o.L === this.h && (0, hr.throwIteratorAccessError)(), this.o = this.o.L, this;
      }, this.next = function() {
        return this.o === this.h && (0, hr.throwIteratorAccessError)(), this.o = this.o.B, this;
      }) : (this.pre = function() {
        return this.o.B === this.h && (0, hr.throwIteratorAccessError)(), this.o = this.o.B, this;
      }, this.next = function() {
        return this.o === this.h && (0, hr.throwIteratorAccessError)(), this.o = this.o.L, this;
      });
    }
    get pointer() {
      return this.o === this.h && (0, hr.throwIteratorAccessError)(), this.o.l;
    }
    set pointer(e) {
      this.o === this.h && (0, hr.throwIteratorAccessError)(), this.o.l = e;
    }
    copy() {
      return new t(this.o, this.h, this.container, this.iteratorType);
    }
  }, oa = class extends Vv.default {
    constructor(e = []) {
      super(), this.h = {}, this.p = this._ = this.h.L = this.h.B = this.h;
      let r = this;
      e.forEach(function(i) {
        r.pushBack(i);
      });
    }
    V(e) {
      let { L: r, B: i } = e;
      r.B = i, i.L = r, e === this.p && (this.p = i), e === this._ && (this._ = r), this.i -= 1;
    }
    G(e, r) {
      let i = r.B, n = { l: e, L: r, B: i };
      r.B = n, i.L = n, r === this.h && (this.p = n), i === this.h && (this._ = n), this.i += 1;
    }
    clear() {
      this.i = 0, this.p = this._ = this.h.L = this.h.B = this.h;
    }
    begin() {
      return new dr(this.p, this.h, this);
    }
    end() {
      return new dr(this.h, this.h, this);
    }
    rBegin() {
      return new dr(this._, this.h, this, 1);
    }
    rEnd() {
      return new dr(this.h, this.h, this, 1);
    }
    front() {
      return this.p.l;
    }
    back() {
      return this._.l;
    }
    getElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let r = this.p;
      for (; e--; ) r = r.B;
      return r.l;
    }
    eraseElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let r = this.p;
      for (; e--; ) r = r.B;
      return this.V(r), this.i;
    }
    eraseElementByValue(e) {
      let r = this.p;
      for (; r !== this.h; ) r.l === e && this.V(r), r = r.B;
      return this.i;
    }
    eraseElementByIterator(e) {
      let r = e.o;
      return r === this.h && (0, hr.throwIteratorAccessError)(), e = e.next(), this.V(r), e;
    }
    pushBack(e) {
      return this.G(e, this._), this.i;
    }
    popBack() {
      if (this.i === 0) return;
      let e = this._.l;
      return this.V(this._), e;
    }
    pushFront(e) {
      return this.G(e, this.h), this.i;
    }
    popFront() {
      if (this.i === 0) return;
      let e = this.p.l;
      return this.V(this.p), e;
    }
    setElementByPos(e, r) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let i = this.p;
      for (; e--; ) i = i.B;
      i.l = r;
    }
    insert(e, r, i = 1) {
      if (e < 0 || e > this.i) throw new RangeError();
      if (i <= 0) return this.i;
      if (e === 0) for (; i--; ) this.pushFront(r);
      else if (e === this.i) for (; i--; ) this.pushBack(r);
      else {
        let n = this.p;
        for (let s = 1; s < e; ++s) n = n.B;
        let o = n.B;
        for (this.i += i; i--; ) n.B = { l: r, L: n }, n.B.L = n, n = n.B;
        n.B = o, o.L = n;
      }
      return this.i;
    }
    find(e) {
      let r = this.p;
      for (; r !== this.h; ) {
        if (r.l === e) return new dr(r, this.h, this);
        r = r.B;
      }
      return this.end();
    }
    reverse() {
      if (this.i <= 1) return;
      let e = this.p, r = this._, i = 0;
      for (; i << 1 < this.i; ) {
        let n = e.l;
        e.l = r.l, r.l = n, e = e.B, r = r.L, i += 1;
      }
    }
    unique() {
      if (this.i <= 1) return this.i;
      let e = this.p;
      for (; e !== this.h; ) {
        let r = e;
        for (; r.B !== this.h && r.l === r.B.l; ) r = r.B, this.i -= 1;
        e.B = r.B, e.B.L = e, e = e.B;
      }
      return this.i;
    }
    sort(e) {
      if (this.i <= 1) return;
      let r = [];
      this.forEach(function(n) {
        r.push(n);
      }), r.sort(e);
      let i = this.p;
      r.forEach(function(n) {
        i.l = n, i = i.B;
      });
    }
    merge(e) {
      let r = this;
      if (this.i === 0) e.forEach(function(i) {
        r.pushBack(i);
      });
      else {
        let i = this.p;
        e.forEach(function(n) {
          for (; i !== r.h && i.l <= n; ) i = i.B;
          r.G(n, i.L);
        });
      }
      return this.i;
    }
    forEach(e) {
      let r = this.p, i = 0;
      for (; r !== this.h; ) e(r.l, i++, this), r = r.B;
    }
    [Symbol.iterator]() {
      return (function* () {
        if (this.i === 0) return;
        let e = this.p;
        for (; e !== this.h; ) yield e.l, e = e.B;
      }).bind(this)();
    }
  }, Qv = oa;
  Fn.default = Qv;
});
var ep = O((Wn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Wn, "t", { value: true });
  Wn.default = void 0;
  var Gv = Jv(Un()), Yv = na();
  function Jv(t) {
    return t && t.t ? t : { default: t };
  }
  var pr = class t extends Yv.RandomIterator {
    constructor(e, r, i) {
      super(e, i), this.container = r;
    }
    copy() {
      return new t(this.o, this.container, this.iteratorType);
    }
  }, aa = class extends Gv.default {
    constructor(e = [], r = 4096) {
      super(), this.j = 0, this.D = 0, this.R = 0, this.N = 0, this.P = 0, this.A = [];
      let i = (() => {
        if (typeof e.length == "number") return e.length;
        if (typeof e.size == "number") return e.size;
        if (typeof e.size == "function") return e.size();
        throw new TypeError("Cannot get the length or size of the container");
      })();
      this.F = r, this.P = Math.max(Math.ceil(i / this.F), 1);
      for (let s = 0; s < this.P; ++s) this.A.push(new Array(this.F));
      let n = Math.ceil(i / this.F);
      this.j = this.R = (this.P >> 1) - (n >> 1), this.D = this.N = this.F - i % this.F >> 1;
      let o = this;
      e.forEach(function(s) {
        o.pushBack(s);
      });
    }
    T() {
      let e = [], r = Math.max(this.P >> 1, 1);
      for (let i = 0; i < r; ++i) e[i] = new Array(this.F);
      for (let i = this.j; i < this.P; ++i) e[e.length] = this.A[i];
      for (let i = 0; i < this.R; ++i) e[e.length] = this.A[i];
      e[e.length] = [...this.A[this.R]], this.j = r, this.R = e.length - 1;
      for (let i = 0; i < r; ++i) e[e.length] = new Array(this.F);
      this.A = e, this.P = e.length;
    }
    O(e) {
      let r = this.D + e + 1, i = r % this.F, n = i - 1, o = this.j + (r - i) / this.F;
      return i === 0 && (o -= 1), o %= this.P, n < 0 && (n += this.F), { curNodeBucketIndex: o, curNodePointerIndex: n };
    }
    clear() {
      this.A = [new Array(this.F)], this.P = 1, this.j = this.R = this.i = 0, this.D = this.N = this.F >> 1;
    }
    begin() {
      return new pr(0, this);
    }
    end() {
      return new pr(this.i, this);
    }
    rBegin() {
      return new pr(this.i - 1, this, 1);
    }
    rEnd() {
      return new pr(-1, this, 1);
    }
    front() {
      if (this.i !== 0) return this.A[this.j][this.D];
    }
    back() {
      if (this.i !== 0) return this.A[this.R][this.N];
    }
    pushBack(e) {
      return this.i && (this.N < this.F - 1 ? this.N += 1 : this.R < this.P - 1 ? (this.R += 1, this.N = 0) : (this.R = 0, this.N = 0), this.R === this.j && this.N === this.D && this.T()), this.i += 1, this.A[this.R][this.N] = e, this.i;
    }
    popBack() {
      if (this.i === 0) return;
      let e = this.A[this.R][this.N];
      return this.i !== 1 && (this.N > 0 ? this.N -= 1 : this.R > 0 ? (this.R -= 1, this.N = this.F - 1) : (this.R = this.P - 1, this.N = this.F - 1)), this.i -= 1, e;
    }
    pushFront(e) {
      return this.i && (this.D > 0 ? this.D -= 1 : this.j > 0 ? (this.j -= 1, this.D = this.F - 1) : (this.j = this.P - 1, this.D = this.F - 1), this.j === this.R && this.D === this.N && this.T()), this.i += 1, this.A[this.j][this.D] = e, this.i;
    }
    popFront() {
      if (this.i === 0) return;
      let e = this.A[this.j][this.D];
      return this.i !== 1 && (this.D < this.F - 1 ? this.D += 1 : this.j < this.P - 1 ? (this.j += 1, this.D = 0) : (this.j = 0, this.D = 0)), this.i -= 1, e;
    }
    getElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let { curNodeBucketIndex: r, curNodePointerIndex: i } = this.O(e);
      return this.A[r][i];
    }
    setElementByPos(e, r) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let { curNodeBucketIndex: i, curNodePointerIndex: n } = this.O(e);
      this.A[i][n] = r;
    }
    insert(e, r, i = 1) {
      if (e < 0 || e > this.i) throw new RangeError();
      if (e === 0) for (; i--; ) this.pushFront(r);
      else if (e === this.i) for (; i--; ) this.pushBack(r);
      else {
        let n = [];
        for (let o = e; o < this.i; ++o) n.push(this.getElementByPos(o));
        this.cut(e - 1);
        for (let o = 0; o < i; ++o) this.pushBack(r);
        for (let o = 0; o < n.length; ++o) this.pushBack(n[o]);
      }
      return this.i;
    }
    cut(e) {
      if (e < 0) return this.clear(), 0;
      let { curNodeBucketIndex: r, curNodePointerIndex: i } = this.O(e);
      return this.R = r, this.N = i, this.i = e + 1, this.i;
    }
    eraseElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      if (e === 0) this.popFront();
      else if (e === this.i - 1) this.popBack();
      else {
        let r = [];
        for (let n = e + 1; n < this.i; ++n) r.push(this.getElementByPos(n));
        this.cut(e), this.popBack();
        let i = this;
        r.forEach(function(n) {
          i.pushBack(n);
        });
      }
      return this.i;
    }
    eraseElementByValue(e) {
      if (this.i === 0) return 0;
      let r = [];
      for (let n = 0; n < this.i; ++n) {
        let o = this.getElementByPos(n);
        o !== e && r.push(o);
      }
      let i = r.length;
      for (let n = 0; n < i; ++n) this.setElementByPos(n, r[n]);
      return this.cut(i - 1);
    }
    eraseElementByIterator(e) {
      let r = e.o;
      return this.eraseElementByPos(r), e = e.next(), e;
    }
    find(e) {
      for (let r = 0; r < this.i; ++r) if (this.getElementByPos(r) === e) return new pr(r, this);
      return this.end();
    }
    reverse() {
      let e = 0, r = this.i - 1;
      for (; e < r; ) {
        let i = this.getElementByPos(e);
        this.setElementByPos(e, this.getElementByPos(r)), this.setElementByPos(r, i), e += 1, r -= 1;
      }
    }
    unique() {
      if (this.i <= 1) return this.i;
      let e = 1, r = this.getElementByPos(0);
      for (let i = 1; i < this.i; ++i) {
        let n = this.getElementByPos(i);
        n !== r && (r = n, this.setElementByPos(e++, n));
      }
      for (; this.i > e; ) this.popBack();
      return this.i;
    }
    sort(e) {
      let r = [];
      for (let i = 0; i < this.i; ++i) r.push(this.getElementByPos(i));
      r.sort(e);
      for (let i = 0; i < this.i; ++i) this.setElementByPos(i, r[i]);
    }
    shrinkToFit() {
      if (this.i === 0) return;
      let e = [];
      this.forEach(function(r) {
        e.push(r);
      }), this.P = Math.max(Math.ceil(this.i / this.F), 1), this.i = this.j = this.R = this.D = this.N = 0, this.A = [];
      for (let r = 0; r < this.P; ++r) this.A.push(new Array(this.F));
      for (let r = 0; r < e.length; ++r) this.pushBack(e[r]);
    }
    forEach(e) {
      for (let r = 0; r < this.i; ++r) e(this.getElementByPos(r), r, this);
    }
    [Symbol.iterator]() {
      return (function* () {
        for (let e = 0; e < this.i; ++e) yield this.getElementByPos(e);
      }).bind(this)();
    }
  }, Xv = aa;
  Wn.default = Xv;
});
var tp = O((Qr) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Qr, "t", { value: true });
  Qr.TreeNodeEnableIndex = Qr.TreeNode = void 0;
  var $n = class {
    constructor(e, r) {
      this.ee = 1, this.u = void 0, this.l = void 0, this.U = void 0, this.W = void 0, this.tt = void 0, this.u = e, this.l = r;
    }
    L() {
      let e = this;
      if (e.ee === 1 && e.tt.tt === e) e = e.W;
      else if (e.U) for (e = e.U; e.W; ) e = e.W;
      else {
        let r = e.tt;
        for (; r.U === e; ) e = r, r = e.tt;
        e = r;
      }
      return e;
    }
    B() {
      let e = this;
      if (e.W) {
        for (e = e.W; e.U; ) e = e.U;
        return e;
      } else {
        let r = e.tt;
        for (; r.W === e; ) e = r, r = e.tt;
        return e.W !== r ? r : e;
      }
    }
    te() {
      let e = this.tt, r = this.W, i = r.U;
      return e.tt === this ? e.tt = r : e.U === this ? e.U = r : e.W = r, r.tt = e, r.U = this, this.tt = r, this.W = i, i && (i.tt = this), r;
    }
    se() {
      let e = this.tt, r = this.U, i = r.W;
      return e.tt === this ? e.tt = r : e.U === this ? e.U = r : e.W = r, r.tt = e, r.W = this, this.tt = r, this.U = i, i && (i.tt = this), r;
    }
  };
  Qr.TreeNode = $n;
  var la = class extends $n {
    constructor() {
      super(...arguments), this.rt = 1;
    }
    te() {
      let e = super.te();
      return this.ie(), e.ie(), e;
    }
    se() {
      let e = super.se();
      return this.ie(), e.ie(), e;
    }
    ie() {
      this.rt = 1, this.U && (this.rt += this.U.rt), this.W && (this.rt += this.W.rt);
    }
  };
  Qr.TreeNodeEnableIndex = la;
});
var ca = O((Hn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Hn, "t", { value: true });
  Hn.default = void 0;
  var rp = tp(), Zv = st(), ip = ot(), ua = class extends Zv.Container {
    constructor(e = function(i, n) {
      return i < n ? -1 : i > n ? 1 : 0;
    }, r = false) {
      super(), this.Y = void 0, this.v = e, r ? (this.re = rp.TreeNodeEnableIndex, this.M = function(i, n, o) {
        let s = this.ne(i, n, o);
        if (s) {
          let a = s.tt;
          for (; a !== this.h; ) a.rt += 1, a = a.tt;
          let u = this.he(s);
          if (u) {
            let { parentNode: f, grandParent: d, curNode: h } = u;
            f.ie(), d.ie(), h.ie();
          }
        }
        return this.i;
      }, this.V = function(i) {
        let n = this.fe(i);
        for (; n !== this.h; ) n.rt -= 1, n = n.tt;
      }) : (this.re = rp.TreeNode, this.M = function(i, n, o) {
        let s = this.ne(i, n, o);
        return s && this.he(s), this.i;
      }, this.V = this.fe), this.h = new this.re();
    }
    X(e, r) {
      let i = this.h;
      for (; e; ) {
        let n = this.v(e.u, r);
        if (n < 0) e = e.W;
        else if (n > 0) i = e, e = e.U;
        else return e;
      }
      return i;
    }
    Z(e, r) {
      let i = this.h;
      for (; e; ) this.v(e.u, r) <= 0 ? e = e.W : (i = e, e = e.U);
      return i;
    }
    $(e, r) {
      let i = this.h;
      for (; e; ) {
        let n = this.v(e.u, r);
        if (n < 0) i = e, e = e.W;
        else if (n > 0) e = e.U;
        else return e;
      }
      return i;
    }
    rr(e, r) {
      let i = this.h;
      for (; e; ) this.v(e.u, r) < 0 ? (i = e, e = e.W) : e = e.U;
      return i;
    }
    ue(e) {
      for (; ; ) {
        let r = e.tt;
        if (r === this.h) return;
        if (e.ee === 1) {
          e.ee = 0;
          return;
        }
        if (e === r.U) {
          let i = r.W;
          if (i.ee === 1) i.ee = 0, r.ee = 1, r === this.Y ? this.Y = r.te() : r.te();
          else if (i.W && i.W.ee === 1) {
            i.ee = r.ee, r.ee = 0, i.W.ee = 0, r === this.Y ? this.Y = r.te() : r.te();
            return;
          } else i.U && i.U.ee === 1 ? (i.ee = 1, i.U.ee = 0, i.se()) : (i.ee = 1, e = r);
        } else {
          let i = r.U;
          if (i.ee === 1) i.ee = 0, r.ee = 1, r === this.Y ? this.Y = r.se() : r.se();
          else if (i.U && i.U.ee === 1) {
            i.ee = r.ee, r.ee = 0, i.U.ee = 0, r === this.Y ? this.Y = r.se() : r.se();
            return;
          } else i.W && i.W.ee === 1 ? (i.ee = 1, i.W.ee = 0, i.te()) : (i.ee = 1, e = r);
        }
      }
    }
    fe(e) {
      if (this.i === 1) return this.clear(), this.h;
      let r = e;
      for (; r.U || r.W; ) {
        if (r.W) for (r = r.W; r.U; ) r = r.U;
        else r = r.U;
        [e.u, r.u] = [r.u, e.u], [e.l, r.l] = [r.l, e.l], e = r;
      }
      this.h.U === r ? this.h.U = r.tt : this.h.W === r && (this.h.W = r.tt), this.ue(r);
      let i = r.tt;
      return r === i.U ? i.U = void 0 : i.W = void 0, this.i -= 1, this.Y.ee = 0, i;
    }
    oe(e, r) {
      return e === void 0 ? false : this.oe(e.U, r) || r(e) ? true : this.oe(e.W, r);
    }
    he(e) {
      for (; ; ) {
        let r = e.tt;
        if (r.ee === 0) return;
        let i = r.tt;
        if (r === i.U) {
          let n = i.W;
          if (n && n.ee === 1) {
            if (n.ee = r.ee = 0, i === this.Y) return;
            i.ee = 1, e = i;
            continue;
          } else if (e === r.W) {
            if (e.ee = 0, e.U && (e.U.tt = r), e.W && (e.W.tt = i), r.W = e.U, i.U = e.W, e.U = r, e.W = i, i === this.Y) this.Y = e, this.h.tt = e;
            else {
              let o = i.tt;
              o.U === i ? o.U = e : o.W = e;
            }
            return e.tt = i.tt, r.tt = e, i.tt = e, i.ee = 1, { parentNode: r, grandParent: i, curNode: e };
          } else r.ee = 0, i === this.Y ? this.Y = i.se() : i.se(), i.ee = 1;
        } else {
          let n = i.U;
          if (n && n.ee === 1) {
            if (n.ee = r.ee = 0, i === this.Y) return;
            i.ee = 1, e = i;
            continue;
          } else if (e === r.U) {
            if (e.ee = 0, e.U && (e.U.tt = i), e.W && (e.W.tt = r), i.W = e.U, r.U = e.W, e.U = i, e.W = r, i === this.Y) this.Y = e, this.h.tt = e;
            else {
              let o = i.tt;
              o.U === i ? o.U = e : o.W = e;
            }
            return e.tt = i.tt, r.tt = e, i.tt = e, i.ee = 1, { parentNode: r, grandParent: i, curNode: e };
          } else r.ee = 0, i === this.Y ? this.Y = i.te() : i.te(), i.ee = 1;
        }
        return;
      }
    }
    ne(e, r, i) {
      if (this.Y === void 0) {
        this.i += 1, this.Y = new this.re(e, r), this.Y.ee = 0, this.Y.tt = this.h, this.h.tt = this.Y, this.h.U = this.Y, this.h.W = this.Y;
        return;
      }
      let n, o = this.h.U, s = this.v(o.u, e);
      if (s === 0) {
        o.l = r;
        return;
      } else if (s > 0) o.U = new this.re(e, r), o.U.tt = o, n = o.U, this.h.U = n;
      else {
        let a = this.h.W, u = this.v(a.u, e);
        if (u === 0) {
          a.l = r;
          return;
        } else if (u < 0) a.W = new this.re(e, r), a.W.tt = a, n = a.W, this.h.W = n;
        else {
          if (i !== void 0) {
            let f = i.o;
            if (f !== this.h) {
              let d = this.v(f.u, e);
              if (d === 0) {
                f.l = r;
                return;
              } else if (d > 0) {
                let h = f.L(), g = this.v(h.u, e);
                if (g === 0) {
                  h.l = r;
                  return;
                } else g < 0 && (n = new this.re(e, r), h.W === void 0 ? (h.W = n, n.tt = h) : (f.U = n, n.tt = f));
              }
            }
          }
          if (n === void 0) for (n = this.Y; ; ) {
            let f = this.v(n.u, e);
            if (f > 0) {
              if (n.U === void 0) {
                n.U = new this.re(e, r), n.U.tt = n, n = n.U;
                break;
              }
              n = n.U;
            } else if (f < 0) {
              if (n.W === void 0) {
                n.W = new this.re(e, r), n.W.tt = n, n = n.W;
                break;
              }
              n = n.W;
            } else {
              n.l = r;
              return;
            }
          }
        }
      }
      return this.i += 1, n;
    }
    I(e, r) {
      for (; e; ) {
        let i = this.v(e.u, r);
        if (i < 0) e = e.W;
        else if (i > 0) e = e.U;
        else return e;
      }
      return e || this.h;
    }
    clear() {
      this.i = 0, this.Y = void 0, this.h.tt = void 0, this.h.U = this.h.W = void 0;
    }
    updateKeyByIterator(e, r) {
      let i = e.o;
      if (i === this.h && (0, ip.throwIteratorAccessError)(), this.i === 1) return i.u = r, true;
      if (i === this.h.U) return this.v(i.B().u, r) > 0 ? (i.u = r, true) : false;
      if (i === this.h.W) return this.v(i.L().u, r) < 0 ? (i.u = r, true) : false;
      let n = i.L().u;
      if (this.v(n, r) >= 0) return false;
      let o = i.B().u;
      return this.v(o, r) <= 0 ? false : (i.u = r, true);
    }
    eraseElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let r = 0, i = this;
      return this.oe(this.Y, function(n) {
        return e === r ? (i.V(n), true) : (r += 1, false);
      }), this.i;
    }
    eraseElementByKey(e) {
      if (this.i === 0) return false;
      let r = this.I(this.Y, e);
      return r === this.h ? false : (this.V(r), true);
    }
    eraseElementByIterator(e) {
      let r = e.o;
      r === this.h && (0, ip.throwIteratorAccessError)();
      let i = r.W === void 0;
      return e.iteratorType === 0 ? i && e.next() : (!i || r.U === void 0) && e.next(), this.V(r), e;
    }
    forEach(e) {
      let r = 0;
      for (let i of this) e(i, r++, this);
    }
    getElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let r, i = 0;
      for (let n of this) {
        if (i === e) {
          r = n;
          break;
        }
        i += 1;
      }
      return r;
    }
    getHeight() {
      if (this.i === 0) return 0;
      let e = function(r) {
        return r ? Math.max(e(r.U), e(r.W)) + 1 : 0;
      };
      return e(this.Y);
    }
  }, eE = ua;
  Hn.default = eE;
});
var ha = O((zn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(zn, "t", { value: true });
  zn.default = void 0;
  var tE = st(), Vn = ot(), fa = class extends tE.ContainerIterator {
    constructor(e, r, i) {
      super(i), this.o = e, this.h = r, this.iteratorType === 0 ? (this.pre = function() {
        return this.o === this.h.U && (0, Vn.throwIteratorAccessError)(), this.o = this.o.L(), this;
      }, this.next = function() {
        return this.o === this.h && (0, Vn.throwIteratorAccessError)(), this.o = this.o.B(), this;
      }) : (this.pre = function() {
        return this.o === this.h.W && (0, Vn.throwIteratorAccessError)(), this.o = this.o.B(), this;
      }, this.next = function() {
        return this.o === this.h && (0, Vn.throwIteratorAccessError)(), this.o = this.o.L(), this;
      });
    }
    get index() {
      let e = this.o, r = this.h.tt;
      if (e === this.h) return r ? r.rt - 1 : 0;
      let i = 0;
      for (e.U && (i += e.U.rt); e !== r; ) {
        let n = e.tt;
        e === n.W && (i += 1, n.U && (i += n.U.rt)), e = n;
      }
      return i;
    }
  }, rE = fa;
  zn.default = rE;
});
var sp = O((Kn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Kn, "t", { value: true });
  Kn.default = void 0;
  var iE = np(ca()), nE = np(ha()), sE = ot();
  function np(t) {
    return t && t.t ? t : { default: t };
  }
  var ze = class t extends nE.default {
    constructor(e, r, i, n) {
      super(e, r, n), this.container = i;
    }
    get pointer() {
      return this.o === this.h && (0, sE.throwIteratorAccessError)(), this.o.u;
    }
    copy() {
      return new t(this.o, this.h, this.container, this.iteratorType);
    }
  }, da = class extends iE.default {
    constructor(e = [], r, i) {
      super(r, i);
      let n = this;
      e.forEach(function(o) {
        n.insert(o);
      });
    }
    *K(e) {
      e !== void 0 && (yield* this.K(e.U), yield e.u, yield* this.K(e.W));
    }
    begin() {
      return new ze(this.h.U || this.h, this.h, this);
    }
    end() {
      return new ze(this.h, this.h, this);
    }
    rBegin() {
      return new ze(this.h.W || this.h, this.h, this, 1);
    }
    rEnd() {
      return new ze(this.h, this.h, this, 1);
    }
    front() {
      return this.h.U ? this.h.U.u : void 0;
    }
    back() {
      return this.h.W ? this.h.W.u : void 0;
    }
    insert(e, r) {
      return this.M(e, void 0, r);
    }
    find(e) {
      let r = this.I(this.Y, e);
      return new ze(r, this.h, this);
    }
    lowerBound(e) {
      let r = this.X(this.Y, e);
      return new ze(r, this.h, this);
    }
    upperBound(e) {
      let r = this.Z(this.Y, e);
      return new ze(r, this.h, this);
    }
    reverseLowerBound(e) {
      let r = this.$(this.Y, e);
      return new ze(r, this.h, this);
    }
    reverseUpperBound(e) {
      let r = this.rr(this.Y, e);
      return new ze(r, this.h, this);
    }
    union(e) {
      let r = this;
      return e.forEach(function(i) {
        r.insert(i);
      }), this.i;
    }
    [Symbol.iterator]() {
      return this.K(this.Y);
    }
  }, oE = da;
  Kn.default = oE;
});
var ap = O((Qn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Qn, "t", { value: true });
  Qn.default = void 0;
  var aE = op(ca()), lE = op(ha()), uE = ot();
  function op(t) {
    return t && t.t ? t : { default: t };
  }
  var Ke = class t extends lE.default {
    constructor(e, r, i, n) {
      super(e, r, n), this.container = i;
    }
    get pointer() {
      this.o === this.h && (0, uE.throwIteratorAccessError)();
      let e = this;
      return new Proxy([], { get(r, i) {
        if (i === "0") return e.o.u;
        if (i === "1") return e.o.l;
      }, set(r, i, n) {
        if (i !== "1") throw new TypeError("props must be 1");
        return e.o.l = n, true;
      } });
    }
    copy() {
      return new t(this.o, this.h, this.container, this.iteratorType);
    }
  }, pa = class extends aE.default {
    constructor(e = [], r, i) {
      super(r, i);
      let n = this;
      e.forEach(function(o) {
        n.setElement(o[0], o[1]);
      });
    }
    *K(e) {
      e !== void 0 && (yield* this.K(e.U), yield [e.u, e.l], yield* this.K(e.W));
    }
    begin() {
      return new Ke(this.h.U || this.h, this.h, this);
    }
    end() {
      return new Ke(this.h, this.h, this);
    }
    rBegin() {
      return new Ke(this.h.W || this.h, this.h, this, 1);
    }
    rEnd() {
      return new Ke(this.h, this.h, this, 1);
    }
    front() {
      if (this.i === 0) return;
      let e = this.h.U;
      return [e.u, e.l];
    }
    back() {
      if (this.i === 0) return;
      let e = this.h.W;
      return [e.u, e.l];
    }
    lowerBound(e) {
      let r = this.X(this.Y, e);
      return new Ke(r, this.h, this);
    }
    upperBound(e) {
      let r = this.Z(this.Y, e);
      return new Ke(r, this.h, this);
    }
    reverseLowerBound(e) {
      let r = this.$(this.Y, e);
      return new Ke(r, this.h, this);
    }
    reverseUpperBound(e) {
      let r = this.rr(this.Y, e);
      return new Ke(r, this.h, this);
    }
    setElement(e, r, i) {
      return this.M(e, r, i);
    }
    find(e) {
      let r = this.I(this.Y, e);
      return new Ke(r, this.h, this);
    }
    getElementByKey(e) {
      return this.I(this.Y, e).l;
    }
    union(e) {
      let r = this;
      return e.forEach(function(i) {
        r.setElement(i[0], i[1]);
      }), this.i;
    }
    [Symbol.iterator]() {
      return this.K(this.Y);
    }
  }, cE = pa;
  Qn.default = cE;
});
var ya = O((ga) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(ga, "t", { value: true });
  ga.default = fE;
  function fE(t) {
    let e = typeof t;
    return e === "object" && t !== null || e === "function";
  }
});
var ma = O((Gr) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Gr, "t", { value: true });
  Gr.HashContainerIterator = Gr.HashContainer = void 0;
  var lp = st(), ba = hE(ya()), Si = ot();
  function hE(t) {
    return t && t.t ? t : { default: t };
  }
  var wa = class extends lp.ContainerIterator {
    constructor(e, r, i) {
      super(i), this.o = e, this.h = r, this.iteratorType === 0 ? (this.pre = function() {
        return this.o.L === this.h && (0, Si.throwIteratorAccessError)(), this.o = this.o.L, this;
      }, this.next = function() {
        return this.o === this.h && (0, Si.throwIteratorAccessError)(), this.o = this.o.B, this;
      }) : (this.pre = function() {
        return this.o.B === this.h && (0, Si.throwIteratorAccessError)(), this.o = this.o.B, this;
      }, this.next = function() {
        return this.o === this.h && (0, Si.throwIteratorAccessError)(), this.o = this.o.L, this;
      });
    }
  };
  Gr.HashContainerIterator = wa;
  var _a = class extends lp.Container {
    constructor() {
      super(), this.H = [], this.g = {}, this.HASH_TAG = Symbol("@@HASH_TAG"), Object.setPrototypeOf(this.g, null), this.h = {}, this.h.L = this.h.B = this.p = this._ = this.h;
    }
    V(e) {
      let { L: r, B: i } = e;
      r.B = i, i.L = r, e === this.p && (this.p = i), e === this._ && (this._ = r), this.i -= 1;
    }
    M(e, r, i) {
      i === void 0 && (i = (0, ba.default)(e));
      let n;
      if (i) {
        let o = e[this.HASH_TAG];
        if (o !== void 0) return this.H[o].l = r, this.i;
        Object.defineProperty(e, this.HASH_TAG, { value: this.H.length, configurable: true }), n = { u: e, l: r, L: this._, B: this.h }, this.H.push(n);
      } else {
        let o = this.g[e];
        if (o) return o.l = r, this.i;
        n = { u: e, l: r, L: this._, B: this.h }, this.g[e] = n;
      }
      return this.i === 0 ? (this.p = n, this.h.B = n) : this._.B = n, this._ = n, this.h.L = n, ++this.i;
    }
    I(e, r) {
      if (r === void 0 && (r = (0, ba.default)(e)), r) {
        let i = e[this.HASH_TAG];
        return i === void 0 ? this.h : this.H[i];
      } else return this.g[e] || this.h;
    }
    clear() {
      let e = this.HASH_TAG;
      this.H.forEach(function(r) {
        delete r.u[e];
      }), this.H = [], this.g = {}, Object.setPrototypeOf(this.g, null), this.i = 0, this.p = this._ = this.h.L = this.h.B = this.h;
    }
    eraseElementByKey(e, r) {
      let i;
      if (r === void 0 && (r = (0, ba.default)(e)), r) {
        let n = e[this.HASH_TAG];
        if (n === void 0) return false;
        delete e[this.HASH_TAG], i = this.H[n], delete this.H[n];
      } else {
        if (i = this.g[e], i === void 0) return false;
        delete this.g[e];
      }
      return this.V(i), true;
    }
    eraseElementByIterator(e) {
      let r = e.o;
      return r === this.h && (0, Si.throwIteratorAccessError)(), this.V(r), e.next();
    }
    eraseElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let r = this.p;
      for (; e--; ) r = r.B;
      return this.V(r), this.i;
    }
  };
  Gr.HashContainer = _a;
});
var cp = O((Gn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Gn, "t", { value: true });
  Gn.default = void 0;
  var up = ma(), dE = ot(), gr = class t extends up.HashContainerIterator {
    constructor(e, r, i, n) {
      super(e, r, n), this.container = i;
    }
    get pointer() {
      return this.o === this.h && (0, dE.throwIteratorAccessError)(), this.o.u;
    }
    copy() {
      return new t(this.o, this.h, this.container, this.iteratorType);
    }
  }, va = class extends up.HashContainer {
    constructor(e = []) {
      super();
      let r = this;
      e.forEach(function(i) {
        r.insert(i);
      });
    }
    begin() {
      return new gr(this.p, this.h, this);
    }
    end() {
      return new gr(this.h, this.h, this);
    }
    rBegin() {
      return new gr(this._, this.h, this, 1);
    }
    rEnd() {
      return new gr(this.h, this.h, this, 1);
    }
    front() {
      return this.p.u;
    }
    back() {
      return this._.u;
    }
    insert(e, r) {
      return this.M(e, void 0, r);
    }
    getElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let r = this.p;
      for (; e--; ) r = r.B;
      return r.u;
    }
    find(e, r) {
      let i = this.I(e, r);
      return new gr(i, this.h, this);
    }
    forEach(e) {
      let r = 0, i = this.p;
      for (; i !== this.h; ) e(i.u, r++, this), i = i.B;
    }
    [Symbol.iterator]() {
      return (function* () {
        let e = this.p;
        for (; e !== this.h; ) yield e.u, e = e.B;
      }).bind(this)();
    }
  }, pE = va;
  Gn.default = pE;
});
var hp = O((Yn) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Yn, "t", { value: true });
  Yn.default = void 0;
  var fp = ma(), gE = bE(ya()), yE = ot();
  function bE(t) {
    return t && t.t ? t : { default: t };
  }
  var yr = class t extends fp.HashContainerIterator {
    constructor(e, r, i, n) {
      super(e, r, n), this.container = i;
    }
    get pointer() {
      this.o === this.h && (0, yE.throwIteratorAccessError)();
      let e = this;
      return new Proxy([], { get(r, i) {
        if (i === "0") return e.o.u;
        if (i === "1") return e.o.l;
      }, set(r, i, n) {
        if (i !== "1") throw new TypeError("props must be 1");
        return e.o.l = n, true;
      } });
    }
    copy() {
      return new t(this.o, this.h, this.container, this.iteratorType);
    }
  }, Ea = class extends fp.HashContainer {
    constructor(e = []) {
      super();
      let r = this;
      e.forEach(function(i) {
        r.setElement(i[0], i[1]);
      });
    }
    begin() {
      return new yr(this.p, this.h, this);
    }
    end() {
      return new yr(this.h, this.h, this);
    }
    rBegin() {
      return new yr(this._, this.h, this, 1);
    }
    rEnd() {
      return new yr(this.h, this.h, this, 1);
    }
    front() {
      if (this.i !== 0) return [this.p.u, this.p.l];
    }
    back() {
      if (this.i !== 0) return [this._.u, this._.l];
    }
    setElement(e, r, i) {
      return this.M(e, r, i);
    }
    getElementByKey(e, r) {
      if (r === void 0 && (r = (0, gE.default)(e)), r) {
        let n = e[this.HASH_TAG];
        return n !== void 0 ? this.H[n].l : void 0;
      }
      let i = this.g[e];
      return i ? i.l : void 0;
    }
    getElementByPos(e) {
      if (e < 0 || e > this.i - 1) throw new RangeError();
      let r = this.p;
      for (; e--; ) r = r.B;
      return [r.u, r.l];
    }
    find(e, r) {
      let i = this.I(e, r);
      return new yr(i, this.h, this);
    }
    forEach(e) {
      let r = 0, i = this.p;
      for (; i !== this.h; ) e([i.u, i.l], r++, this), i = i.B;
    }
    [Symbol.iterator]() {
      return (function* () {
        let e = this.p;
        for (; e !== this.h; ) yield [e.u, e.l], e = e.B;
      }).bind(this)();
    }
  }, wE = Ea;
  Yn.default = wE;
});
var dp = O((Ne) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ne, "t", { value: true });
  Object.defineProperty(Ne, "Deque", { enumerable: true, get: function() {
    return AE.default;
  } });
  Object.defineProperty(Ne, "HashMap", { enumerable: true, get: function() {
    return CE.default;
  } });
  Object.defineProperty(Ne, "HashSet", { enumerable: true, get: function() {
    return RE.default;
  } });
  Object.defineProperty(Ne, "LinkList", { enumerable: true, get: function() {
    return SE.default;
  } });
  Object.defineProperty(Ne, "OrderedMap", { enumerable: true, get: function() {
    return TE.default;
  } });
  Object.defineProperty(Ne, "OrderedSet", { enumerable: true, get: function() {
    return IE.default;
  } });
  Object.defineProperty(Ne, "PriorityQueue", { enumerable: true, get: function() {
    return vE.default;
  } });
  Object.defineProperty(Ne, "Queue", { enumerable: true, get: function() {
    return mE.default;
  } });
  Object.defineProperty(Ne, "Stack", { enumerable: true, get: function() {
    return _E.default;
  } });
  Object.defineProperty(Ne, "Vector", { enumerable: true, get: function() {
    return EE.default;
  } });
  var _E = at(Gd()), mE = at(Yd()), vE = at(Jd()), EE = at(Xd()), SE = at(Zd()), AE = at(ep()), IE = at(sp()), TE = at(ap()), RE = at(cp()), CE = at(hp());
  function at(t) {
    return t && t.t ? t : { default: t };
  }
});
var gp = O((AU, pp) => {
  "use strict";
  _();
  v();
  m();
  var kE = dp().OrderedSet, lt = nt()("number-allocator:trace"), PE = nt()("number-allocator:error");
  function Re(t, e) {
    this.low = t, this.high = e;
  }
  Re.prototype.equals = function(t) {
    return this.low === t.low && this.high === t.high;
  };
  Re.prototype.compare = function(t) {
    return this.low < t.low && this.high < t.low ? -1 : t.low < this.low && t.high < this.low ? 1 : 0;
  };
  function ut(t, e) {
    if (!(this instanceof ut)) return new ut(t, e);
    this.min = t, this.max = e, this.ss = new kE([], (r, i) => r.compare(i)), lt("Create"), this.clear();
  }
  ut.prototype.firstVacant = function() {
    return this.ss.size() === 0 ? null : this.ss.front().low;
  };
  ut.prototype.alloc = function() {
    if (this.ss.size() === 0) return lt("alloc():empty"), null;
    let t = this.ss.begin(), e = t.pointer.low, r = t.pointer.high, i = e;
    return i + 1 <= r ? this.ss.updateKeyByIterator(t, new Re(e + 1, r)) : this.ss.eraseElementByPos(0), lt("alloc():" + i), i;
  };
  ut.prototype.use = function(t) {
    let e = new Re(t, t), r = this.ss.lowerBound(e);
    if (!r.equals(this.ss.end())) {
      let i = r.pointer.low, n = r.pointer.high;
      return r.pointer.equals(e) ? (this.ss.eraseElementByIterator(r), lt("use():" + t), true) : i > t ? false : i === t ? (this.ss.updateKeyByIterator(r, new Re(i + 1, n)), lt("use():" + t), true) : n === t ? (this.ss.updateKeyByIterator(r, new Re(i, n - 1)), lt("use():" + t), true) : (this.ss.updateKeyByIterator(r, new Re(t + 1, n)), this.ss.insert(new Re(i, t - 1)), lt("use():" + t), true);
    }
    return lt("use():failed"), false;
  };
  ut.prototype.free = function(t) {
    if (t < this.min || t > this.max) {
      PE("free():" + t + " is out of range");
      return;
    }
    let e = new Re(t, t), r = this.ss.upperBound(e);
    if (r.equals(this.ss.end())) {
      if (r.equals(this.ss.begin())) {
        this.ss.insert(e);
        return;
      }
      r.pre();
      let i = r.pointer.high;
      r.pointer.high + 1 === t ? this.ss.updateKeyByIterator(r, new Re(i, t)) : this.ss.insert(e);
    } else if (r.equals(this.ss.begin())) if (t + 1 === r.pointer.low) {
      let i = r.pointer.high;
      this.ss.updateKeyByIterator(r, new Re(t, i));
    } else this.ss.insert(e);
    else {
      let i = r.pointer.low, n = r.pointer.high;
      r.pre();
      let o = r.pointer.low;
      r.pointer.high + 1 === t ? t + 1 === i ? (this.ss.eraseElementByIterator(r), this.ss.updateKeyByIterator(r, new Re(o, n))) : this.ss.updateKeyByIterator(r, new Re(o, t)) : t + 1 === i ? (this.ss.eraseElementByIterator(r.next()), this.ss.insert(new Re(t, n))) : this.ss.insert(e);
    }
    lt("free():" + t);
  };
  ut.prototype.clear = function() {
    lt("clear()"), this.ss.clear(), this.ss.insert(new Re(this.min, this.max));
  };
  ut.prototype.intervalCount = function() {
    return this.ss.size();
  };
  ut.prototype.dump = function() {
    console.log("length:" + this.ss.size());
    for (let t of this.ss) console.log(t);
  };
  pp.exports = ut;
});
var Sa = O((MU, yp) => {
  _();
  v();
  m();
  var BE = gp();
  yp.exports.NumberAllocator = BE;
});
var bp = O((Ia) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ia, "__esModule", { value: true });
  var xE = Qd(), OE = Sa(), Aa = class {
    constructor(e) {
      __publicField(this, "aliasToTopic");
      __publicField(this, "topicToAlias");
      __publicField(this, "max");
      __publicField(this, "numberAllocator");
      __publicField(this, "length");
      e > 0 && (this.aliasToTopic = new xE.LRUCache({ max: e }), this.topicToAlias = {}, this.numberAllocator = new OE.NumberAllocator(1, e), this.max = e, this.length = 0);
    }
    put(e, r) {
      if (r === 0 || r > this.max) return false;
      let i = this.aliasToTopic.get(r);
      return i && delete this.topicToAlias[i], this.aliasToTopic.set(r, e), this.topicToAlias[e] = r, this.numberAllocator.use(r), this.length = this.aliasToTopic.size, true;
    }
    getTopicByAlias(e) {
      return this.aliasToTopic.get(e);
    }
    getAliasByTopic(e) {
      let r = this.topicToAlias[e];
      return typeof r < "u" && this.aliasToTopic.get(r), r;
    }
    clear() {
      this.aliasToTopic.clear(), this.topicToAlias = {}, this.numberAllocator.clear(), this.length = 0;
    }
    getLruAlias() {
      let e = this.numberAllocator.firstVacant();
      return e || [...this.aliasToTopic.keys()][this.aliasToTopic.size - 1];
    }
  };
  Ia.default = Aa;
});
var wp = O((Ai) => {
  "use strict";
  _();
  v();
  m();
  var ME = Ai && Ai.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Ai, "__esModule", { value: true });
  var LE = Ei(), qE = ME(bp()), UE = cr(), NE = (t, e) => {
    t.log("_handleConnack");
    let { options: r } = t, n = r.protocolVersion === 5 ? e.reasonCode : e.returnCode;
    if (clearTimeout(t.connackTimer), delete t.topicAliasSend, e.properties) {
      if (e.properties.topicAliasMaximum) {
        if (e.properties.topicAliasMaximum > 65535) {
          t.emit("error", new Error("topicAliasMaximum from broker is out of range"));
          return;
        }
        e.properties.topicAliasMaximum > 0 && (t.topicAliasSend = new qE.default(e.properties.topicAliasMaximum));
      }
      e.properties.serverKeepAlive && r.keepalive && (r.keepalive = e.properties.serverKeepAlive), e.properties.maximumPacketSize && (r.properties || (r.properties = {}), r.properties.maximumPacketSize = e.properties.maximumPacketSize);
    }
    if (n === 0) t.reconnecting = false, t._onConnect(e);
    else if (n > 0) {
      let o = new UE.ErrorWithReasonCode(`Connection refused: ${LE.ReasonCodes[n]}`, n);
      t.emit("error", o), t.options.reconnectOnConnackError && t._cleanUp(true);
    }
  };
  Ai.default = NE;
});
var _p = O((Ta) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ta, "__esModule", { value: true });
  var DE = (t, e, r) => {
    t.log("handling pubrel packet");
    let i = typeof r < "u" ? r : t.noop, { messageId: n } = e, o = { cmd: "pubcomp", messageId: n };
    t.incomingStore.get(e, (s, a) => {
      s ? t._sendPacket(o, i) : (t.emit("message", a.topic, a.payload, a), t.handleMessage(a, (u) => {
        if (u) return i(u);
        t.incomingStore.del(a, t.noop), t._sendPacket(o, i);
      }));
    });
  };
  Ta.default = DE;
});
var mp = O((Ii) => {
  "use strict";
  _();
  v();
  m();
  var Ti = Ii && Ii.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Ii, "__esModule", { value: true });
  var jE = Ti(Dd()), FE = Ti($d()), WE = Ti(wp()), $E = Ti(Ei()), HE = Ti(_p()), VE = (t, e, r) => {
    let { options: i } = t;
    if (i.protocolVersion === 5 && i.properties && i.properties.maximumPacketSize && i.properties.maximumPacketSize < e.length) return t.emit("error", new Error(`exceeding packets size ${e.cmd}`)), t.end({ reasonCode: 149, properties: { reasonString: "Maximum packet size was exceeded" } }), t;
    switch (t.log("_handlePacket :: emitting packetreceive"), t.emit("packetreceive", e), e.cmd) {
      case "publish":
        (0, jE.default)(t, e, r);
        break;
      case "puback":
      case "pubrec":
      case "pubcomp":
      case "suback":
      case "unsuback":
        t.reschedulePing(), (0, $E.default)(t, e), r();
        break;
      case "pubrel":
        t.reschedulePing(), (0, HE.default)(t, e, r);
        break;
      case "connack":
        (0, WE.default)(t, e), r();
        break;
      case "auth":
        t.reschedulePing(), (0, FE.default)(t, e), r();
        break;
      case "pingresp":
        t.log("_handlePacket :: received pingresp"), t.reschedulePing(true), r();
        break;
      case "disconnect":
        t.emit("disconnect", e), r();
        break;
      default:
        t.log("_handlePacket :: unknown command"), r();
        break;
    }
  };
  Ii.default = VE;
});
var ka = O((Ca) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ca, "__esModule", { value: true });
  var Ra = class {
    constructor() {
      __publicField(this, "nextId");
      this.nextId = Math.max(1, Math.floor(Math.random() * 65535));
    }
    allocate() {
      let e = this.nextId++;
      return this.nextId === 65536 && (this.nextId = 1), e;
    }
    getLastAllocated() {
      return this.nextId === 1 ? 65535 : this.nextId - 1;
    }
    register(e) {
      return true;
    }
    deallocate(e) {
    }
    clear() {
    }
  };
  Ca.default = Ra;
});
var vp = O((Ba) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Ba, "__esModule", { value: true });
  var Pa = class {
    constructor(e) {
      __publicField(this, "aliasToTopic");
      __publicField(this, "max");
      __publicField(this, "length");
      this.aliasToTopic = {}, this.max = e;
    }
    put(e, r) {
      return r === 0 || r > this.max ? false : (this.aliasToTopic[r] = e, this.length = Object.keys(this.aliasToTopic).length, true);
    }
    getTopicByAlias(e) {
      return this.aliasToTopic[e];
    }
    clear() {
      this.aliasToTopic = {};
    }
  };
  Ba.default = Pa;
});
var Ep = O((Yr) => {
  "use strict";
  _();
  v();
  m();
  var zE = Yr && Yr.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Yr, "__esModule", { value: true });
  Yr.TypedEventEmitter = void 0;
  var KE = zE((Ot(), G(xt))), QE = cr(), Jn = class {
  };
  Yr.TypedEventEmitter = Jn;
  (0, QE.applyMixin)(Jn, KE.default);
});
var Ap = O((Xn, Sp) => {
  _();
  v();
  m();
  (function(t, e) {
    typeof Xn == "object" && typeof Sp < "u" ? e(Xn) : typeof define == "function" && define.amd ? define(["exports"], e) : (t = typeof globalThis < "u" ? globalThis : t || self, e(t.fastUniqueNumbers = {}));
  })(Xn, function(t) {
    "use strict";
    var e = function(g) {
      return function(y) {
        var E = g(y);
        return y.add(E), E;
      };
    }, r = function(g) {
      return function(y, E) {
        return g.set(y, E), E;
      };
    }, i = Number.MAX_SAFE_INTEGER === void 0 ? 9007199254740991 : Number.MAX_SAFE_INTEGER, n = 536870912, o = n * 2, s = function(g, y) {
      return function(E) {
        var w = y.get(E), S = w === void 0 ? E.size : w < o ? w + 1 : 0;
        if (!E.has(S)) return g(E, S);
        if (E.size < n) {
          for (; E.has(S); ) S = Math.floor(Math.random() * o);
          return g(E, S);
        }
        if (E.size > i) throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");
        for (; E.has(S); ) S = Math.floor(Math.random() * i);
        return g(E, S);
      };
    }, a = /* @__PURE__ */ new WeakMap(), u = r(a), f = s(u, a), d = e(f);
    t.addUniqueNumber = d, t.generateUniqueNumber = f;
  });
});
var Tp = O((Zn, Ip) => {
  _();
  v();
  m();
  (function(t, e) {
    typeof Zn == "object" && typeof Ip < "u" ? e(Zn, Ap()) : typeof define == "function" && define.amd ? define(["exports", "fast-unique-numbers"], e) : (t = typeof globalThis < "u" ? globalThis : t || self, e(t.workerTimersBroker = {}, t.fastUniqueNumbers));
  })(Zn, function(t, e) {
    "use strict";
    var r = function(s) {
      return s.method !== void 0 && s.method === "call";
    }, i = function(s) {
      return s.error === null && typeof s.id == "number";
    }, n = function(s) {
      var a = /* @__PURE__ */ new Map([[0, function() {
      }]]), u = /* @__PURE__ */ new Map([[0, function() {
      }]]), f = /* @__PURE__ */ new Map(), d = new Worker(s);
      d.addEventListener("message", function(w) {
        var S = w.data;
        if (r(S)) {
          var I = S.params, C = I.timerId, k = I.timerType;
          if (k === "interval") {
            var M = a.get(C);
            if (typeof M == "number") {
              var q = f.get(M);
              if (q === void 0 || q.timerId !== C || q.timerType !== k) throw new Error("The timer is in an undefined state.");
            } else if (typeof M < "u") M();
            else throw new Error("The timer is in an undefined state.");
          } else if (k === "timeout") {
            var z = u.get(C);
            if (typeof z == "number") {
              var j = f.get(z);
              if (j === void 0 || j.timerId !== C || j.timerType !== k) throw new Error("The timer is in an undefined state.");
            } else if (typeof z < "u") z(), u.delete(C);
            else throw new Error("The timer is in an undefined state.");
          }
        } else if (i(S)) {
          var Q = S.id, $ = f.get(Q);
          if ($ === void 0) throw new Error("The timer is in an undefined state.");
          var te = $.timerId, pt = $.timerType;
          f.delete(Q), pt === "interval" ? a.delete(te) : u.delete(te);
        } else {
          var Fe = S.error.message;
          throw new Error(Fe);
        }
      });
      var h = function(S) {
        var I = e.generateUniqueNumber(f);
        f.set(I, { timerId: S, timerType: "interval" }), a.set(S, I), d.postMessage({ id: I, method: "clear", params: { timerId: S, timerType: "interval" } });
      }, g = function(S) {
        var I = e.generateUniqueNumber(f);
        f.set(I, { timerId: S, timerType: "timeout" }), u.set(S, I), d.postMessage({ id: I, method: "clear", params: { timerId: S, timerType: "timeout" } });
      }, y = function(S) {
        var I = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, C = e.generateUniqueNumber(a);
        return a.set(C, function() {
          S(), typeof a.get(C) == "function" && d.postMessage({ id: null, method: "set", params: { delay: I, now: performance.now(), timerId: C, timerType: "interval" } });
        }), d.postMessage({ id: null, method: "set", params: { delay: I, now: performance.now(), timerId: C, timerType: "interval" } }), C;
      }, E = function(S) {
        var I = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, C = e.generateUniqueNumber(u);
        return u.set(C, S), d.postMessage({ id: null, method: "set", params: { delay: I, now: performance.now(), timerId: C, timerType: "timeout" } }), C;
      };
      return { clearInterval: h, clearTimeout: g, setInterval: y, setTimeout: E };
    };
    t.load = n;
  });
});
var Cp = O((es, Rp) => {
  _();
  v();
  m();
  (function(t, e) {
    typeof es == "object" && typeof Rp < "u" ? e(es, Tp()) : typeof define == "function" && define.amd ? define(["exports", "worker-timers-broker"], e) : (t = typeof globalThis < "u" ? globalThis : t || self, e(t.workerTimers = {}, t.workerTimersBroker));
  })(es, function(t, e) {
    "use strict";
    var r = function(d, h) {
      var g = null;
      return function() {
        if (g !== null) return g;
        var y = new Blob([h], { type: "application/javascript; charset=utf-8" }), E = URL.createObjectURL(y);
        return g = d(E), setTimeout(function() {
          return URL.revokeObjectURL(E);
        }), g;
      };
    }, i = `(()=>{var e={472:(e,t,r)=>{var o,i;void 0===(i="function"==typeof(o=function(){"use strict";var e=new Map,t=new Map,r=function(t){var r=e.get(t);if(void 0===r)throw new Error('There is no interval scheduled with the given id "'.concat(t,'".'));clearTimeout(r),e.delete(t)},o=function(e){var r=t.get(e);if(void 0===r)throw new Error('There is no timeout scheduled with the given id "'.concat(e,'".'));clearTimeout(r),t.delete(e)},i=function(e,t){var r,o=performance.now();return{expected:o+(r=e-Math.max(0,o-t)),remainingDelay:r}},n=function e(t,r,o,i){var n=performance.now();n>o?postMessage({id:null,method:"call",params:{timerId:r,timerType:i}}):t.set(r,setTimeout(e,o-n,t,r,o,i))},a=function(t,r,o){var a=i(t,o),s=a.expected,d=a.remainingDelay;e.set(r,setTimeout(n,d,e,r,s,"interval"))},s=function(e,r,o){var a=i(e,o),s=a.expected,d=a.remainingDelay;t.set(r,setTimeout(n,d,t,r,s,"timeout"))};addEventListener("message",(function(e){var t=e.data;try{if("clear"===t.method){var i=t.id,n=t.params,d=n.timerId,c=n.timerType;if("interval"===c)r(d),postMessage({error:null,id:i});else{if("timeout"!==c)throw new Error('The given type "'.concat(c,'" is not supported'));o(d),postMessage({error:null,id:i})}}else{if("set"!==t.method)throw new Error('The given method "'.concat(t.method,'" is not supported'));var u=t.params,l=u.delay,p=u.now,m=u.timerId,v=u.timerType;if("interval"===v)a(l,m,p);else{if("timeout"!==v)throw new Error('The given type "'.concat(v,'" is not supported'));s(l,m,p)}}}catch(e){postMessage({error:{message:e.message},id:t.id,result:null})}}))})?o.call(t,r,t,e):o)||(e.exports=i)}},t={};function r(o){var i=t[o];if(void 0!==i)return i.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,r),n.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";r(472)})()})();`, n = r(e.load, i), o = function(d) {
      return n().clearInterval(d);
    }, s = function(d) {
      return n().clearTimeout(d);
    }, a = function() {
      var d;
      return (d = n()).setInterval.apply(d, arguments);
    }, u = function() {
      var d;
      return (d = n()).setTimeout.apply(d, arguments);
    };
    t.clearInterval = o, t.clearTimeout = s, t.setInterval = a, t.setTimeout = u;
  });
});
var Ri = O((br) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(br, "__esModule", { value: true });
  br.isReactNativeBrowser = br.isWebWorker = void 0;
  var GE = () => {
    var _a;
    return typeof window < "u" ? typeof navigator < "u" && ((_a = navigator.userAgent) == null ? void 0 : _a.toLowerCase().indexOf(" electron/")) > -1 && (R == null ? void 0 : R.versions) ? !Object.prototype.hasOwnProperty.call(R.versions, "electron") : typeof window.document < "u" : false;
  }, kp = () => {
    var _a, _b;
    return !!(typeof self == "object" && ((_b = (_a = self == null ? void 0 : self.constructor) == null ? void 0 : _a.name) == null ? void 0 : _b.includes("WorkerGlobalScope")));
  }, Pp = () => typeof navigator < "u" && navigator.product === "ReactNative", YE = GE() || kp() || Pp();
  br.isWebWorker = kp();
  br.isReactNativeBrowser = Pp();
  br.default = YE;
});
var Mp = O((St) => {
  "use strict";
  _();
  v();
  m();
  var JE = St && St.__createBinding || (Object.create ? function(t, e, r, i) {
    i === void 0 && (i = r);
    var n = Object.getOwnPropertyDescriptor(e, r);
    (!n || ("get" in n ? !e.__esModule : n.writable || n.configurable)) && (n = { enumerable: true, get: function() {
      return e[r];
    } }), Object.defineProperty(t, i, n);
  } : function(t, e, r, i) {
    i === void 0 && (i = r), t[i] = e[r];
  }), XE = St && St.__setModuleDefault || (Object.create ? function(t, e) {
    Object.defineProperty(t, "default", { enumerable: true, value: e });
  } : function(t, e) {
    t.default = e;
  }), ZE = St && St.__importStar || /* @__PURE__ */ function() {
    var t = function(e) {
      return t = Object.getOwnPropertyNames || function(r) {
        var i = [];
        for (var n in r) Object.prototype.hasOwnProperty.call(r, n) && (i[i.length] = n);
        return i;
      }, t(e);
    };
    return function(e) {
      if (e && e.__esModule) return e;
      var r = {};
      if (e != null) for (var i = t(e), n = 0; n < i.length; n++) i[n] !== "default" && JE(r, e, i[n]);
      return XE(r, e), r;
    };
  }();
  Object.defineProperty(St, "__esModule", { value: true });
  var Bp = Cp(), xa = ZE(Ri()), xp = { set: Bp.setInterval, clear: Bp.clearInterval }, Op = { set: (t, e) => setInterval(t, e), clear: (t) => clearInterval(t) }, e1 = (t) => {
    switch (t) {
      case "native":
        return Op;
      case "worker":
        return xp;
      case "auto":
      default:
        return xa.default && !xa.isWebWorker && !xa.isReactNativeBrowser ? xp : Op;
    }
  };
  St.default = e1;
});
var Ma = O((Ci) => {
  "use strict";
  _();
  v();
  m();
  var t1 = Ci && Ci.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Ci, "__esModule", { value: true });
  var r1 = t1(Mp()), Oa = class {
    constructor(e, r) {
      __publicField(this, "_keepalive");
      __publicField(this, "timerId");
      __publicField(this, "timer");
      __publicField(this, "destroyed", false);
      __publicField(this, "counter");
      __publicField(this, "client");
      __publicField(this, "_keepaliveTimeoutTimestamp");
      __publicField(this, "_intervalEvery");
      this.client = e, this.timer = typeof r == "object" && "set" in r && "clear" in r ? r : (0, r1.default)(r), this.setKeepalive(e.options.keepalive);
    }
    get keepaliveTimeoutTimestamp() {
      return this._keepaliveTimeoutTimestamp;
    }
    get intervalEvery() {
      return this._intervalEvery;
    }
    get keepalive() {
      return this._keepalive;
    }
    clear() {
      this.timerId && (this.timer.clear(this.timerId), this.timerId = null);
    }
    setKeepalive(e) {
      if (e *= 1e3, isNaN(e) || e <= 0 || e > 2147483647) throw new Error(`Keepalive value must be an integer between 0 and 2147483647. Provided value is ${e}`);
      this._keepalive = e, this.reschedule(), this.client.log(`KeepaliveManager: set keepalive to ${e}ms`);
    }
    destroy() {
      this.clear(), this.destroyed = true;
    }
    reschedule() {
      if (this.destroyed) return;
      this.clear(), this.counter = 0;
      let e = Math.ceil(this._keepalive * 1.5);
      this._keepaliveTimeoutTimestamp = Date.now() + e, this._intervalEvery = Math.ceil(this._keepalive / 2), this.timerId = this.timer.set(() => {
        this.destroyed || (this.counter += 1, this.counter === 2 ? this.client.sendPing() : this.counter > 2 && this.client.onKeepaliveTimeout());
      }, this._intervalEvery);
    }
  };
  Ci.default = Oa;
});
var ts = O((Qe) => {
  "use strict";
  var _a;
  _();
  v();
  m();
  var i1 = Qe && Qe.__createBinding || (Object.create ? function(t, e, r, i) {
    i === void 0 && (i = r);
    var n = Object.getOwnPropertyDescriptor(e, r);
    (!n || ("get" in n ? !e.__esModule : n.writable || n.configurable)) && (n = { enumerable: true, get: function() {
      return e[r];
    } }), Object.defineProperty(t, i, n);
  } : function(t, e, r, i) {
    i === void 0 && (i = r), t[i] = e[r];
  }), n1 = Qe && Qe.__setModuleDefault || (Object.create ? function(t, e) {
    Object.defineProperty(t, "default", { enumerable: true, value: e });
  } : function(t, e) {
    t.default = e;
  }), Dp = Qe && Qe.__importStar || /* @__PURE__ */ function() {
    var t = function(e) {
      return t = Object.getOwnPropertyNames || function(r) {
        var i = [];
        for (var n in r) Object.prototype.hasOwnProperty.call(r, n) && (i[i.length] = n);
        return i;
      }, t(e);
    };
    return function(e) {
      if (e && e.__esModule) return e;
      var r = {};
      if (e != null) for (var i = t(e), n = 0; n < i.length; n++) i[n] !== "default" && i1(r, e, i[n]);
      return n1(r, e), r;
    };
  }(), Ht = Qe && Qe.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Qe, "__esModule", { value: true });
  var La = Ht(Bd()), s1 = Nt(), Lp = Ht(Ld()), o1 = Ht(nt()), qp = Dp(Ud()), Jr = Ht(Wo()), a1 = Ht(mp()), l1 = Ht(ka()), u1 = Ht(vp()), ki = cr(), c1 = Ep(), f1 = Ht(Ma()), Up = Dp(Ri()), qa = globalThis.setImmediate || ((...t) => {
    let e = t.shift();
    (0, ki.nextTick)(() => {
      e(...t);
    });
  }), Np = { keepalive: 60, reschedulePings: true, protocolId: "MQTT", protocolVersion: 4, reconnectPeriod: 1e3, connectTimeout: 30 * 1e3, clean: true, resubscribe: true, subscribeBatchSize: null, writeCache: true, timerVariant: "auto" }, Ua = (_a = class extends c1.TypedEventEmitter {
    constructor(e, r) {
      super();
      __publicField(this, "connected");
      __publicField(this, "disconnecting");
      __publicField(this, "disconnected");
      __publicField(this, "reconnecting");
      __publicField(this, "incomingStore");
      __publicField(this, "outgoingStore");
      __publicField(this, "options");
      __publicField(this, "queueQoSZero");
      __publicField(this, "_reconnectCount");
      __publicField(this, "log");
      __publicField(this, "messageIdProvider");
      __publicField(this, "outgoing");
      __publicField(this, "messageIdToTopic");
      __publicField(this, "noop");
      __publicField(this, "keepaliveManager");
      __publicField(this, "stream");
      __publicField(this, "queue");
      __publicField(this, "streamBuilder");
      __publicField(this, "_resubscribeTopics");
      __publicField(this, "connackTimer");
      __publicField(this, "reconnectTimer");
      __publicField(this, "_storeProcessing");
      __publicField(this, "_packetIdsDuringStoreProcessing");
      __publicField(this, "_storeProcessingQueue");
      __publicField(this, "_firstConnection");
      __publicField(this, "topicAliasRecv");
      __publicField(this, "topicAliasSend");
      __publicField(this, "_deferredReconnect");
      __publicField(this, "connackPacket");
      this.options = r || {};
      for (let i in Np) typeof this.options[i] > "u" ? this.options[i] = Np[i] : this.options[i] = r[i];
      this.log = this.options.log || (0, o1.default)("mqttjs:client"), this.noop = this._noop.bind(this), this.log("MqttClient :: version:", _a.VERSION), Up.isWebWorker ? this.log("MqttClient :: environment", "webworker") : this.log("MqttClient :: environment", Up.default ? "browser" : "node"), this.log("MqttClient :: options.protocol", r.protocol), this.log("MqttClient :: options.protocolVersion", r.protocolVersion), this.log("MqttClient :: options.username", r.username), this.log("MqttClient :: options.keepalive", r.keepalive), this.log("MqttClient :: options.reconnectPeriod", r.reconnectPeriod), this.log("MqttClient :: options.rejectUnauthorized", r.rejectUnauthorized), this.log("MqttClient :: options.properties.topicAliasMaximum", r.properties ? r.properties.topicAliasMaximum : void 0), this.options.clientId = typeof r.clientId == "string" ? r.clientId : _a.defaultId(), this.log("MqttClient :: clientId", this.options.clientId), this.options.customHandleAcks = r.protocolVersion === 5 && r.customHandleAcks ? r.customHandleAcks : (...i) => {
        i[3](null, 0);
      }, this.options.writeCache || (La.default.writeToStream.cacheNumbers = false), this.streamBuilder = e, this.messageIdProvider = typeof this.options.messageIdProvider > "u" ? new l1.default() : this.options.messageIdProvider, this.outgoingStore = r.outgoingStore || new Jr.default(), this.incomingStore = r.incomingStore || new Jr.default(), this.queueQoSZero = r.queueQoSZero === void 0 ? true : r.queueQoSZero, this._resubscribeTopics = {}, this.messageIdToTopic = {}, this.keepaliveManager = null, this.connected = false, this.disconnecting = false, this.reconnecting = false, this.queue = [], this.connackTimer = null, this.reconnectTimer = null, this._storeProcessing = false, this._packetIdsDuringStoreProcessing = {}, this._storeProcessingQueue = [], this.outgoing = {}, this._firstConnection = true, r.properties && r.properties.topicAliasMaximum > 0 && (r.properties.topicAliasMaximum > 65535 ? this.log("MqttClient :: options.properties.topicAliasMaximum is out of range") : this.topicAliasRecv = new u1.default(r.properties.topicAliasMaximum)), this.on("connect", () => {
        let { queue: i } = this, n = () => {
          let o = i.shift();
          this.log("deliver :: entry %o", o);
          let s = null;
          if (!o) {
            this._resubscribe();
            return;
          }
          s = o.packet, this.log("deliver :: call _sendPacket for %o", s);
          let a = true;
          s.messageId && s.messageId !== 0 && (this.messageIdProvider.register(s.messageId) || (a = false)), a ? this._sendPacket(s, (u) => {
            o.cb && o.cb(u), n();
          }) : (this.log("messageId: %d has already used. The message is skipped and removed.", s.messageId), n());
        };
        this.log("connect :: sending queued packets"), n();
      }), this.on("close", () => {
        this.log("close :: connected set to `false`"), this.connected = false, this.log("close :: clearing connackTimer"), clearTimeout(this.connackTimer), this._destroyKeepaliveManager(), this.topicAliasRecv && this.topicAliasRecv.clear(), this.log("close :: calling _setupReconnect"), this._setupReconnect();
      }), this.options.manualConnect || (this.log("MqttClient :: setting up stream"), this.connect());
    }
    static defaultId() {
      return `mqttjs_${Math.random().toString(16).substr(2, 8)}`;
    }
    handleAuth(e, r) {
      r();
    }
    handleMessage(e, r) {
      r();
    }
    _nextId() {
      return this.messageIdProvider.allocate();
    }
    getLastMessageId() {
      return this.messageIdProvider.getLastAllocated();
    }
    connect() {
      var _a2;
      let e = new s1.Writable(), r = La.default.parser(this.options), i = null, n = [];
      this.log("connect :: calling method to clear reconnect"), this._clearReconnect(), this.disconnected && !this.reconnecting && (this.incomingStore = this.options.incomingStore || new Jr.default(), this.outgoingStore = this.options.outgoingStore || new Jr.default(), this.disconnecting = false, this.disconnected = false), this.log("connect :: using streamBuilder provided to client to create stream"), this.stream = this.streamBuilder(this), r.on("packet", (f) => {
        this.log("parser :: on packet push to packets array."), n.push(f);
      });
      let o = () => {
        this.log("work :: getting next packet in queue");
        let f = n.shift();
        if (f) this.log("work :: packet pulled from queue"), (0, a1.default)(this, f, s);
        else {
          this.log("work :: no packets in queue");
          let d = i;
          i = null, this.log("work :: done flag is %s", !!d), d && d();
        }
      }, s = () => {
        if (n.length) (0, ki.nextTick)(o);
        else {
          let f = i;
          i = null, f();
        }
      };
      e._write = (f, d, h) => {
        i = h, this.log("writable stream :: parsing buffer"), r.parse(f), o();
      };
      let a = (f) => {
        this.log("streamErrorHandler :: error", f.message), f.code ? (this.log("streamErrorHandler :: emitting error"), this.emit("error", f)) : this.noop(f);
      };
      this.log("connect :: pipe stream to writable stream"), this.stream.pipe(e), this.stream.on("error", a), this.stream.on("close", () => {
        this.log("(%s)stream :: on close", this.options.clientId), this._flushVolatile(), this.log("stream: emit close to MqttClient"), this.emit("close");
      }), this.log("connect: sending packet `connect`");
      let u = { cmd: "connect", protocolId: this.options.protocolId, protocolVersion: this.options.protocolVersion, clean: this.options.clean, clientId: this.options.clientId, keepalive: this.options.keepalive, username: this.options.username, password: this.options.password, properties: this.options.properties };
      if (this.options.will && (u.will = { ...this.options.will, payload: (_a2 = this.options.will) == null ? void 0 : _a2.payload }), this.topicAliasRecv && (u.properties || (u.properties = {}), this.topicAliasRecv && (u.properties.topicAliasMaximum = this.topicAliasRecv.max)), this._writePacket(u), r.on("error", this.emit.bind(this, "error")), this.options.properties) {
        if (!this.options.properties.authenticationMethod && this.options.properties.authenticationData) return this.end(() => this.emit("error", new Error("Packet has no Authentication Method"))), this;
        if (this.options.properties.authenticationMethod && this.options.authPacket && typeof this.options.authPacket == "object") {
          let f = { cmd: "auth", reasonCode: 0, ...this.options.authPacket };
          this._writePacket(f);
        }
      }
      return this.stream.setMaxListeners(1e3), clearTimeout(this.connackTimer), this.connackTimer = setTimeout(() => {
        this.log("!!connectTimeout hit!! Calling _cleanUp with force `true`"), this.emit("error", new Error("connack timeout")), this._cleanUp(true);
      }, this.options.connectTimeout), this;
    }
    publish(e, r, i, n) {
      this.log("publish :: message `%s` to topic `%s`", r, e);
      let { options: o } = this;
      typeof i == "function" && (n = i, i = null), i = i || {}, i = { ...{ qos: 0, retain: false, dup: false }, ...i };
      let { qos: a, retain: u, dup: f, properties: d, cbStorePut: h } = i;
      if (this._checkDisconnecting(n)) return this;
      let g = () => {
        let y = 0;
        if ((a === 1 || a === 2) && (y = this._nextId(), y === null)) return this.log("No messageId left"), false;
        let E = { cmd: "publish", topic: e, payload: r, qos: a, retain: u, messageId: y, dup: f };
        switch (o.protocolVersion === 5 && (E.properties = d), this.log("publish :: qos", a), a) {
          case 1:
          case 2:
            this.outgoing[E.messageId] = { volatile: false, cb: n || this.noop }, this.log("MqttClient:publish: packet cmd: %s", E.cmd), this._sendPacket(E, void 0, h);
            break;
          default:
            this.log("MqttClient:publish: packet cmd: %s", E.cmd), this._sendPacket(E, n, h);
            break;
        }
        return true;
      };
      return (this._storeProcessing || this._storeProcessingQueue.length > 0 || !g()) && this._storeProcessingQueue.push({ invoke: g, cbStorePut: i.cbStorePut, callback: n }), this;
    }
    publishAsync(e, r, i) {
      return new Promise((n, o) => {
        this.publish(e, r, i, (s, a) => {
          s ? o(s) : n(a);
        });
      });
    }
    subscribe(e, r, i) {
      let n = this.options.protocolVersion;
      typeof r == "function" && (i = r), i = i || this.noop;
      let o = false, s = [];
      typeof e == "string" ? (e = [e], s = e) : Array.isArray(e) ? s = e : typeof e == "object" && (o = e.resubscribe, delete e.resubscribe, s = Object.keys(e));
      let a = qp.validateTopics(s);
      if (a !== null) return qa(i, new Error(`Invalid topic ${a}`)), this;
      if (this._checkDisconnecting(i)) return this.log("subscribe: discconecting true"), this;
      let u = { qos: 0 };
      n === 5 && (u.nl = false, u.rap = false, u.rh = 0), r = { ...u, ...r };
      let { properties: f } = r, d = [], h = (E, w) => {
        if (w = w || r, !Object.prototype.hasOwnProperty.call(this._resubscribeTopics, E) || this._resubscribeTopics[E].qos < w.qos || o) {
          let S = { topic: E, qos: w.qos };
          n === 5 && (S.nl = w.nl, S.rap = w.rap, S.rh = w.rh, S.properties = f), this.log("subscribe: pushing topic `%s` and qos `%s` to subs list", S.topic, S.qos), d.push(S);
        }
      };
      if (Array.isArray(e) ? e.forEach((E) => {
        this.log("subscribe: array topic %s", E), h(E);
      }) : Object.keys(e).forEach((E) => {
        this.log("subscribe: object topic %s, %o", E, e[E]), h(E, e[E]);
      }), !d.length) return i(null, []), this;
      let g = (E, w) => {
        let S = { cmd: "subscribe", subscriptions: E, messageId: w };
        if (f && (S.properties = f), this.options.resubscribe) {
          this.log("subscribe :: resubscribe true");
          let C = [];
          E.forEach((k) => {
            if (this.options.reconnectPeriod > 0) {
              let M = { qos: k.qos };
              n === 5 && (M.nl = k.nl || false, M.rap = k.rap || false, M.rh = k.rh || 0, M.properties = k.properties), this._resubscribeTopics[k.topic] = M, C.push(k.topic);
            }
          }), this.messageIdToTopic[S.messageId] = C;
        }
        let I = new Promise((C, k) => {
          this.outgoing[S.messageId] = { volatile: true, cb(M, q) {
            if (!M) {
              let { granted: z } = q;
              for (let j = 0; j < z.length; j += 1) E[j].qos = z[j];
            }
            M ? k(new ki.ErrorWithSubackPacket(M.message, q)) : C(q);
          } };
        });
        return this.log("subscribe :: call _sendPacket"), this._sendPacket(S), I;
      }, y = () => {
        let E = this.options.subscribeBatchSize ?? d.length, w = [];
        for (let S = 0; S < d.length; S += E) {
          let I = d.slice(S, S + E), C = this._nextId();
          if (C === null) return this.log("No messageId left"), false;
          w.push(g(I, C));
        }
        return Promise.all(w).then((S) => {
          i(null, d, S.at(-1));
        }).catch((S) => {
          i(S, d, S.packet);
        }), true;
      };
      return (this._storeProcessing || this._storeProcessingQueue.length > 0 || !y()) && this._storeProcessingQueue.push({ invoke: y, callback: i }), this;
    }
    subscribeAsync(e, r) {
      return new Promise((i, n) => {
        this.subscribe(e, r, (o, s) => {
          o ? n(o) : i(s);
        });
      });
    }
    unsubscribe(e, r, i) {
      typeof e == "string" && (e = [e]), typeof r == "function" && (i = r), i = i || this.noop;
      let n = qp.validateTopics(e);
      if (n !== null) return qa(i, new Error(`Invalid topic ${n}`)), this;
      if (this._checkDisconnecting(i)) return this;
      let o = () => {
        let s = this._nextId();
        if (s === null) return this.log("No messageId left"), false;
        let a = { cmd: "unsubscribe", messageId: s, unsubscriptions: [] };
        return typeof e == "string" ? a.unsubscriptions = [e] : Array.isArray(e) && (a.unsubscriptions = e), this.options.resubscribe && a.unsubscriptions.forEach((u) => {
          delete this._resubscribeTopics[u];
        }), typeof r == "object" && r.properties && (a.properties = r.properties), this.outgoing[a.messageId] = { volatile: true, cb: i }, this.log("unsubscribe: call _sendPacket"), this._sendPacket(a), true;
      };
      return (this._storeProcessing || this._storeProcessingQueue.length > 0 || !o()) && this._storeProcessingQueue.push({ invoke: o, callback: i }), this;
    }
    unsubscribeAsync(e, r) {
      return new Promise((i, n) => {
        this.unsubscribe(e, r, (o, s) => {
          o ? n(o) : i(s);
        });
      });
    }
    end(e, r, i) {
      this.log("end :: (%s)", this.options.clientId), (e == null || typeof e != "boolean") && (i = i || r, r = e, e = false), typeof r != "object" && (i = i || r, r = null), this.log("end :: cb? %s", !!i), (!i || typeof i != "function") && (i = this.noop);
      let n = () => {
        this.log("end :: closeStores: closing incoming and outgoing stores"), this.disconnected = true, this.incomingStore.close((s) => {
          this.outgoingStore.close((a) => {
            if (this.log("end :: closeStores: emitting end"), this.emit("end"), i) {
              let u = s || a;
              this.log("end :: closeStores: invoking callback with args"), i(u);
            }
          });
        }), this._deferredReconnect ? this._deferredReconnect() : (this.options.reconnectPeriod === 0 || this.options.manualConnect) && (this.disconnecting = false);
      }, o = () => {
        this.log("end :: (%s) :: finish :: calling _cleanUp with force %s", this.options.clientId, e), this._cleanUp(e, () => {
          this.log("end :: finish :: calling process.nextTick on closeStores"), (0, ki.nextTick)(n);
        }, r);
      };
      return this.disconnecting ? (i(), this) : (this._clearReconnect(), this.disconnecting = true, !e && Object.keys(this.outgoing).length > 0 ? (this.log("end :: (%s) :: calling finish in 10ms once outgoing is empty", this.options.clientId), this.once("outgoingEmpty", setTimeout.bind(null, o, 10))) : (this.log("end :: (%s) :: immediately calling finish", this.options.clientId), o()), this);
    }
    endAsync(e, r) {
      return new Promise((i, n) => {
        this.end(e, r, (o) => {
          o ? n(o) : i();
        });
      });
    }
    removeOutgoingMessage(e) {
      if (this.outgoing[e]) {
        let { cb: r } = this.outgoing[e];
        this._removeOutgoingAndStoreMessage(e, () => {
          r(new Error("Message removed"));
        });
      }
      return this;
    }
    reconnect(e) {
      this.log("client reconnect");
      let r = () => {
        e ? (this.options.incomingStore = e.incomingStore, this.options.outgoingStore = e.outgoingStore) : (this.options.incomingStore = null, this.options.outgoingStore = null), this.incomingStore = this.options.incomingStore || new Jr.default(), this.outgoingStore = this.options.outgoingStore || new Jr.default(), this.disconnecting = false, this.disconnected = false, this._deferredReconnect = null, this._reconnect();
      };
      return this.disconnecting && !this.disconnected ? this._deferredReconnect = r : r(), this;
    }
    _flushVolatile() {
      this.outgoing && (this.log("_flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"), Object.keys(this.outgoing).forEach((e) => {
        this.outgoing[e].volatile && typeof this.outgoing[e].cb == "function" && (this.outgoing[e].cb(new Error("Connection closed")), delete this.outgoing[e]);
      }));
    }
    _flush() {
      this.outgoing && (this.log("_flush: queue exists? %b", !!this.outgoing), Object.keys(this.outgoing).forEach((e) => {
        typeof this.outgoing[e].cb == "function" && (this.outgoing[e].cb(new Error("Connection closed")), delete this.outgoing[e]);
      }));
    }
    _removeTopicAliasAndRecoverTopicName(e) {
      let r;
      e.properties && (r = e.properties.topicAlias);
      let i = e.topic.toString();
      if (this.log("_removeTopicAliasAndRecoverTopicName :: alias %d, topic %o", r, i), i.length === 0) {
        if (typeof r > "u") return new Error("Unregistered Topic Alias");
        if (i = this.topicAliasSend.getTopicByAlias(r), typeof i > "u") return new Error("Unregistered Topic Alias");
        e.topic = i;
      }
      r && delete e.properties.topicAlias;
    }
    _checkDisconnecting(e) {
      return this.disconnecting && (e && e !== this.noop ? e(new Error("client disconnecting")) : this.emit("error", new Error("client disconnecting"))), this.disconnecting;
    }
    _reconnect() {
      this.log("_reconnect: emitting reconnect to client"), this.emit("reconnect"), this.connected ? (this.end(() => {
        this.connect();
      }), this.log("client already connected. disconnecting first.")) : (this.log("_reconnect: calling connect"), this.connect());
    }
    _setupReconnect() {
      !this.disconnecting && !this.reconnectTimer && this.options.reconnectPeriod > 0 ? (this.reconnecting || (this.log("_setupReconnect :: emit `offline` state"), this.emit("offline"), this.log("_setupReconnect :: set `reconnecting` to `true`"), this.reconnecting = true), this.log("_setupReconnect :: setting reconnectTimer for %d ms", this.options.reconnectPeriod), this.reconnectTimer = setInterval(() => {
        this.log("reconnectTimer :: reconnect triggered!"), this._reconnect();
      }, this.options.reconnectPeriod)) : this.log("_setupReconnect :: doing nothing...");
    }
    _clearReconnect() {
      this.log("_clearReconnect : clearing reconnect timer"), this.reconnectTimer && (clearInterval(this.reconnectTimer), this.reconnectTimer = null);
    }
    _cleanUp(e, r, i = {}) {
      if (r && (this.log("_cleanUp :: done callback provided for on stream close"), this.stream.on("close", r)), this.log("_cleanUp :: forced? %s", e), e) this.options.reconnectPeriod === 0 && this.options.clean && this._flush(), this.log("_cleanUp :: (%s) :: destroying stream", this.options.clientId), this.stream.destroy();
      else {
        let n = { cmd: "disconnect", ...i };
        this.log("_cleanUp :: (%s) :: call _sendPacket with disconnect packet", this.options.clientId), this._sendPacket(n, () => {
          this.log("_cleanUp :: (%s) :: destroying stream", this.options.clientId), qa(() => {
            this.stream.end(() => {
              this.log("_cleanUp :: (%s) :: stream destroyed", this.options.clientId);
            });
          });
        });
      }
      !this.disconnecting && !this.reconnecting && (this.log("_cleanUp :: client not disconnecting/reconnecting. Clearing and resetting reconnect."), this._clearReconnect(), this._setupReconnect()), this._destroyKeepaliveManager(), r && !this.connected && (this.log("_cleanUp :: (%s) :: removing stream `done` callback `close` listener", this.options.clientId), this.stream.removeListener("close", r), r());
    }
    _storeAndSend(e, r, i) {
      this.log("storeAndSend :: store packet with cmd %s to outgoingStore", e.cmd);
      let n = e, o;
      if (n.cmd === "publish" && (n = (0, Lp.default)(e), o = this._removeTopicAliasAndRecoverTopicName(n), o)) return r && r(o);
      this.outgoingStore.put(n, (s) => {
        if (s) return r && r(s);
        i(), this._writePacket(e, r);
      });
    }
    _applyTopicAlias(e) {
      if (this.options.protocolVersion === 5 && e.cmd === "publish") {
        let r;
        e.properties && (r = e.properties.topicAlias);
        let i = e.topic.toString();
        if (this.topicAliasSend) if (r) {
          if (i.length !== 0 && (this.log("applyTopicAlias :: register topic: %s - alias: %d", i, r), !this.topicAliasSend.put(i, r))) return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d", i, r), new Error("Sending Topic Alias out of range");
        } else i.length !== 0 && (this.options.autoAssignTopicAlias ? (r = this.topicAliasSend.getAliasByTopic(i), r ? (e.topic = "", e.properties = { ...e.properties, topicAlias: r }, this.log("applyTopicAlias :: auto assign(use) topic: %s - alias: %d", i, r)) : (r = this.topicAliasSend.getLruAlias(), this.topicAliasSend.put(i, r), e.properties = { ...e.properties, topicAlias: r }, this.log("applyTopicAlias :: auto assign topic: %s - alias: %d", i, r))) : this.options.autoUseTopicAlias && (r = this.topicAliasSend.getAliasByTopic(i), r && (e.topic = "", e.properties = { ...e.properties, topicAlias: r }, this.log("applyTopicAlias :: auto use topic: %s - alias: %d", i, r))));
        else if (r) return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d", i, r), new Error("Sending Topic Alias out of range");
      }
    }
    _noop(e) {
      this.log("noop ::", e);
    }
    _writePacket(e, r) {
      this.log("_writePacket :: packet: %O", e), this.log("_writePacket :: emitting `packetsend`"), this.emit("packetsend", e), this.log("_writePacket :: writing to stream");
      let i = La.default.writeToStream(e, this.stream, this.options);
      this.log("_writePacket :: writeToStream result %s", i), !i && r && r !== this.noop ? (this.log("_writePacket :: handle events on `drain` once through callback."), this.stream.once("drain", r)) : r && (this.log("_writePacket :: invoking cb"), r());
    }
    _sendPacket(e, r, i, n) {
      this.log("_sendPacket :: (%s) ::  start", this.options.clientId), i = i || this.noop, r = r || this.noop;
      let o = this._applyTopicAlias(e);
      if (o) {
        r(o);
        return;
      }
      if (!this.connected) {
        if (e.cmd === "auth") {
          this._writePacket(e, r);
          return;
        }
        this.log("_sendPacket :: client not connected. Storing packet offline."), this._storePacket(e, r, i);
        return;
      }
      if (n) {
        this._writePacket(e, r);
        return;
      }
      switch (e.cmd) {
        case "publish":
          break;
        case "pubrel":
          this._storeAndSend(e, r, i);
          return;
        default:
          this._writePacket(e, r);
          return;
      }
      switch (e.qos) {
        case 2:
        case 1:
          this._storeAndSend(e, r, i);
          break;
        case 0:
        default:
          this._writePacket(e, r);
          break;
      }
      this.log("_sendPacket :: (%s) ::  end", this.options.clientId);
    }
    _storePacket(e, r, i) {
      this.log("_storePacket :: packet: %o", e), this.log("_storePacket :: cb? %s", !!r), i = i || this.noop;
      let n = e;
      if (n.cmd === "publish") {
        n = (0, Lp.default)(e);
        let s = this._removeTopicAliasAndRecoverTopicName(n);
        if (s) return r && r(s);
      }
      let o = n.qos || 0;
      o === 0 && this.queueQoSZero || n.cmd !== "publish" ? this.queue.push({ packet: n, cb: r }) : o > 0 ? (r = this.outgoing[n.messageId] ? this.outgoing[n.messageId].cb : null, this.outgoingStore.put(n, (s) => {
        if (s) return r && r(s);
        i();
      })) : r && r(new Error("No connection to broker"));
    }
    _setupKeepaliveManager() {
      this.log("_setupKeepaliveManager :: keepalive %d (seconds)", this.options.keepalive), !this.keepaliveManager && this.options.keepalive && (this.keepaliveManager = new f1.default(this, this.options.timerVariant));
    }
    _destroyKeepaliveManager() {
      this.keepaliveManager && (this.log("_destroyKeepaliveManager :: destroying keepalive manager"), this.keepaliveManager.destroy(), this.keepaliveManager = null);
    }
    reschedulePing(e = false) {
      this.keepaliveManager && this.options.keepalive && (e || this.options.reschedulePings) && this._reschedulePing();
    }
    _reschedulePing() {
      this.log("_reschedulePing :: rescheduling ping"), this.keepaliveManager.reschedule();
    }
    sendPing() {
      this.log("_sendPing :: sending pingreq"), this._sendPacket({ cmd: "pingreq" });
    }
    onKeepaliveTimeout() {
      this.emit("error", new Error("Keepalive timeout")), this.log("onKeepaliveTimeout :: calling _cleanUp with force true"), this._cleanUp(true);
    }
    _resubscribe() {
      this.log("_resubscribe");
      let e = Object.keys(this._resubscribeTopics);
      if (!this._firstConnection && (this.options.clean || this.options.protocolVersion >= 4 && !this.connackPacket.sessionPresent) && e.length > 0) if (this.options.resubscribe) if (this.options.protocolVersion === 5) {
        this.log("_resubscribe: protocolVersion 5");
        for (let r = 0; r < e.length; r++) {
          let i = {};
          i[e[r]] = this._resubscribeTopics[e[r]], i.resubscribe = true, this.subscribe(i, { properties: i[e[r]].properties });
        }
      } else this._resubscribeTopics.resubscribe = true, this.subscribe(this._resubscribeTopics);
      else this._resubscribeTopics = {};
      this._firstConnection = false;
    }
    _onConnect(e) {
      if (this.disconnected) {
        this.emit("connect", e);
        return;
      }
      this.connackPacket = e, this.messageIdProvider.clear(), this._setupKeepaliveManager(), this.connected = true;
      let r = () => {
        let i = this.outgoingStore.createStream(), n = () => {
          i.destroy(), i = null, this._flushStoreProcessingQueue(), o();
        }, o = () => {
          this._storeProcessing = false, this._packetIdsDuringStoreProcessing = {};
        };
        this.once("close", n), i.on("error", (a) => {
          o(), this._flushStoreProcessingQueue(), this.removeListener("close", n), this.emit("error", a);
        });
        let s = () => {
          if (!i) return;
          let a = i.read(1), u;
          if (!a) {
            i.once("readable", s);
            return;
          }
          if (this._storeProcessing = true, this._packetIdsDuringStoreProcessing[a.messageId]) {
            s();
            return;
          }
          !this.disconnecting && !this.reconnectTimer ? (u = this.outgoing[a.messageId] ? this.outgoing[a.messageId].cb : null, this.outgoing[a.messageId] = { volatile: false, cb(f, d) {
            u && u(f, d), s();
          } }, this._packetIdsDuringStoreProcessing[a.messageId] = true, this.messageIdProvider.register(a.messageId) ? this._sendPacket(a, void 0, void 0, true) : this.log("messageId: %d has already used.", a.messageId)) : i.destroy && i.destroy();
        };
        i.on("end", () => {
          let a = true;
          for (let u in this._packetIdsDuringStoreProcessing) if (!this._packetIdsDuringStoreProcessing[u]) {
            a = false;
            break;
          }
          this.removeListener("close", n), a ? (o(), this._invokeAllStoreProcessingQueue(), this.emit("connect", e)) : r();
        }), s();
      };
      r();
    }
    _invokeStoreProcessingQueue() {
      if (!this._storeProcessing && this._storeProcessingQueue.length > 0) {
        let e = this._storeProcessingQueue[0];
        if (e && e.invoke()) return this._storeProcessingQueue.shift(), true;
      }
      return false;
    }
    _invokeAllStoreProcessingQueue() {
      for (; this._invokeStoreProcessingQueue(); ) ;
    }
    _flushStoreProcessingQueue() {
      for (let e of this._storeProcessingQueue) e.cbStorePut && e.cbStorePut(new Error("Connection closed")), e.callback && e.callback(new Error("Connection closed"));
      this._storeProcessingQueue.splice(0);
    }
    _removeOutgoingAndStoreMessage(e, r) {
      delete this.outgoing[e], this.outgoingStore.del({ messageId: e }, (i, n) => {
        r(i, n), this.messageIdProvider.deallocate(e), this._invokeStoreProcessingQueue();
      });
    }
  }, __publicField(_a, "VERSION", ki.MQTTJS_VERSION), _a);
  Qe.default = Ua;
});
var jp = O((Da) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(Da, "__esModule", { value: true });
  var h1 = Sa(), Na = class {
    constructor() {
      __publicField(this, "numberAllocator");
      __publicField(this, "lastId");
      this.numberAllocator = new h1.NumberAllocator(1, 65535);
    }
    allocate() {
      return this.lastId = this.numberAllocator.alloc(), this.lastId;
    }
    getLastAllocated() {
      return this.lastId;
    }
    register(e) {
      return this.numberAllocator.use(e);
    }
    deallocate(e) {
      this.numberAllocator.free(e);
    }
    clear() {
      this.numberAllocator.clear();
    }
  };
  Da.default = Na;
});
function wr(t) {
  throw new RangeError(y1[t]);
}
function Fp(t, e) {
  let r = t.split("@"), i = "";
  r.length > 1 && (i = r[0] + "@", t = r[1]);
  let n = function(o, s) {
    let a = [], u = o.length;
    for (; u--; ) a[u] = s(o[u]);
    return a;
  }((t = t.replace(g1, ".")).split("."), e).join(".");
  return i + n;
}
function Vp(t) {
  let e = [], r = 0, i = t.length;
  for (; r < i; ) {
    let n = t.charCodeAt(r++);
    if (n >= 55296 && n <= 56319 && r < i) {
      let o = t.charCodeAt(r++);
      (64512 & o) == 56320 ? e.push(((1023 & n) << 10) + (1023 & o) + 65536) : (e.push(n), r--);
    } else e.push(n);
  }
  return e;
}
var d1;
var p1;
var g1;
var y1;
var ct;
var ja;
var Wp;
var zp;
var $p;
var Hp;
var Vt;
var Kp = Ae(() => {
  _();
  v();
  m();
  d1 = /^xn--/, p1 = /[^\0-\x7E]/, g1 = /[\x2E\u3002\uFF0E\uFF61]/g, y1 = { overflow: "Overflow: input needs wider integers to process", "not-basic": "Illegal input >= 0x80 (not a basic code point)", "invalid-input": "Invalid input" }, ct = Math.floor, ja = String.fromCharCode;
  Wp = function(t, e) {
    return t + 22 + 75 * (t < 26) - ((e != 0) << 5);
  }, zp = function(t, e, r) {
    let i = 0;
    for (t = r ? ct(t / 700) : t >> 1, t += ct(t / e); t > 455; i += 36) t = ct(t / 35);
    return ct(i + 36 * t / (t + 38));
  }, $p = function(t) {
    let e = [], r = t.length, i = 0, n = 128, o = 72, s = t.lastIndexOf("-");
    s < 0 && (s = 0);
    for (let u = 0; u < s; ++u) t.charCodeAt(u) >= 128 && wr("not-basic"), e.push(t.charCodeAt(u));
    for (let u = s > 0 ? s + 1 : 0; u < r; ) {
      let f = i;
      for (let h = 1, g = 36; ; g += 36) {
        u >= r && wr("invalid-input");
        let y = (a = t.charCodeAt(u++)) - 48 < 10 ? a - 22 : a - 65 < 26 ? a - 65 : a - 97 < 26 ? a - 97 : 36;
        (y >= 36 || y > ct((2147483647 - i) / h)) && wr("overflow"), i += y * h;
        let E = g <= o ? 1 : g >= o + 26 ? 26 : g - o;
        if (y < E) break;
        let w = 36 - E;
        h > ct(2147483647 / w) && wr("overflow"), h *= w;
      }
      let d = e.length + 1;
      o = zp(i - f, d, f == 0), ct(i / d) > 2147483647 - n && wr("overflow"), n += ct(i / d), i %= d, e.splice(i++, 0, n);
    }
    var a;
    return String.fromCodePoint(...e);
  }, Hp = function(t) {
    let e = [], r = (t = Vp(t)).length, i = 128, n = 0, o = 72;
    for (let u of t) u < 128 && e.push(ja(u));
    let s = e.length, a = s;
    for (s && e.push("-"); a < r; ) {
      let u = 2147483647;
      for (let d of t) d >= i && d < u && (u = d);
      let f = a + 1;
      u - i > ct((2147483647 - n) / f) && wr("overflow"), n += (u - i) * f, i = u;
      for (let d of t) if (d < i && ++n > 2147483647 && wr("overflow"), d == i) {
        let h = n;
        for (let g = 36; ; g += 36) {
          let y = g <= o ? 1 : g >= o + 26 ? 26 : g - o;
          if (h < y) break;
          let E = h - y, w = 36 - y;
          e.push(ja(Wp(y + E % w, 0))), h = ct(E / w);
        }
        e.push(ja(Wp(h, 0))), o = zp(n, f, a == s), n = 0, ++a;
      }
      ++n, ++i;
    }
    return e.join("");
  }, Vt = { version: "2.1.0", ucs2: { decode: Vp, encode: (t) => String.fromCodePoint(...t) }, decode: $p, encode: Hp, toASCII: function(t) {
    return Fp(t, function(e) {
      return p1.test(e) ? "xn--" + Hp(e) : e;
    });
  }, toUnicode: function(t) {
    return Fp(t, function(e) {
      return d1.test(e) ? $p(e.slice(4).toLowerCase()) : e;
    });
  } };
  Vt.decode;
  Vt.encode;
  Vt.toASCII;
  Vt.toUnicode;
  Vt.ucs2;
  Vt.version;
});
function b1(t, e) {
  return Object.prototype.hasOwnProperty.call(t, e);
}
var w1;
var Pi;
var _1;
var ft;
var Qp = Ae(() => {
  _();
  v();
  m();
  w1 = function(t, e, r, i) {
    e = e || "&", r = r || "=";
    var n = {};
    if (typeof t != "string" || t.length === 0) return n;
    var o = /\+/g;
    t = t.split(e);
    var s = 1e3;
    i && typeof i.maxKeys == "number" && (s = i.maxKeys);
    var a = t.length;
    s > 0 && a > s && (a = s);
    for (var u = 0; u < a; ++u) {
      var f, d, h, g, y = t[u].replace(o, "%20"), E = y.indexOf(r);
      E >= 0 ? (f = y.substr(0, E), d = y.substr(E + 1)) : (f = y, d = ""), h = decodeURIComponent(f), g = decodeURIComponent(d), b1(n, h) ? Array.isArray(n[h]) ? n[h].push(g) : n[h] = [n[h], g] : n[h] = g;
    }
    return n;
  }, Pi = function(t) {
    switch (typeof t) {
      case "string":
        return t;
      case "boolean":
        return t ? "true" : "false";
      case "number":
        return isFinite(t) ? t : "";
      default:
        return "";
    }
  }, _1 = function(t, e, r, i) {
    return e = e || "&", r = r || "=", t === null && (t = void 0), typeof t == "object" ? Object.keys(t).map(function(n) {
      var o = encodeURIComponent(Pi(n)) + r;
      return Array.isArray(t[n]) ? t[n].map(function(s) {
        return o + encodeURIComponent(Pi(s));
      }).join(e) : o + encodeURIComponent(Pi(t[n]));
    }).join(e) : i ? encodeURIComponent(Pi(i)) + r + encodeURIComponent(Pi(t)) : "";
  }, ft = {};
  ft.decode = ft.parse = w1, ft.encode = ft.stringify = _1;
  ft.decode;
  ft.encode;
  ft.parse;
  ft.stringify;
});
function Fa() {
  throw new Error("setTimeout has not been defined");
}
function Wa() {
  throw new Error("clearTimeout has not been defined");
}
function Jp(t) {
  if (It === setTimeout) return setTimeout(t, 0);
  if ((It === Fa || !It) && setTimeout) return It = setTimeout, setTimeout(t, 0);
  try {
    return It(t, 0);
  } catch {
    try {
      return It.call(null, t, 0);
    } catch {
      return It.call(this || Zr, t, 0);
    }
  }
}
function m1() {
  Xr && _r && (Xr = false, _r.length ? Rt = _r.concat(Rt) : rs = -1, Rt.length && Xp());
}
function Xp() {
  if (!Xr) {
    var t = Jp(m1);
    Xr = true;
    for (var e = Rt.length; e; ) {
      for (_r = Rt, Rt = []; ++rs < e; ) _r && _r[rs].run();
      rs = -1, e = Rt.length;
    }
    _r = null, Xr = false, function(r) {
      if (Tt === clearTimeout) return clearTimeout(r);
      if ((Tt === Wa || !Tt) && clearTimeout) return Tt = clearTimeout, clearTimeout(r);
      try {
        Tt(r);
      } catch {
        try {
          return Tt.call(null, r);
        } catch {
          return Tt.call(this || Zr, r);
        }
      }
    }(t);
  }
}
function Gp(t, e) {
  (this || Zr).fun = t, (this || Zr).array = e;
}
function At() {
}
var Yp;
var It;
var Tt;
var Zr;
var fe;
var _r;
var Rt;
var Xr;
var rs;
var oe;
var Zp = Ae(() => {
  _();
  v();
  m();
  Zr = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : global, fe = Yp = {};
  (function() {
    try {
      It = typeof setTimeout == "function" ? setTimeout : Fa;
    } catch {
      It = Fa;
    }
    try {
      Tt = typeof clearTimeout == "function" ? clearTimeout : Wa;
    } catch {
      Tt = Wa;
    }
  })();
  Rt = [], Xr = false, rs = -1;
  fe.nextTick = function(t) {
    var e = new Array(arguments.length - 1);
    if (arguments.length > 1) for (var r = 1; r < arguments.length; r++) e[r - 1] = arguments[r];
    Rt.push(new Gp(t, e)), Rt.length !== 1 || Xr || Jp(Xp);
  }, Gp.prototype.run = function() {
    (this || Zr).fun.apply(null, (this || Zr).array);
  }, fe.title = "browser", fe.browser = true, fe.env = {}, fe.argv = [], fe.version = "", fe.versions = {}, fe.on = At, fe.addListener = At, fe.once = At, fe.off = At, fe.removeListener = At, fe.removeAllListeners = At, fe.emit = At, fe.prependListener = At, fe.prependOnceListener = At, fe.listeners = function(t) {
    return [];
  }, fe.binding = function(t) {
    throw new Error("process.binding is not supported");
  }, fe.cwd = function() {
    return "/";
  }, fe.chdir = function(t) {
    throw new Error("process.chdir is not supported");
  }, fe.umask = function() {
    return 0;
  };
  oe = Yp;
  oe.addListener;
  oe.argv;
  oe.binding;
  oe.browser;
  oe.chdir;
  oe.cwd;
  oe.emit;
  oe.env;
  oe.listeners;
  oe.nextTick;
  oe.off;
  oe.on;
  oe.once;
  oe.prependListener;
  oe.prependOnceListener;
  oe.removeAllListeners;
  oe.removeListener;
  oe.title;
  oe.umask;
  oe.version;
  oe.versions;
});
function v1() {
  if (eg) return $a;
  eg = true;
  var t = $a = {}, e, r;
  function i() {
    throw new Error("setTimeout has not been defined");
  }
  function n() {
    throw new Error("clearTimeout has not been defined");
  }
  (function() {
    try {
      typeof setTimeout == "function" ? e = setTimeout : e = i;
    } catch {
      e = i;
    }
    try {
      typeof clearTimeout == "function" ? r = clearTimeout : r = n;
    } catch {
      r = n;
    }
  })();
  function o(w) {
    if (e === setTimeout) return setTimeout(w, 0);
    if ((e === i || !e) && setTimeout) return e = setTimeout, setTimeout(w, 0);
    try {
      return e(w, 0);
    } catch {
      try {
        return e.call(null, w, 0);
      } catch {
        return e.call(this || ei, w, 0);
      }
    }
  }
  function s(w) {
    if (r === clearTimeout) return clearTimeout(w);
    if ((r === n || !r) && clearTimeout) return r = clearTimeout, clearTimeout(w);
    try {
      return r(w);
    } catch {
      try {
        return r.call(null, w);
      } catch {
        return r.call(this || ei, w);
      }
    }
  }
  var a = [], u = false, f, d = -1;
  function h() {
    !u || !f || (u = false, f.length ? a = f.concat(a) : d = -1, a.length && g());
  }
  function g() {
    if (!u) {
      var w = o(h);
      u = true;
      for (var S = a.length; S; ) {
        for (f = a, a = []; ++d < S; ) f && f[d].run();
        d = -1, S = a.length;
      }
      f = null, u = false, s(w);
    }
  }
  t.nextTick = function(w) {
    var S = new Array(arguments.length - 1);
    if (arguments.length > 1) for (var I = 1; I < arguments.length; I++) S[I - 1] = arguments[I];
    a.push(new y(w, S)), a.length === 1 && !u && o(g);
  };
  function y(w, S) {
    (this || ei).fun = w, (this || ei).array = S;
  }
  y.prototype.run = function() {
    (this || ei).fun.apply(null, (this || ei).array);
  }, t.title = "browser", t.browser = true, t.env = {}, t.argv = [], t.version = "", t.versions = {};
  function E() {
  }
  return t.on = E, t.addListener = E, t.once = E, t.off = E, t.removeListener = E, t.removeAllListeners = E, t.emit = E, t.prependListener = E, t.prependOnceListener = E, t.listeners = function(w) {
    return [];
  }, t.binding = function(w) {
    throw new Error("process.binding is not supported");
  }, t.cwd = function() {
    return "/";
  }, t.chdir = function(w) {
    throw new Error("process.chdir is not supported");
  }, t.umask = function() {
    return 0;
  }, $a;
}
var $a;
var eg;
var ei;
var ne;
var Ha = Ae(() => {
  _();
  v();
  m();
  $a = {}, eg = false, ei = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : global;
  ne = v1();
  ne.platform = "browser";
  ne.addListener;
  ne.argv;
  ne.binding;
  ne.browser;
  ne.chdir;
  ne.cwd;
  ne.emit;
  ne.env;
  ne.listeners;
  ne.nextTick;
  ne.off;
  ne.on;
  ne.once;
  ne.prependListener;
  ne.prependOnceListener;
  ne.removeAllListeners;
  ne.removeListener;
  ne.title;
  ne.umask;
  ne.version;
  ne.versions;
});
function E1() {
  if (tg) return Va;
  tg = true;
  var t = ne;
  function e(o) {
    if (typeof o != "string") throw new TypeError("Path must be a string. Received " + JSON.stringify(o));
  }
  function r(o, s) {
    for (var a = "", u = 0, f = -1, d = 0, h, g = 0; g <= o.length; ++g) {
      if (g < o.length) h = o.charCodeAt(g);
      else {
        if (h === 47) break;
        h = 47;
      }
      if (h === 47) {
        if (!(f === g - 1 || d === 1)) if (f !== g - 1 && d === 2) {
          if (a.length < 2 || u !== 2 || a.charCodeAt(a.length - 1) !== 46 || a.charCodeAt(a.length - 2) !== 46) {
            if (a.length > 2) {
              var y = a.lastIndexOf("/");
              if (y !== a.length - 1) {
                y === -1 ? (a = "", u = 0) : (a = a.slice(0, y), u = a.length - 1 - a.lastIndexOf("/")), f = g, d = 0;
                continue;
              }
            } else if (a.length === 2 || a.length === 1) {
              a = "", u = 0, f = g, d = 0;
              continue;
            }
          }
          s && (a.length > 0 ? a += "/.." : a = "..", u = 2);
        } else a.length > 0 ? a += "/" + o.slice(f + 1, g) : a = o.slice(f + 1, g), u = g - f - 1;
        f = g, d = 0;
      } else h === 46 && d !== -1 ? ++d : d = -1;
    }
    return a;
  }
  function i(o, s) {
    var a = s.dir || s.root, u = s.base || (s.name || "") + (s.ext || "");
    return a ? a === s.root ? a + u : a + o + u : u;
  }
  var n = { resolve: function() {
    for (var s = "", a = false, u, f = arguments.length - 1; f >= -1 && !a; f--) {
      var d;
      f >= 0 ? d = arguments[f] : (u === void 0 && (u = t.cwd()), d = u), e(d), d.length !== 0 && (s = d + "/" + s, a = d.charCodeAt(0) === 47);
    }
    return s = r(s, !a), a ? s.length > 0 ? "/" + s : "/" : s.length > 0 ? s : ".";
  }, normalize: function(s) {
    if (e(s), s.length === 0) return ".";
    var a = s.charCodeAt(0) === 47, u = s.charCodeAt(s.length - 1) === 47;
    return s = r(s, !a), s.length === 0 && !a && (s = "."), s.length > 0 && u && (s += "/"), a ? "/" + s : s;
  }, isAbsolute: function(s) {
    return e(s), s.length > 0 && s.charCodeAt(0) === 47;
  }, join: function() {
    if (arguments.length === 0) return ".";
    for (var s, a = 0; a < arguments.length; ++a) {
      var u = arguments[a];
      e(u), u.length > 0 && (s === void 0 ? s = u : s += "/" + u);
    }
    return s === void 0 ? "." : n.normalize(s);
  }, relative: function(s, a) {
    if (e(s), e(a), s === a || (s = n.resolve(s), a = n.resolve(a), s === a)) return "";
    for (var u = 1; u < s.length && s.charCodeAt(u) === 47; ++u) ;
    for (var f = s.length, d = f - u, h = 1; h < a.length && a.charCodeAt(h) === 47; ++h) ;
    for (var g = a.length, y = g - h, E = d < y ? d : y, w = -1, S = 0; S <= E; ++S) {
      if (S === E) {
        if (y > E) {
          if (a.charCodeAt(h + S) === 47) return a.slice(h + S + 1);
          if (S === 0) return a.slice(h + S);
        } else d > E && (s.charCodeAt(u + S) === 47 ? w = S : S === 0 && (w = 0));
        break;
      }
      var I = s.charCodeAt(u + S), C = a.charCodeAt(h + S);
      if (I !== C) break;
      I === 47 && (w = S);
    }
    var k = "";
    for (S = u + w + 1; S <= f; ++S) (S === f || s.charCodeAt(S) === 47) && (k.length === 0 ? k += ".." : k += "/..");
    return k.length > 0 ? k + a.slice(h + w) : (h += w, a.charCodeAt(h) === 47 && ++h, a.slice(h));
  }, _makeLong: function(s) {
    return s;
  }, dirname: function(s) {
    if (e(s), s.length === 0) return ".";
    for (var a = s.charCodeAt(0), u = a === 47, f = -1, d = true, h = s.length - 1; h >= 1; --h) if (a = s.charCodeAt(h), a === 47) {
      if (!d) {
        f = h;
        break;
      }
    } else d = false;
    return f === -1 ? u ? "/" : "." : u && f === 1 ? "//" : s.slice(0, f);
  }, basename: function(s, a) {
    if (a !== void 0 && typeof a != "string") throw new TypeError('"ext" argument must be a string');
    e(s);
    var u = 0, f = -1, d = true, h;
    if (a !== void 0 && a.length > 0 && a.length <= s.length) {
      if (a.length === s.length && a === s) return "";
      var g = a.length - 1, y = -1;
      for (h = s.length - 1; h >= 0; --h) {
        var E = s.charCodeAt(h);
        if (E === 47) {
          if (!d) {
            u = h + 1;
            break;
          }
        } else y === -1 && (d = false, y = h + 1), g >= 0 && (E === a.charCodeAt(g) ? --g === -1 && (f = h) : (g = -1, f = y));
      }
      return u === f ? f = y : f === -1 && (f = s.length), s.slice(u, f);
    } else {
      for (h = s.length - 1; h >= 0; --h) if (s.charCodeAt(h) === 47) {
        if (!d) {
          u = h + 1;
          break;
        }
      } else f === -1 && (d = false, f = h + 1);
      return f === -1 ? "" : s.slice(u, f);
    }
  }, extname: function(s) {
    e(s);
    for (var a = -1, u = 0, f = -1, d = true, h = 0, g = s.length - 1; g >= 0; --g) {
      var y = s.charCodeAt(g);
      if (y === 47) {
        if (!d) {
          u = g + 1;
          break;
        }
        continue;
      }
      f === -1 && (d = false, f = g + 1), y === 46 ? a === -1 ? a = g : h !== 1 && (h = 1) : a !== -1 && (h = -1);
    }
    return a === -1 || f === -1 || h === 0 || h === 1 && a === f - 1 && a === u + 1 ? "" : s.slice(a, f);
  }, format: function(s) {
    if (s === null || typeof s != "object") throw new TypeError('The "pathObject" argument must be of type Object. Received type ' + typeof s);
    return i("/", s);
  }, parse: function(s) {
    e(s);
    var a = { root: "", dir: "", base: "", ext: "", name: "" };
    if (s.length === 0) return a;
    var u = s.charCodeAt(0), f = u === 47, d;
    f ? (a.root = "/", d = 1) : d = 0;
    for (var h = -1, g = 0, y = -1, E = true, w = s.length - 1, S = 0; w >= d; --w) {
      if (u = s.charCodeAt(w), u === 47) {
        if (!E) {
          g = w + 1;
          break;
        }
        continue;
      }
      y === -1 && (E = false, y = w + 1), u === 46 ? h === -1 ? h = w : S !== 1 && (S = 1) : h !== -1 && (S = -1);
    }
    return h === -1 || y === -1 || S === 0 || S === 1 && h === y - 1 && h === g + 1 ? y !== -1 && (g === 0 && f ? a.base = a.name = s.slice(1, y) : a.base = a.name = s.slice(g, y)) : (g === 0 && f ? (a.name = s.slice(1, h), a.base = s.slice(1, y)) : (a.name = s.slice(g, h), a.base = s.slice(g, y)), a.ext = s.slice(h, y)), g > 0 ? a.dir = s.slice(0, g - 1) : f && (a.dir = "/"), a;
  }, sep: "/", delimiter: ":", win32: null, posix: null };
  return n.posix = n, Va = n, Va;
}
var Va;
var tg;
var za;
var rg = Ae(() => {
  _();
  v();
  m();
  Ha();
  Va = {}, tg = false;
  za = E1();
});
var cg = {};
Ar(cg, { URL: () => X1, Url: () => K1, default: () => X, fileURLToPath: () => lg, format: () => Q1, parse: () => J1, pathToFileURL: () => ug, resolve: () => G1, resolveObject: () => Y1 });
function De() {
  this.protocol = null, this.slashes = null, this.auth = null, this.host = null, this.port = null, this.hostname = null, this.hash = null, this.search = null, this.query = null, this.pathname = null, this.path = null, this.href = null;
}
function Bi(t, e, r) {
  if (t && ht.isObject(t) && t instanceof De) return t;
  var i = new De();
  return i.parse(t, e, r), i;
}
function P1() {
  if (og) return Ga;
  og = true;
  var t = oe;
  function e(o) {
    if (typeof o != "string") throw new TypeError("Path must be a string. Received " + JSON.stringify(o));
  }
  function r(o, s) {
    for (var a = "", u = 0, f = -1, d = 0, h, g = 0; g <= o.length; ++g) {
      if (g < o.length) h = o.charCodeAt(g);
      else {
        if (h === 47) break;
        h = 47;
      }
      if (h === 47) {
        if (!(f === g - 1 || d === 1)) if (f !== g - 1 && d === 2) {
          if (a.length < 2 || u !== 2 || a.charCodeAt(a.length - 1) !== 46 || a.charCodeAt(a.length - 2) !== 46) {
            if (a.length > 2) {
              var y = a.lastIndexOf("/");
              if (y !== a.length - 1) {
                y === -1 ? (a = "", u = 0) : (a = a.slice(0, y), u = a.length - 1 - a.lastIndexOf("/")), f = g, d = 0;
                continue;
              }
            } else if (a.length === 2 || a.length === 1) {
              a = "", u = 0, f = g, d = 0;
              continue;
            }
          }
          s && (a.length > 0 ? a += "/.." : a = "..", u = 2);
        } else a.length > 0 ? a += "/" + o.slice(f + 1, g) : a = o.slice(f + 1, g), u = g - f - 1;
        f = g, d = 0;
      } else h === 46 && d !== -1 ? ++d : d = -1;
    }
    return a;
  }
  function i(o, s) {
    var a = s.dir || s.root, u = s.base || (s.name || "") + (s.ext || "");
    return a ? a === s.root ? a + u : a + o + u : u;
  }
  var n = { resolve: function() {
    for (var s = "", a = false, u, f = arguments.length - 1; f >= -1 && !a; f--) {
      var d;
      f >= 0 ? d = arguments[f] : (u === void 0 && (u = t.cwd()), d = u), e(d), d.length !== 0 && (s = d + "/" + s, a = d.charCodeAt(0) === 47);
    }
    return s = r(s, !a), a ? s.length > 0 ? "/" + s : "/" : s.length > 0 ? s : ".";
  }, normalize: function(s) {
    if (e(s), s.length === 0) return ".";
    var a = s.charCodeAt(0) === 47, u = s.charCodeAt(s.length - 1) === 47;
    return s = r(s, !a), s.length === 0 && !a && (s = "."), s.length > 0 && u && (s += "/"), a ? "/" + s : s;
  }, isAbsolute: function(s) {
    return e(s), s.length > 0 && s.charCodeAt(0) === 47;
  }, join: function() {
    if (arguments.length === 0) return ".";
    for (var s, a = 0; a < arguments.length; ++a) {
      var u = arguments[a];
      e(u), u.length > 0 && (s === void 0 ? s = u : s += "/" + u);
    }
    return s === void 0 ? "." : n.normalize(s);
  }, relative: function(s, a) {
    if (e(s), e(a), s === a || (s = n.resolve(s), a = n.resolve(a), s === a)) return "";
    for (var u = 1; u < s.length && s.charCodeAt(u) === 47; ++u) ;
    for (var f = s.length, d = f - u, h = 1; h < a.length && a.charCodeAt(h) === 47; ++h) ;
    for (var g = a.length, y = g - h, E = d < y ? d : y, w = -1, S = 0; S <= E; ++S) {
      if (S === E) {
        if (y > E) {
          if (a.charCodeAt(h + S) === 47) return a.slice(h + S + 1);
          if (S === 0) return a.slice(h + S);
        } else d > E && (s.charCodeAt(u + S) === 47 ? w = S : S === 0 && (w = 0));
        break;
      }
      var I = s.charCodeAt(u + S), C = a.charCodeAt(h + S);
      if (I !== C) break;
      I === 47 && (w = S);
    }
    var k = "";
    for (S = u + w + 1; S <= f; ++S) (S === f || s.charCodeAt(S) === 47) && (k.length === 0 ? k += ".." : k += "/..");
    return k.length > 0 ? k + a.slice(h + w) : (h += w, a.charCodeAt(h) === 47 && ++h, a.slice(h));
  }, _makeLong: function(s) {
    return s;
  }, dirname: function(s) {
    if (e(s), s.length === 0) return ".";
    for (var a = s.charCodeAt(0), u = a === 47, f = -1, d = true, h = s.length - 1; h >= 1; --h) if (a = s.charCodeAt(h), a === 47) {
      if (!d) {
        f = h;
        break;
      }
    } else d = false;
    return f === -1 ? u ? "/" : "." : u && f === 1 ? "//" : s.slice(0, f);
  }, basename: function(s, a) {
    if (a !== void 0 && typeof a != "string") throw new TypeError('"ext" argument must be a string');
    e(s);
    var u = 0, f = -1, d = true, h;
    if (a !== void 0 && a.length > 0 && a.length <= s.length) {
      if (a.length === s.length && a === s) return "";
      var g = a.length - 1, y = -1;
      for (h = s.length - 1; h >= 0; --h) {
        var E = s.charCodeAt(h);
        if (E === 47) {
          if (!d) {
            u = h + 1;
            break;
          }
        } else y === -1 && (d = false, y = h + 1), g >= 0 && (E === a.charCodeAt(g) ? --g === -1 && (f = h) : (g = -1, f = y));
      }
      return u === f ? f = y : f === -1 && (f = s.length), s.slice(u, f);
    } else {
      for (h = s.length - 1; h >= 0; --h) if (s.charCodeAt(h) === 47) {
        if (!d) {
          u = h + 1;
          break;
        }
      } else f === -1 && (d = false, f = h + 1);
      return f === -1 ? "" : s.slice(u, f);
    }
  }, extname: function(s) {
    e(s);
    for (var a = -1, u = 0, f = -1, d = true, h = 0, g = s.length - 1; g >= 0; --g) {
      var y = s.charCodeAt(g);
      if (y === 47) {
        if (!d) {
          u = g + 1;
          break;
        }
        continue;
      }
      f === -1 && (d = false, f = g + 1), y === 46 ? a === -1 ? a = g : h !== 1 && (h = 1) : a !== -1 && (h = -1);
    }
    return a === -1 || f === -1 || h === 0 || h === 1 && a === f - 1 && a === u + 1 ? "" : s.slice(a, f);
  }, format: function(s) {
    if (s === null || typeof s != "object") throw new TypeError('The "pathObject" argument must be of type Object. Received type ' + typeof s);
    return i("/", s);
  }, parse: function(s) {
    e(s);
    var a = { root: "", dir: "", base: "", ext: "", name: "" };
    if (s.length === 0) return a;
    var u = s.charCodeAt(0), f = u === 47, d;
    f ? (a.root = "/", d = 1) : d = 0;
    for (var h = -1, g = 0, y = -1, E = true, w = s.length - 1, S = 0; w >= d; --w) {
      if (u = s.charCodeAt(w), u === 47) {
        if (!E) {
          g = w + 1;
          break;
        }
        continue;
      }
      y === -1 && (E = false, y = w + 1), u === 46 ? h === -1 ? h = w : S !== 1 && (S = 1) : h !== -1 && (S = -1);
    }
    return h === -1 || y === -1 || S === 0 || S === 1 && h === y - 1 && h === g + 1 ? y !== -1 && (g === 0 && f ? a.base = a.name = s.slice(1, y) : a.base = a.name = s.slice(g, y)) : (g === 0 && f ? (a.name = s.slice(1, h), a.base = s.slice(1, y)) : (a.name = s.slice(g, h), a.base = s.slice(g, y)), a.ext = s.slice(h, y)), g > 0 ? a.dir = s.slice(0, g - 1) : f && (a.dir = "/"), a;
  }, sep: "/", delimiter: ":", win32: null, posix: null };
  return n.posix = n, Ga = n, Ga;
}
function W1(t) {
  if (typeof t == "string") t = new URL(t);
  else if (!(t instanceof URL)) throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");
  if (t.protocol !== "file:") throw new Deno.errors.InvalidData("invalid url scheme");
  return Ja ? $1(t) : H1(t);
}
function $1(t) {
  let e = t.hostname, r = t.pathname;
  for (let i = 0; i < r.length; i++) if (r[i] === "%") {
    let n = r.codePointAt(i + 2) || 32;
    if (r[i + 1] === "2" && n === 102 || r[i + 1] === "5" && n === 99) throw new Deno.errors.InvalidData("must not include encoded \\ or / characters");
  }
  if (r = r.replace(q1, "\\"), r = decodeURIComponent(r), e !== "") return `\\\\${e}${r}`;
  {
    let i = r.codePointAt(1) | 32, n = r[2];
    if (i < M1 || i > L1 || n !== ":") throw new Deno.errors.InvalidData("file url path must be absolute");
    return r.slice(1);
  }
}
function H1(t) {
  if (t.hostname !== "") throw new Deno.errors.InvalidData("invalid file url hostname");
  let e = t.pathname;
  for (let r = 0; r < e.length; r++) if (e[r] === "%") {
    let i = e.codePointAt(r + 2) || 32;
    if (e[r + 1] === "2" && i === 102) throw new Deno.errors.InvalidData("must not include encoded / characters");
  }
  return decodeURIComponent(e);
}
function V1(t) {
  let e = ag.resolve(t), r = t.charCodeAt(t.length - 1);
  (r === O1 || Ja && r === x1) && e[e.length - 1] !== ag.sep && (e += "/");
  let i = new URL("file://");
  return e.includes("%") && (e = e.replace(U1, "%25")), !Ja && e.includes("\\") && (e = e.replace(N1, "%5C")), e.includes(`
`) && (e = e.replace(D1, "%0A")), e.includes("\r") && (e = e.replace(j1, "%0D")), e.includes("	") && (e = e.replace(F1, "%09")), i.pathname = e, i;
}
function lg(t) {
  if (typeof t == "string") t = new URL(t);
  else if (!(t instanceof URL)) throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");
  if (t.protocol !== "file:") throw new Deno.errors.InvalidData("invalid url scheme");
  return Xa ? uS(t) : cS(t);
}
function uS(t) {
  let e = t.hostname, r = t.pathname;
  for (let i = 0; i < r.length; i++) if (r[i] === "%") {
    let n = r.codePointAt(i + 2) || 32;
    if (r[i + 1] === "2" && n === 102 || r[i + 1] === "5" && n === 99) throw new Deno.errors.InvalidData("must not include encoded \\ or / characters");
  }
  if (r = r.replace(iS, "\\"), r = decodeURIComponent(r), e !== "") return `\\\\${e}${r}`;
  {
    let i = r.codePointAt(1) | 32, n = r[2];
    if (i < tS || i > rS || n !== ":") throw new Deno.errors.InvalidData("file url path must be absolute");
    return r.slice(1);
  }
}
function cS(t) {
  if (t.hostname !== "") throw new Deno.errors.InvalidData("invalid file url hostname");
  let e = t.pathname;
  for (let r = 0; r < e.length; r++) if (e[r] === "%") {
    let i = e.codePointAt(r + 2) || 32;
    if (e[r + 1] === "2" && i === 102) throw new Deno.errors.InvalidData("must not include encoded / characters");
  }
  return decodeURIComponent(e);
}
function ug(t) {
  let e = za.resolve(t), r = t.charCodeAt(t.length - 1);
  (r === eS || Xa && r === Z1) && e[e.length - 1] !== za.sep && (e += "/");
  let i = new URL("file://");
  return e.includes("%") && (e = e.replace(nS, "%25")), !Xa && e.includes("\\") && (e = e.replace(sS, "%5C")), e.includes(`
`) && (e = e.replace(oS, "%0A")), e.includes("\r") && (e = e.replace(aS, "%0D")), e.includes("	") && (e = e.replace(lS, "%09")), i.pathname = e, i;
}
var X;
var S1;
var ht;
var A1;
var I1;
var T1;
var R1;
var Ya;
var ig;
var ng;
var sg;
var C1;
var k1;
var Ka;
var ti;
var Qa;
var Ga;
var og;
var ag;
var B1;
var x1;
var O1;
var M1;
var L1;
var Ja;
var q1;
var U1;
var N1;
var D1;
var j1;
var F1;
var z1;
var K1;
var Q1;
var G1;
var Y1;
var J1;
var X1;
var Z1;
var eS;
var tS;
var rS;
var Xa;
var iS;
var nS;
var sS;
var oS;
var aS;
var lS;
var fg = Ae(() => {
  _();
  v();
  m();
  Kp();
  Qp();
  Zp();
  rg();
  Ha();
  X = {}, S1 = Vt, ht = { isString: function(t) {
    return typeof t == "string";
  }, isObject: function(t) {
    return typeof t == "object" && t !== null;
  }, isNull: function(t) {
    return t === null;
  }, isNullOrUndefined: function(t) {
    return t == null;
  } };
  X.parse = Bi, X.resolve = function(t, e) {
    return Bi(t, false, true).resolve(e);
  }, X.resolveObject = function(t, e) {
    return t ? Bi(t, false, true).resolveObject(e) : e;
  }, X.format = function(t) {
    return ht.isString(t) && (t = Bi(t)), t instanceof De ? t.format() : De.prototype.format.call(t);
  }, X.Url = De;
  A1 = /^([a-z0-9.+-]+:)/i, I1 = /:[0-9]*$/, T1 = /^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/, R1 = ["{", "}", "|", "\\", "^", "`"].concat(["<", ">", '"', "`", " ", "\r", `
`, "	"]), Ya = ["'"].concat(R1), ig = ["%", "/", "?", ";", "#"].concat(Ya), ng = ["/", "?", "#"], sg = /^[+a-z0-9A-Z_-]{0,63}$/, C1 = /^([+a-z0-9A-Z_-]{0,63})(.*)$/, k1 = { javascript: true, "javascript:": true }, Ka = { javascript: true, "javascript:": true }, ti = { http: true, https: true, ftp: true, gopher: true, file: true, "http:": true, "https:": true, "ftp:": true, "gopher:": true, "file:": true }, Qa = ft;
  De.prototype.parse = function(t, e, r) {
    if (!ht.isString(t)) throw new TypeError("Parameter 'url' must be a string, not " + typeof t);
    var i = t.indexOf("?"), n = i !== -1 && i < t.indexOf("#") ? "?" : "#", o = t.split(n);
    o[0] = o[0].replace(/\\/g, "/");
    var s = t = o.join(n);
    if (s = s.trim(), !r && t.split("#").length === 1) {
      var a = T1.exec(s);
      if (a) return this.path = s, this.href = s, this.pathname = a[1], a[2] ? (this.search = a[2], this.query = e ? Qa.parse(this.search.substr(1)) : this.search.substr(1)) : e && (this.search = "", this.query = {}), this;
    }
    var u = A1.exec(s);
    if (u) {
      var f = (u = u[0]).toLowerCase();
      this.protocol = f, s = s.substr(u.length);
    }
    if (r || u || s.match(/^\/\/[^@\/]+@[^@\/]+/)) {
      var d = s.substr(0, 2) === "//";
      !d || u && Ka[u] || (s = s.substr(2), this.slashes = true);
    }
    if (!Ka[u] && (d || u && !ti[u])) {
      for (var h, g, y = -1, E = 0; E < ng.length; E++) (w = s.indexOf(ng[E])) !== -1 && (y === -1 || w < y) && (y = w);
      for ((g = y === -1 ? s.lastIndexOf("@") : s.lastIndexOf("@", y)) !== -1 && (h = s.slice(0, g), s = s.slice(g + 1), this.auth = decodeURIComponent(h)), y = -1, E = 0; E < ig.length; E++) {
        var w;
        (w = s.indexOf(ig[E])) !== -1 && (y === -1 || w < y) && (y = w);
      }
      y === -1 && (y = s.length), this.host = s.slice(0, y), s = s.slice(y), this.parseHost(), this.hostname = this.hostname || "";
      var S = this.hostname[0] === "[" && this.hostname[this.hostname.length - 1] === "]";
      if (!S) for (var I = this.hostname.split(/\./), C = (E = 0, I.length); E < C; E++) {
        var k = I[E];
        if (k && !k.match(sg)) {
          for (var M = "", q = 0, z = k.length; q < z; q++) k.charCodeAt(q) > 127 ? M += "x" : M += k[q];
          if (!M.match(sg)) {
            var j = I.slice(0, E), Q = I.slice(E + 1), $ = k.match(C1);
            $ && (j.push($[1]), Q.unshift($[2])), Q.length && (s = "/" + Q.join(".") + s), this.hostname = j.join(".");
            break;
          }
        }
      }
      this.hostname.length > 255 ? this.hostname = "" : this.hostname = this.hostname.toLowerCase(), S || (this.hostname = S1.toASCII(this.hostname));
      var te = this.port ? ":" + this.port : "", pt = this.hostname || "";
      this.host = pt + te, this.href += this.host, S && (this.hostname = this.hostname.substr(1, this.hostname.length - 2), s[0] !== "/" && (s = "/" + s));
    }
    if (!k1[f]) for (E = 0, C = Ya.length; E < C; E++) {
      var Fe = Ya[E];
      if (s.indexOf(Fe) !== -1) {
        var Se = encodeURIComponent(Fe);
        Se === Fe && (Se = escape(Fe)), s = s.split(Fe).join(Se);
      }
    }
    var mr = s.indexOf("#");
    mr !== -1 && (this.hash = s.substr(mr), s = s.slice(0, mr));
    var vr = s.indexOf("?");
    if (vr !== -1 ? (this.search = s.substr(vr), this.query = s.substr(vr + 1), e && (this.query = Qa.parse(this.query)), s = s.slice(0, vr)) : e && (this.search = "", this.query = {}), s && (this.pathname = s), ti[f] && this.hostname && !this.pathname && (this.pathname = "/"), this.pathname || this.search) {
      te = this.pathname || "";
      var os = this.search || "";
      this.path = te + os;
    }
    return this.href = this.format(), this;
  }, De.prototype.format = function() {
    var t = this.auth || "";
    t && (t = (t = encodeURIComponent(t)).replace(/%3A/i, ":"), t += "@");
    var e = this.protocol || "", r = this.pathname || "", i = this.hash || "", n = false, o = "";
    this.host ? n = t + this.host : this.hostname && (n = t + (this.hostname.indexOf(":") === -1 ? this.hostname : "[" + this.hostname + "]"), this.port && (n += ":" + this.port)), this.query && ht.isObject(this.query) && Object.keys(this.query).length && (o = Qa.stringify(this.query));
    var s = this.search || o && "?" + o || "";
    return e && e.substr(-1) !== ":" && (e += ":"), this.slashes || (!e || ti[e]) && n !== false ? (n = "//" + (n || ""), r && r.charAt(0) !== "/" && (r = "/" + r)) : n || (n = ""), i && i.charAt(0) !== "#" && (i = "#" + i), s && s.charAt(0) !== "?" && (s = "?" + s), e + n + (r = r.replace(/[?#]/g, function(a) {
      return encodeURIComponent(a);
    })) + (s = s.replace("#", "%23")) + i;
  }, De.prototype.resolve = function(t) {
    return this.resolveObject(Bi(t, false, true)).format();
  }, De.prototype.resolveObject = function(t) {
    if (ht.isString(t)) {
      var e = new De();
      e.parse(t, false, true), t = e;
    }
    for (var r = new De(), i = Object.keys(this), n = 0; n < i.length; n++) {
      var o = i[n];
      r[o] = this[o];
    }
    if (r.hash = t.hash, t.href === "") return r.href = r.format(), r;
    if (t.slashes && !t.protocol) {
      for (var s = Object.keys(t), a = 0; a < s.length; a++) {
        var u = s[a];
        u !== "protocol" && (r[u] = t[u]);
      }
      return ti[r.protocol] && r.hostname && !r.pathname && (r.path = r.pathname = "/"), r.href = r.format(), r;
    }
    if (t.protocol && t.protocol !== r.protocol) {
      if (!ti[t.protocol]) {
        for (var f = Object.keys(t), d = 0; d < f.length; d++) {
          var h = f[d];
          r[h] = t[h];
        }
        return r.href = r.format(), r;
      }
      if (r.protocol = t.protocol, t.host || Ka[t.protocol]) r.pathname = t.pathname;
      else {
        for (var g = (t.pathname || "").split("/"); g.length && !(t.host = g.shift()); ) ;
        t.host || (t.host = ""), t.hostname || (t.hostname = ""), g[0] !== "" && g.unshift(""), g.length < 2 && g.unshift(""), r.pathname = g.join("/");
      }
      if (r.search = t.search, r.query = t.query, r.host = t.host || "", r.auth = t.auth, r.hostname = t.hostname || t.host, r.port = t.port, r.pathname || r.search) {
        var y = r.pathname || "", E = r.search || "";
        r.path = y + E;
      }
      return r.slashes = r.slashes || t.slashes, r.href = r.format(), r;
    }
    var w = r.pathname && r.pathname.charAt(0) === "/", S = t.host || t.pathname && t.pathname.charAt(0) === "/", I = S || w || r.host && t.pathname, C = I, k = r.pathname && r.pathname.split("/") || [], M = (g = t.pathname && t.pathname.split("/") || [], r.protocol && !ti[r.protocol]);
    if (M && (r.hostname = "", r.port = null, r.host && (k[0] === "" ? k[0] = r.host : k.unshift(r.host)), r.host = "", t.protocol && (t.hostname = null, t.port = null, t.host && (g[0] === "" ? g[0] = t.host : g.unshift(t.host)), t.host = null), I = I && (g[0] === "" || k[0] === "")), S) r.host = t.host || t.host === "" ? t.host : r.host, r.hostname = t.hostname || t.hostname === "" ? t.hostname : r.hostname, r.search = t.search, r.query = t.query, k = g;
    else if (g.length) k || (k = []), k.pop(), k = k.concat(g), r.search = t.search, r.query = t.query;
    else if (!ht.isNullOrUndefined(t.search)) return M && (r.hostname = r.host = k.shift(), ($ = !!(r.host && r.host.indexOf("@") > 0) && r.host.split("@")) && (r.auth = $.shift(), r.host = r.hostname = $.shift())), r.search = t.search, r.query = t.query, ht.isNull(r.pathname) && ht.isNull(r.search) || (r.path = (r.pathname ? r.pathname : "") + (r.search ? r.search : "")), r.href = r.format(), r;
    if (!k.length) return r.pathname = null, r.search ? r.path = "/" + r.search : r.path = null, r.href = r.format(), r;
    for (var q = k.slice(-1)[0], z = (r.host || t.host || k.length > 1) && (q === "." || q === "..") || q === "", j = 0, Q = k.length; Q >= 0; Q--) (q = k[Q]) === "." ? k.splice(Q, 1) : q === ".." ? (k.splice(Q, 1), j++) : j && (k.splice(Q, 1), j--);
    if (!I && !C) for (; j--; j) k.unshift("..");
    !I || k[0] === "" || k[0] && k[0].charAt(0) === "/" || k.unshift(""), z && k.join("/").substr(-1) !== "/" && k.push("");
    var $, te = k[0] === "" || k[0] && k[0].charAt(0) === "/";
    return M && (r.hostname = r.host = te ? "" : k.length ? k.shift() : "", ($ = !!(r.host && r.host.indexOf("@") > 0) && r.host.split("@")) && (r.auth = $.shift(), r.host = r.hostname = $.shift())), (I = I || r.host && k.length) && !te && k.unshift(""), k.length ? r.pathname = k.join("/") : (r.pathname = null, r.path = null), ht.isNull(r.pathname) && ht.isNull(r.search) || (r.path = (r.pathname ? r.pathname : "") + (r.search ? r.search : "")), r.auth = t.auth || r.auth, r.slashes = r.slashes || t.slashes, r.href = r.format(), r;
  }, De.prototype.parseHost = function() {
    var t = this.host, e = I1.exec(t);
    e && ((e = e[0]) !== ":" && (this.port = e.substr(1)), t = t.substr(0, t.length - e.length)), t && (this.hostname = t);
  };
  X.Url;
  X.format;
  X.resolve;
  X.resolveObject;
  Ga = {}, og = false;
  ag = P1(), B1 = typeof Deno < "u" ? Deno.build.os === "windows" ? "win32" : Deno.build.os : void 0;
  X.URL = typeof URL < "u" ? URL : null;
  X.pathToFileURL = V1;
  X.fileURLToPath = W1;
  X.Url;
  X.format;
  X.resolve;
  X.resolveObject;
  X.URL;
  x1 = 92, O1 = 47, M1 = 97, L1 = 122, Ja = B1 === "win32", q1 = /\//g, U1 = /%/g, N1 = /\\/g, D1 = /\n/g, j1 = /\r/g, F1 = /\t/g;
  z1 = typeof Deno < "u" ? Deno.build.os === "windows" ? "win32" : Deno.build.os : void 0;
  X.URL = typeof URL < "u" ? URL : null;
  X.pathToFileURL = ug;
  X.fileURLToPath = lg;
  K1 = X.Url, Q1 = X.format, G1 = X.resolve, Y1 = X.resolveObject, J1 = X.parse, X1 = X.URL, Z1 = 92, eS = 47, tS = 97, rS = 122, Xa = z1 === "win32", iS = /\//g, nS = /%/g, sS = /\\/g, oS = /\n/g, aS = /\r/g, lS = /\t/g;
});
var dg = O((j3, hg) => {
  "use strict";
  _();
  v();
  m();
  hg.exports = function() {
    throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object");
  };
});
var is = O((xi) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(xi, "__esModule", { value: true });
  xi.BufferedDuplex = void 0;
  xi.writev = gg;
  var fS = Nt(), pg = (he(), G(be));
  function gg(t, e) {
    let r = new Array(t.length);
    for (let i = 0; i < t.length; i++) typeof t[i].chunk == "string" ? r[i] = pg.Buffer.from(t[i].chunk, "utf8") : r[i] = t[i].chunk;
    this._write(pg.Buffer.concat(r), "binary", e);
  }
  var Za = class extends fS.Duplex {
    constructor(e, r, i) {
      super({ objectMode: true });
      __publicField(this, "socket");
      __publicField(this, "proxy");
      __publicField(this, "isSocketOpen");
      __publicField(this, "writeQueue");
      this.proxy = r, this.socket = i, this.writeQueue = [], e.objectMode || (this._writev = gg.bind(this)), this.isSocketOpen = false, this.proxy.on("data", (n) => {
        !this.destroyed && this.readable && this.push(n);
      });
    }
    _read(e) {
      this.proxy.read(e);
    }
    _write(e, r, i) {
      this.isSocketOpen ? this.writeToProxy(e, r, i) : this.writeQueue.push({ chunk: e, encoding: r, cb: i });
    }
    _final(e) {
      this.writeQueue = [], this.proxy.end(e);
    }
    _destroy(e, r) {
      this.writeQueue = [], this.proxy.destroy(), r(e);
    }
    socketReady() {
      this.emit("connect"), this.isSocketOpen = true, this.processWriteQueue();
    }
    writeToProxy(e, r, i) {
      this.proxy.write(e, r) === false ? this.proxy.once("drain", i) : i();
    }
    processWriteQueue() {
      for (; this.writeQueue.length > 0; ) {
        let { chunk: e, encoding: r, cb: i } = this.writeQueue.shift();
        this.writeToProxy(e, r, i);
      }
    }
  };
  xi.BufferedDuplex = Za;
});
var Oi = O((Kt) => {
  "use strict";
  _();
  v();
  m();
  var tl = Kt && Kt.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Kt, "__esModule", { value: true });
  Kt.streamBuilder = Kt.browserStreamBuilder = void 0;
  var ns = (he(), G(be)), yg = tl(dg()), hS = tl(nt()), dS = Nt(), pS = tl(Ri()), el = is(), zt = (0, hS.default)("mqttjs:ws"), gS = ["rejectUnauthorized", "ca", "cert", "key", "pfx", "passphrase"];
  function bg(t, e) {
    let r = `${t.protocol}://${t.hostname}:${t.port}${t.path}`;
    return typeof t.transformWsUrl == "function" && (r = t.transformWsUrl(r, t, e)), r;
  }
  function wg(t) {
    let e = t;
    return t.port || (t.protocol === "wss" ? e.port = 443 : e.port = 80), t.path || (e.path = "/"), t.wsOptions || (e.wsOptions = {}), !pS.default && !t.forceNativeWebSocket && t.protocol === "wss" && gS.forEach((r) => {
      Object.prototype.hasOwnProperty.call(t, r) && !Object.prototype.hasOwnProperty.call(t.wsOptions, r) && (e.wsOptions[r] = t[r]);
    }), e;
  }
  function yS(t) {
    let e = wg(t);
    if (e.hostname || (e.hostname = e.host), !e.hostname) {
      if (typeof document > "u") throw new Error("Could not determine host. Specify host manually.");
      let r = new URL(document.URL);
      e.hostname = r.hostname, e.port || (e.port = Number(r.port));
    }
    return e.objectMode === void 0 && (e.objectMode = !(e.binary === true || e.binary === void 0)), e;
  }
  function bS(t, e, r) {
    zt("createWebSocket"), zt(`protocol: ${r.protocolId} ${r.protocolVersion}`);
    let i = r.protocolId === "MQIsdp" && r.protocolVersion === 3 ? "mqttv3.1" : "mqtt";
    zt(`creating new Websocket for url: ${e} and protocol: ${i}`);
    let n;
    return r.createWebsocket ? n = r.createWebsocket(e, [i], r) : n = new yg.default(e, [i], r.wsOptions), n;
  }
  function wS(t, e) {
    let r = e.protocolId === "MQIsdp" && e.protocolVersion === 3 ? "mqttv3.1" : "mqtt", i = bg(e, t), n;
    return e.createWebsocket ? n = e.createWebsocket(i, [r], e) : n = new WebSocket(i, [r]), n.binaryType = "arraybuffer", n;
  }
  var _S = (t, e) => {
    zt("streamBuilder");
    let r = wg(e);
    r.hostname = r.hostname || r.host || "localhost";
    let i = bg(r, t), n = bS(t, i, r), o = yg.default.createWebSocketStream(n, r.wsOptions);
    return o.url = i, n.on("close", () => {
      o.destroy();
    }), o;
  };
  Kt.streamBuilder = _S;
  var mS = (t, e) => {
    zt("browserStreamBuilder");
    let r, n = yS(e).browserBufferSize || 1024 * 512, o = e.browserBufferTimeout || 1e3, s = !e.objectMode, a = wS(t, e), u = d(e, w, S);
    e.objectMode || (u._writev = el.writev.bind(u)), u.on("close", () => {
      a.close();
    });
    let f = typeof a.addEventListener < "u";
    a.readyState === a.OPEN ? (r = u, r.socket = a) : (r = new el.BufferedDuplex(e, u, a), f ? a.addEventListener("open", h) : a.onopen = h), f ? (a.addEventListener("close", g), a.addEventListener("error", y), a.addEventListener("message", E)) : (a.onclose = g, a.onerror = y, a.onmessage = E);
    function d(I, C, k) {
      let M = new dS.Transform({ objectMode: I.objectMode });
      return M._write = C, M._flush = k, M;
    }
    function h() {
      zt("WebSocket onOpen"), r instanceof el.BufferedDuplex && r.socketReady();
    }
    function g(I) {
      zt("WebSocket onClose", I), r.end(), r.destroy();
    }
    function y(I) {
      zt("WebSocket onError", I);
      let C = new Error("WebSocket error");
      C.event = I, r.destroy(C);
    }
    async function E(I) {
      if (!u || u.destroyed || !u.readable) return;
      let { data: C } = I;
      C instanceof ArrayBuffer ? C = ns.Buffer.from(C) : C instanceof Blob ? C = ns.Buffer.from(await new Response(C).arrayBuffer()) : C = ns.Buffer.from(C, "utf8"), u.push(C);
    }
    function w(I, C, k) {
      if (a.bufferedAmount > n) {
        setTimeout(w, o, I, C, k);
        return;
      }
      s && typeof I == "string" && (I = ns.Buffer.from(I, "utf8"));
      try {
        a.send(I);
      } catch (M) {
        return k(M);
      }
      k();
    }
    function S(I) {
      a.close(), I();
    }
    return r;
  };
  Kt.browserStreamBuilder = mS;
});
var rl = {};
Ar(rl, { Server: () => xe, Socket: () => xe, Stream: () => xe, _createServerHandle: () => xe, _normalizeArgs: () => xe, _setSimultaneousAccepts: () => xe, connect: () => xe, createConnection: () => xe, createServer: () => xe, default: () => vS, isIP: () => xe, isIPv4: () => xe, isIPv6: () => xe });
function xe() {
  throw new Error("Node.js net module is not supported by JSPM core outside of Node.js");
}
var vS;
var il = Ae(() => {
  _();
  v();
  m();
  vS = { _createServerHandle: xe, _normalizeArgs: xe, _setSimultaneousAccepts: xe, connect: xe, createConnection: xe, createServer: xe, isIP: xe, isIPv4: xe, isIPv6: xe, Server: xe, Socket: xe, Stream: xe };
});
var nl = O((IF, _g) => {
  _();
  v();
  m();
  _g.exports = {};
});
var ol = O((Mi) => {
  "use strict";
  _();
  v();
  m();
  var sl = Mi && Mi.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Mi, "__esModule", { value: true });
  var ES = sl((il(), G(rl))), SS = sl(nt()), AS = sl(nl()), IS = (0, SS.default)("mqttjs:tcp"), TS = (t, e) => {
    if (e.port = e.port || 1883, e.hostname = e.hostname || e.host || "localhost", e.socksProxy) return (0, AS.default)(e.hostname, e.port, e.socksProxy, { timeout: e.socksTimeout });
    let { port: r, path: i } = e, n = e.hostname;
    return IS("port %d and host %s", r, n), ES.default.createConnection({ port: r, host: n, path: i });
  };
  Mi.default = TS;
});
var mg = {};
Ar(mg, { default: () => RS });
var RS;
var vg = Ae(() => {
  _();
  v();
  m();
  RS = {};
});
var ll = O((Li) => {
  "use strict";
  _();
  v();
  m();
  var al = Li && Li.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(Li, "__esModule", { value: true });
  var Eg = (vg(), G(mg)), CS = al((il(), G(rl))), kS = al(nt()), PS = al(nl()), BS = (0, kS.default)("mqttjs:tls");
  function xS(t) {
    let { host: e, port: r, socksProxy: i, ...n } = t;
    if (i !== void 0) {
      let o = (0, PS.default)(e, r, i, { timeout: t.socksTimeout });
      return (0, Eg.connect)({ ...n, socket: o });
    }
    return (0, Eg.connect)(t);
  }
  var OS = (t, e) => {
    e.port = e.port || 8883, e.host = e.hostname || e.host || "localhost", CS.default.isIP(e.host) === 0 && (e.servername = e.host), e.rejectUnauthorized = e.rejectUnauthorized !== false, delete e.path, BS("port %d host %s rejectUnauthorized %b", e.port, e.host, e.rejectUnauthorized);
    let r = xS(e);
    r.on("secureConnect", () => {
      e.rejectUnauthorized && !r.authorized ? r.emit("error", new Error("TLS not authorized")) : r.removeListener("error", i);
    });
    function i(n) {
      e.rejectUnauthorized && t.emit("error", n), r.end();
    }
    return r.on("error", i), r;
  };
  Li.default = OS;
});
var fl = O((cl) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(cl, "__esModule", { value: true });
  var Sg = (he(), G(be)), MS = Nt(), LS = is(), dt, ul, Oe;
  function qS() {
    let t = new MS.Transform();
    return t._write = (e, r, i) => {
      dt.send({ data: e.buffer, success() {
        i();
      }, fail(n) {
        i(new Error(n));
      } });
    }, t._flush = (e) => {
      dt.close({ success() {
        e();
      } });
    }, t;
  }
  function US(t) {
    t.hostname || (t.hostname = "localhost"), t.path || (t.path = "/"), t.wsOptions || (t.wsOptions = {});
  }
  function NS(t, e) {
    let r = t.protocol === "wxs" ? "wss" : "ws", i = `${r}://${t.hostname}${t.path}`;
    return t.port && t.port !== 80 && t.port !== 443 && (i = `${r}://${t.hostname}:${t.port}${t.path}`), typeof t.transformWsUrl == "function" && (i = t.transformWsUrl(i, t, e)), i;
  }
  function DS() {
    dt.onOpen(() => {
      Oe.socketReady();
    }), dt.onMessage((t) => {
      let { data: e } = t;
      e instanceof ArrayBuffer ? e = Sg.Buffer.from(e) : e = Sg.Buffer.from(e, "utf8"), ul.push(e);
    }), dt.onClose(() => {
      Oe.emit("close"), Oe.end(), Oe.destroy();
    }), dt.onError((t) => {
      let e = new Error(t.errMsg);
      Oe.destroy(e);
    });
  }
  var jS = (t, e) => {
    if (e.hostname = e.hostname || e.host, !e.hostname) throw new Error("Could not determine host. Specify host manually.");
    let r = e.protocolId === "MQIsdp" && e.protocolVersion === 3 ? "mqttv3.1" : "mqtt";
    US(e);
    let i = NS(e, t);
    dt = wx.connectSocket({ url: i, protocols: [r] }), ul = qS(), Oe = new LS.BufferedDuplex(e, ul, dt), Oe._destroy = (o, s) => {
      dt.close({ success() {
        s && s(o);
      } });
    };
    let n = Oe.destroy;
    return Oe.destroy = (o, s) => (Oe.destroy = n, setTimeout(() => {
      dt.close({ fail() {
        Oe._destroy(o, s);
      } });
    }, 0), Oe), DS(), Oe;
  };
  cl.default = jS;
});
var pl = O((dl) => {
  "use strict";
  _();
  v();
  m();
  Object.defineProperty(dl, "__esModule", { value: true });
  var hl = (he(), G(be)), FS = Nt(), WS = is(), Ct, qi, ri, Ag = false;
  function $S() {
    let t = new FS.Transform();
    return t._write = (e, r, i) => {
      Ct.sendSocketMessage({ data: e.buffer, success() {
        i();
      }, fail() {
        i(new Error());
      } });
    }, t._flush = (e) => {
      Ct.closeSocket({ success() {
        e();
      } });
    }, t;
  }
  function HS(t) {
    t.hostname || (t.hostname = "localhost"), t.path || (t.path = "/"), t.wsOptions || (t.wsOptions = {});
  }
  function VS(t, e) {
    let r = t.protocol === "alis" ? "wss" : "ws", i = `${r}://${t.hostname}${t.path}`;
    return t.port && t.port !== 80 && t.port !== 443 && (i = `${r}://${t.hostname}:${t.port}${t.path}`), typeof t.transformWsUrl == "function" && (i = t.transformWsUrl(i, t, e)), i;
  }
  function zS() {
    Ag || (Ag = true, Ct.onSocketOpen(() => {
      ri.socketReady();
    }), Ct.onSocketMessage((t) => {
      if (typeof t.data == "string") {
        let e = hl.Buffer.from(t.data, "base64");
        qi.push(e);
      } else {
        let e = new FileReader();
        e.addEventListener("load", () => {
          if (e.result instanceof ArrayBuffer) {
            qi.push(hl.Buffer.from(e.result));
            return;
          }
          qi.push(hl.Buffer.from(e.result, "utf-8"));
        }), e.readAsArrayBuffer(t.data);
      }
    }), Ct.onSocketClose(() => {
      ri.end(), ri.destroy();
    }), Ct.onSocketError((t) => {
      ri.destroy(t);
    }));
  }
  var KS = (t, e) => {
    if (e.hostname = e.hostname || e.host, !e.hostname) throw new Error("Could not determine host. Specify host manually.");
    let r = e.protocolId === "MQIsdp" && e.protocolVersion === 3 ? "mqttv3.1" : "mqtt";
    HS(e);
    let i = VS(e, t);
    return Ct = e.my, Ct.connectSocket({ url: i, protocols: r }), qi = $S(), ri = new WS.BufferedDuplex(e, qi, Ct), zS(), ri;
  };
  dl.default = KS;
});
var Cg = O((ii) => {
  "use strict";
  _();
  v();
  m();
  var ss = ii && ii.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(ii, "__esModule", { value: true });
  ii.connectAsync = XS;
  var QS = ss(nt()), GS = ss((fg(), G(cg))), YS = ss(ts()), Ig = ss(Ri());
  typeof (R == null ? void 0 : R.nextTick) != "function" && (R.nextTick = setImmediate);
  var Tg = (0, QS.default)("mqttjs"), ge = null;
  function JS(t) {
    let e;
    if (t.auth) if (e = t.auth.match(/^(.+):(.+)$/), e) {
      let [, r, i] = e;
      t.username = r, t.password = i;
    } else t.username = t.auth;
  }
  function Rg(t, e) {
    var _a, _b, _c;
    if (Tg("connecting to an MQTT broker..."), typeof t == "object" && !e && (e = t, t = ""), e = e || {}, t && typeof t == "string") {
      let n = GS.default.parse(t, true), o = {};
      if (n.port != null && (o.port = Number(n.port)), o.host = n.hostname, o.query = n.query, o.auth = n.auth, o.protocol = n.protocol, o.path = n.path, e = { ...o, ...e }, !e.protocol) throw new Error("Missing protocol");
      e.protocol = e.protocol.replace(/:$/, "");
    }
    if (e.unixSocket = e.unixSocket || ((_a = e.protocol) == null ? void 0 : _a.includes("+unix")), e.unixSocket ? e.protocol = e.protocol.replace("+unix", "") : !((_b = e.protocol) == null ? void 0 : _b.startsWith("ws")) && !((_c = e.protocol) == null ? void 0 : _c.startsWith("wx")) && delete e.path, JS(e), e.query && typeof e.query.clientId == "string" && (e.clientId = e.query.clientId), Ig.default || e.unixSocket ? e.socksProxy = void 0 : e.socksProxy === void 0 && typeof R < "u" && (e.socksProxy = R.env.MQTTJS_SOCKS_PROXY), e.cert && e.key) if (e.protocol) {
      if (["mqtts", "wss", "wxs", "alis"].indexOf(e.protocol) === -1) switch (e.protocol) {
        case "mqtt":
          e.protocol = "mqtts";
          break;
        case "ws":
          e.protocol = "wss";
          break;
        case "wx":
          e.protocol = "wxs";
          break;
        case "ali":
          e.protocol = "alis";
          break;
        default:
          throw new Error(`Unknown protocol for secure connection: "${e.protocol}"!`);
      }
    } else throw new Error("Missing secure protocol key");
    if (ge || (ge = {}, !Ig.default && !e.forceNativeWebSocket ? (ge.ws = Oi().streamBuilder, ge.wss = Oi().streamBuilder, ge.mqtt = ol().default, ge.tcp = ol().default, ge.ssl = ll().default, ge.tls = ge.ssl, ge.mqtts = ll().default) : (ge.ws = Oi().browserStreamBuilder, ge.wss = Oi().browserStreamBuilder, ge.wx = fl().default, ge.wxs = fl().default, ge.ali = pl().default, ge.alis = pl().default)), !ge[e.protocol]) {
      let n = ["mqtts", "wss"].indexOf(e.protocol) !== -1;
      e.protocol = ["mqtt", "mqtts", "ws", "wss", "wx", "wxs", "ali", "alis"].filter((o, s) => n && s % 2 === 0 ? false : typeof ge[o] == "function")[0];
    }
    if (e.clean === false && !e.clientId) throw new Error("Missing clientId for unclean clients");
    e.protocol && (e.defaultProtocol = e.protocol);
    function r(n) {
      return e.servers && ((!n._reconnectCount || n._reconnectCount === e.servers.length) && (n._reconnectCount = 0), e.host = e.servers[n._reconnectCount].host, e.port = e.servers[n._reconnectCount].port, e.protocol = e.servers[n._reconnectCount].protocol ? e.servers[n._reconnectCount].protocol : e.defaultProtocol, e.hostname = e.host, n._reconnectCount++), Tg("calling streambuilder for", e.protocol), ge[e.protocol](n, e);
    }
    let i = new YS.default(r, e);
    return i.on("error", () => {
    }), i;
  }
  function XS(t, e, r = true) {
    return new Promise((i, n) => {
      let o = Rg(t, e), s = { connect: (u) => {
        a(), i(o);
      }, end: () => {
        a(), i(o);
      }, error: (u) => {
        a(), o.end(), n(u);
      } };
      r === false && (s.close = () => {
        s.error(new Error("Couldn't connect to server"));
      });
      function a() {
        Object.keys(s).forEach((u) => {
          o.off(u, s[u]);
        });
      }
      Object.keys(s).forEach((u) => {
        o.on(u, s[u]);
      });
    });
  }
  ii.default = Rg;
});
var gl = O((K) => {
  "use strict";
  _();
  v();
  m();
  var kg = K && K.__createBinding || (Object.create ? function(t, e, r, i) {
    i === void 0 && (i = r);
    var n = Object.getOwnPropertyDescriptor(e, r);
    (!n || ("get" in n ? !e.__esModule : n.writable || n.configurable)) && (n = { enumerable: true, get: function() {
      return e[r];
    } }), Object.defineProperty(t, i, n);
  } : function(t, e, r, i) {
    i === void 0 && (i = r), t[i] = e[r];
  }), ZS = K && K.__setModuleDefault || (Object.create ? function(t, e) {
    Object.defineProperty(t, "default", { enumerable: true, value: e });
  } : function(t, e) {
    t.default = e;
  }), eA = K && K.__importStar || /* @__PURE__ */ function() {
    var t = function(e) {
      return t = Object.getOwnPropertyNames || function(r) {
        var i = [];
        for (var n in r) Object.prototype.hasOwnProperty.call(r, n) && (i[i.length] = n);
        return i;
      }, t(e);
    };
    return function(e) {
      if (e && e.__esModule) return e;
      var r = {};
      if (e != null) for (var i = t(e), n = 0; n < i.length; n++) i[n] !== "default" && kg(r, e, i[n]);
      return ZS(r, e), r;
    };
  }(), Pg = K && K.__exportStar || function(t, e) {
    for (var r in t) r !== "default" && !Object.prototype.hasOwnProperty.call(e, r) && kg(e, t, r);
  }, Ui = K && K.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(K, "__esModule", { value: true });
  K.ReasonCodes = K.KeepaliveManager = K.UniqueMessageIdProvider = K.DefaultMessageIdProvider = K.Store = K.MqttClient = K.connectAsync = K.connect = K.Client = void 0;
  var Bg = Ui(ts());
  K.MqttClient = Bg.default;
  var tA = Ui(ka());
  K.DefaultMessageIdProvider = tA.default;
  var rA = Ui(jp());
  K.UniqueMessageIdProvider = rA.default;
  var iA = Ui(Wo());
  K.Store = iA.default;
  var xg = eA(Cg());
  K.connect = xg.default;
  Object.defineProperty(K, "connectAsync", { enumerable: true, get: function() {
    return xg.connectAsync;
  } });
  var nA = Ui(Ma());
  K.KeepaliveManager = nA.default;
  K.Client = Bg.default;
  Pg(ts(), K);
  Pg(cr(), K);
  var sA = Ei();
  Object.defineProperty(K, "ReasonCodes", { enumerable: true, get: function() {
    return sA.ReasonCodes;
  } });
});
var cA = O((je) => {
  _();
  v();
  m();
  var Og = je && je.__createBinding || (Object.create ? function(t, e, r, i) {
    i === void 0 && (i = r);
    var n = Object.getOwnPropertyDescriptor(e, r);
    (!n || ("get" in n ? !e.__esModule : n.writable || n.configurable)) && (n = { enumerable: true, get: function() {
      return e[r];
    } }), Object.defineProperty(t, i, n);
  } : function(t, e, r, i) {
    i === void 0 && (i = r), t[i] = e[r];
  }), oA = je && je.__setModuleDefault || (Object.create ? function(t, e) {
    Object.defineProperty(t, "default", { enumerable: true, value: e });
  } : function(t, e) {
    t.default = e;
  }), aA = je && je.__importStar || /* @__PURE__ */ function() {
    var t = function(e) {
      return t = Object.getOwnPropertyNames || function(r) {
        var i = [];
        for (var n in r) Object.prototype.hasOwnProperty.call(r, n) && (i[i.length] = n);
        return i;
      }, t(e);
    };
    return function(e) {
      if (e && e.__esModule) return e;
      var r = {};
      if (e != null) for (var i = t(e), n = 0; n < i.length; n++) i[n] !== "default" && Og(r, e, i[n]);
      return oA(r, e), r;
    };
  }(), lA = je && je.__exportStar || function(t, e) {
    for (var r in t) r !== "default" && !Object.prototype.hasOwnProperty.call(e, r) && Og(e, t, r);
  };
  Object.defineProperty(je, "__esModule", { value: true });
  var uA = aA(gl());
  je.default = uA;
  lA(gl(), je);
});
var mqtt_esm_default = cA();
export {
  mqtt_esm_default as default
};
/*! Bundled license information:

mqtt/dist/mqtt.esm.js:
  (*! Bundled license information:
  
  @jspm/core/nodelibs/browser/buffer.js:
    (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)
  
  safe-buffer/index.js:
    (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)
  *)
*/
//# sourceMappingURL=mqtt.js.map
